// API Response and Request Types
import type { TimeConflict } from './course'
import type { NotificationPreferences } from './notification'
import type { Course, Schedule } from './course'

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
  meta?: {
    page?: number
    per_page?: number
    total?: number
    total_pages?: number
  }
}

export interface ApiError {
  code: string
  message: string
  field?: string
  details?: Record<string, any>
}

export interface PaginationParams {
  page?: number
  per_page?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface FilterParams {
  search?: string
  semester_id?: string
  status?: string
  type?: string
  date_from?: string
  date_to?: string
}

// Authentication Types
export interface LoginRequest {
  email: string
  password: string
  remember_me?: boolean
}

export interface LoginResponse {
  token: string
  refresh_token: string
  expires_in: number
  user: {
    id: string
    email: string
    name: string
    role: 'student' | 'admin' | 'faculty'
  }
}

export interface RefreshTokenRequest {
  refresh_token: string
}

// Course Registration Types
export interface CourseRegistrationRequest {
  course_ids: string[]
  semester_id: string
}

export interface CourseRegistrationResponse {
  successful_registrations: string[]
  failed_registrations: {
    course_id: string
    error: string
  }[]
  conflicts: TimeConflict[]
}

// Profile Update Types
export interface ProfileUpdateRequest {
  name?: string
  phone?: string
  address?: string
  emergency_contact?: {
    name: string
    phone: string
    relationship: string
  }
  notification_preferences?: NotificationPreferences
}

// Schedule Types
export interface ScheduleRequest {
  semester_id?: string
  include_exams?: boolean
  format?: 'weekly' | 'daily' | 'calendar'
}

export interface ScheduleResponse {
  semester: {
    id: string
    name: string
    start_date: string
    end_date: string
  }
  courses: CourseSchedule[]
  exams: ExamSchedule[]
}

export interface CourseSchedule {
  course: Course
  schedule: Schedule[]
}

export interface ExamSchedule {
  id: string
  course_id: string
  course_code: string
  course_name: string
  exam_type: 'midterm' | 'final' | 'quiz'
  date: string
  start_time: string
  end_time: string
  location: string
  duration_minutes: number
  instructions?: string
}
