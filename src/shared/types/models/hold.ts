// Academic and Administrative Holds Types
export type HoldSeverity = 'low' | 'medium' | 'high' | 'critical'
export type HoldType = 'financial' | 'academic' | 'administrative' | 'disciplinary' | 'health'

export interface Hold {
  id: string
  type: HoldType
  title: string
  description: string
  severity: HoldSeverity
  resolution_steps: string[]
  deadline?: string
  contact_info?: {
    name: string
    email: string
    phone?: string
    office?: string
  }
  created_at: string
  updated_at?: string
}

export interface HoldResolution {
  hold_id: string
  resolution_date: string
  resolved_by: string
  notes?: string
}
