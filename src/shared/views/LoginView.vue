<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/shared/stores/auth'
import {
  type AuthCodeFlowErrorResponse,
  type AuthCodeFlowSuccessResponse,
  useTokenClient,
} from 'vue3-google-signin'
import logoLogin from '@/assets/logo-login.png'
import bannerLogin from '@/assets/banner-login.jpg'
import { Button } from '@/shared/components/ui/button'
import IconGoogle from '@/shared/components/icons/IconGoogle.vue'

const router = useRouter()
const authStore = useAuthStore()

const error = ref('')
const successMessage = ref('')
const googleLoading = ref(false)

const handleGoogleLoginSuccess = async (access_token: string) => {
  error.value = ''
  successMessage.value = ''
  googleLoading.value = true

  try {
    const result = await authStore.loginWithGoogle(access_token)
    if (result.success) {
      successMessage.value = result.message || 'Google login successful'
      const redirect = router.currentRoute.value.query.redirect as string
      router.push(redirect || '/dashboard')
    } else {
      error.value = result.error || 'Google authentication failed'
    }
  } catch (err) {
    console.error('Google login error:', err)
    error.value = 'An unexpected error occurred during Google authentication'
  } finally {
    googleLoading.value = false
  }
}

const handleOnSuccess = (response: AuthCodeFlowSuccessResponse) => {
  handleGoogleLoginSuccess(response.access_token)
}

const handleOnError = (errorResponse: AuthCodeFlowErrorResponse) => {
  error.value = errorResponse.error || 'An unexpected error occurred during Google authentication'
}

const { isReady, login } = useTokenClient({
  onSuccess: handleOnSuccess,
  onError: handleOnError,
  // other options
})
</script>

<template>
  <div class="min-h-screen relative">
    <!-- Banner Image Background -->
    <div class="absolute inset-0">
      <img :src="bannerLogin" alt="Swinburne Student" class="w-full h-full object-cover" />
    </div>

    <!-- Login Form Overlay -->
    <div class="absolute inset-0 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div class="w-full max-w-md">
        <!-- Login Card -->
        <div class="bg-white backdrop-blur-sm rounded-2xl shadow-2xl p-8">
          <!-- Logo -->
          <div class="flex justify-center mb-6">
            <img :src="logoLogin" alt="Swinburne University of Technology" class="h-52 w-auto" />
          </div>

          <!-- Success/Error Messages -->
          <div
            v-if="successMessage"
            class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg"
          >
            {{ successMessage }}
          </div>
          <div
            v-if="error"
            class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg"
          >
            {{ error }}
          </div>

          <!-- Google Sign-In Button -->
          <div class="space-y-6 mb-6">
            <Button :disabled="!isReady" @click="() => login()" class="w-full">
              <IconGoogle class="size-6 mr-1" />
              Login with Google
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
