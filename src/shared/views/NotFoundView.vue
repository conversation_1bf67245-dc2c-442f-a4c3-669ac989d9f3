<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Button } from '@/shared/components/ui/button'
import { useAuthStore } from '@/shared/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const goToDashboard = () => {
  // Redirect to the appropriate dashboard based on user role
  const dashboardName = authStore.isStudent ? 'student-dashboard' : 'lecturer-dashboard'
  router.push({ name: dashboardName })
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-gray-50">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- 404 Number -->
      <div class="mb-8">
        <h1 class="text-9xl font-bold text-gray-200 select-none">404</h1>
      </div>

      <!-- Main Content -->
      <div class="space-y-6">
        <div class="space-y-2">
          <h2 class="text-3xl font-bold text-gray-900">Page Not Found</h2>
          <p class="text-lg text-gray-600 max-w-sm mx-auto">
            The page you're looking for doesn't exist or has been moved.
          </p>
        </div>

        <!-- Action Button -->
        <div class="pt-6">
          <Button @click="goToDashboard" class="px-8 py-3 text-base font-medium">
            Return to Dashboard
          </Button>
        </div>

        <!-- Additional Info -->
        <div class="pt-4">
          <p class="text-sm text-gray-500">
            If you believe this is an error, please contact support.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
