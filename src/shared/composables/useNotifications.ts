import { computed, onMounted, onUnmounted } from 'vue'
import { useNotificationStore } from '@/shared/stores/notification'
import type { NotificationType } from '@/shared/types/models/notification'

export const useNotifications = () => {
  const store = useNotificationStore()

  // Request notification permission
  const requestPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }

  // Filter helpers
  const filterByCategory = (category: 'all' | NotificationType) => {
    store.setFilter('category', category)
  }

  const filterByReadStatus = (status: 'all' | 'read' | 'unread') => {
    store.setFilter('read_status', status)
  }

  // Computed helpers
  const hasUnreadNotifications = computed(() => store.unreadCount > 0)
  const hasCriticalNotifications = computed(() => store.criticalNotifications.length > 0)

  // Pull to refresh functionality
  const refreshNotifications = async () => {
    await store.fetchNotifications(true)
  }

  // Initialize on mount
  onMounted(async () => {
    await store.initialize()

    // Request notification permission if not already granted
    if ('Notification' in window && Notification.permission === 'default') {
      await requestPermission()
    }
  })

  // Cleanup on unmount
  onUnmounted(() => {
    store.cleanup()
  })

  return {
    // Store state
    notifications: store.notifications,
    allNotifications: store.allNotifications,
    filters: store.filters,
    isLoading: store.isLoading,
    error: store.error,
    lastUpdated: store.lastUpdated,
    isConnected: store.isConnected,

    // Computed
    unreadCount: store.unreadCount,
    criticalNotifications: store.criticalNotifications,
    notificationsByCategory: store.notificationsByCategory,
    hasUnreadNotifications,
    hasCriticalNotifications,

    // Actions
    markAsRead: store.markAsRead,
    markAsUnread: store.markAsUnread,
    markAllAsRead: store.markAllAsRead,
    removeNotification: store.removeNotification,
    clearError: store.clearError,
    refreshNotifications,

    // Filtering
    filterByCategory,
    filterByReadStatus,

    // Permissions
    requestPermission,
  }
}
