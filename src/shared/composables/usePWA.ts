import { ref, onMounted, onUnmounted } from 'vue'

interface PWAInstallPrompt {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

interface NotificationOptions {
  title: string
  body?: string
  icon?: string
  badge?: string
  tag?: string
  data?: any
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
}

export function usePWA() {
  // State
  const isInstallable = ref(false)
  const isInstalled = ref(false)
  const isOnline = ref(navigator.onLine)
  const isUpdateAvailable = ref(false)
  const notificationPermission = ref<NotificationPermission>('default')
  
  // Service Worker
  const swRegistration = ref<ServiceWorkerRegistration | null>(null)
  const installPrompt = ref<PWAInstallPrompt | null>(null)

  // Install PWA
  const installPWA = async (): Promise<boolean> => {
    if (!installPrompt.value) {
      console.warn('PWA install prompt not available')
      return false
    }

    try {
      await installPrompt.value.prompt()
      const choiceResult = await installPrompt.value.userChoice
      
      if (choiceResult.outcome === 'accepted') {
        console.log('PWA installation accepted')
        isInstalled.value = true
        isInstallable.value = false
        installPrompt.value = null
        return true
      } else {
        console.log('PWA installation dismissed')
        return false
      }
    } catch (error) {
      console.error('PWA installation failed:', error)
      return false
    }
  }

  // Service Worker registration
  const registerServiceWorker = async (): Promise<boolean> => {
    if (!('serviceWorker' in navigator)) {
      console.warn('Service Worker not supported')
      return false
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })

      swRegistration.value = registration
      console.log('Service Worker registered successfully')

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              isUpdateAvailable.value = true
              console.log('New service worker version available')
            }
          })
        }
      })

      // Check for existing updates
      if (registration.waiting) {
        isUpdateAvailable.value = true
      }

      return true
    } catch (error) {
      console.error('Service Worker registration failed:', error)
      return false
    }
  }

  // Update service worker
  const updateServiceWorker = async (): Promise<void> => {
    if (!swRegistration.value?.waiting) {
      console.warn('No service worker update available')
      return
    }

    // Tell the waiting service worker to skip waiting
    swRegistration.value.waiting.postMessage({ type: 'SKIP_WAITING' })
    
    // Reload the page to activate the new service worker
    window.location.reload()
  }

  // Notification management
  const requestNotificationPermission = async (): Promise<NotificationPermission> => {
    if (!('Notification' in window)) {
      console.warn('Notifications not supported')
      return 'denied'
    }

    if (Notification.permission === 'granted') {
      notificationPermission.value = 'granted'
      return 'granted'
    }

    try {
      const permission = await Notification.requestPermission()
      notificationPermission.value = permission
      return permission
    } catch (error) {
      console.error('Notification permission request failed:', error)
      notificationPermission.value = 'denied'
      return 'denied'
    }
  }

  // Show notification
  const showNotification = async (options: NotificationOptions): Promise<boolean> => {
    if (notificationPermission.value !== 'granted') {
      const permission = await requestNotificationPermission()
      if (permission !== 'granted') {
        return false
      }
    }

    try {
      if (swRegistration.value) {
        // Use service worker to show notification (better for PWA)
        await swRegistration.value.showNotification(options.title, {
          body: options.body,
          icon: options.icon || '/icons/icon-192x192.png',
          badge: options.badge || '/icons/badge-72x72.png',
          tag: options.tag,
          data: options.data,
          actions: options.actions,
          vibrate: [100, 50, 100],
          requireInteraction: false
        })
      } else {
        // Fallback to regular notification
        new Notification(options.title, {
          body: options.body,
          icon: options.icon || '/icons/icon-192x192.png',
          tag: options.tag,
          data: options.data
        })
      }
      return true
    } catch (error) {
      console.error('Failed to show notification:', error)
      return false
    }
  }

  // Push notification subscription
  const subscribeToPushNotifications = async (): Promise<PushSubscription | null> => {
    if (!swRegistration.value) {
      console.warn('Service Worker not registered')
      return null
    }

    if (notificationPermission.value !== 'granted') {
      const permission = await requestNotificationPermission()
      if (permission !== 'granted') {
        return null
      }
    }

    try {
      const subscription = await swRegistration.value.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          // Replace with your VAPID public key
          'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9LdNnC_NNPJ6Ew8FqZMJQOcqiOIHHHI8YlYFgHeTSQwHLQxXGrLA'
        )
      })

      console.log('Push notification subscription successful')
      return subscription
    } catch (error) {
      console.error('Push notification subscription failed:', error)
      return null
    }
  }

  // Offline data caching
  const cacheData = async (key: string, data: any): Promise<boolean> => {
    try {
      if ('caches' in window) {
        const cache = await caches.open('dynamic-v1')
        const response = new Response(JSON.stringify(data), {
          headers: { 'Content-Type': 'application/json' }
        })
        await cache.put(`/cache/${key}`, response)
        return true
      } else {
        // Fallback to localStorage
        localStorage.setItem(`cache_${key}`, JSON.stringify(data))
        return true
      }
    } catch (error) {
      console.error('Failed to cache data:', error)
      return false
    }
  }

  // Retrieve cached data
  const getCachedData = async (key: string): Promise<any | null> => {
    try {
      if ('caches' in window) {
        const cache = await caches.open('dynamic-v1')
        const response = await cache.match(`/cache/${key}`)
        if (response) {
          return await response.json()
        }
      }
      
      // Fallback to localStorage
      const cached = localStorage.getItem(`cache_${key}`)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      console.error('Failed to retrieve cached data:', error)
      return null
    }
  }

  // Background sync
  const scheduleBackgroundSync = async (tag: string, data?: any): Promise<boolean> => {
    if (!swRegistration.value || !('sync' in window.ServiceWorkerRegistration.prototype)) {
      console.warn('Background sync not supported')
      return false
    }

    try {
      // Store data for background sync
      if (data) {
        await cacheData(`sync_${tag}`, data)
      }
      
      await swRegistration.value.sync.register(tag)
      console.log('Background sync scheduled:', tag)
      return true
    } catch (error) {
      console.error('Background sync scheduling failed:', error)
      return false
    }
  }

  // Check if app is running as PWA
  const isPWA = (): boolean => {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true ||
           document.referrer.includes('android-app://')
  }

  // Utility function for VAPID key conversion
  const urlBase64ToUint8Array = (base64String: string): Uint8Array => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  // Event listeners
  const setupEventListeners = () => {
    // Install prompt
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault()
      installPrompt.value = e as any
      isInstallable.value = true
      console.log('PWA install prompt available')
    })

    // App installed
    window.addEventListener('appinstalled', () => {
      isInstalled.value = true
      isInstallable.value = false
      installPrompt.value = null
      console.log('PWA installed successfully')
    })

    // Online/offline status
    window.addEventListener('online', () => {
      isOnline.value = true
      console.log('App is online')
    })

    window.addEventListener('offline', () => {
      isOnline.value = false
      console.log('App is offline')
    })

    // Service worker messages
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'SYNC_SUCCESS') {
          console.log('Background sync completed:', event.data.action)
        }
      })
    }
  }

  // Initialize PWA features
  const initializePWA = async () => {
    setupEventListeners()
    
    // Check if already installed
    isInstalled.value = isPWA()
    
    // Register service worker
    await registerServiceWorker()
    
    // Check notification permission
    if ('Notification' in window) {
      notificationPermission.value = Notification.permission
    }
  }

  // Cleanup
  const cleanup = () => {
    // Remove event listeners if needed
  }

  // Initialize on mount
  onMounted(() => {
    initializePWA()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    isInstallable,
    isInstalled,
    isOnline,
    isUpdateAvailable,
    notificationPermission,
    swRegistration,

    // Methods
    installPWA,
    updateServiceWorker,
    requestNotificationPermission,
    showNotification,
    subscribeToPushNotifications,
    cacheData,
    getCachedData,
    scheduleBackgroundSync,
    isPWA,

    // Utilities
    registerServiceWorker
  }
}
