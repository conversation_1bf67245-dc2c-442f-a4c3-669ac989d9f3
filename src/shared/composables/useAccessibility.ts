import { ref, onMounted, onUnmounted, nextTick } from 'vue'

interface AccessibilityOptions {
  announcePageChanges?: boolean
  trapFocus?: boolean
  enableKeyboardNavigation?: boolean
}

interface FocusTrapOptions {
  initialFocus?: string
  returnFocus?: boolean
}

export function useAccessibility(options: AccessibilityOptions = {}) {
  const {
    announcePageChanges = true,
    trapFocus = false,
    enableKeyboardNavigation = true
  } = options

  // Screen reader announcements
  const announcer = ref<HTMLElement | null>(null)
  const isReducedMotion = ref(false)
  const currentFocusedElement = ref<HTMLElement | null>(null)

  // Focus trap state
  const focusTrapActive = ref(false)
  const focusableElements = ref<HTMLElement[]>([])
  const firstFocusableElement = ref<HTMLElement | null>(null)
  const lastFocusableElement = ref<HTMLElement | null>(null)

  // Keyboard navigation state
  const keyboardNavigationEnabled = ref(enableKeyboardNavigation)

  // Initialize accessibility features
  const initializeAccessibility = () => {
    // Create screen reader announcer
    if (announcePageChanges && !announcer.value) {
      announcer.value = document.createElement('div')
      announcer.value.setAttribute('aria-live', 'polite')
      announcer.value.setAttribute('aria-atomic', 'true')
      announcer.value.setAttribute('class', 'sr-only')
      announcer.value.style.cssText = `
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
      `
      document.body.appendChild(announcer.value)
    }

    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    isReducedMotion.value = mediaQuery.matches
    
    mediaQuery.addEventListener('change', (e) => {
      isReducedMotion.value = e.matches
    })

    // Add keyboard navigation listeners
    if (keyboardNavigationEnabled.value) {
      document.addEventListener('keydown', handleKeyboardNavigation)
    }
  }

  // Announce message to screen readers
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!announcer.value) return

    announcer.value.setAttribute('aria-live', priority)
    announcer.value.textContent = message

    // Clear after announcement
    setTimeout(() => {
      if (announcer.value) {
        announcer.value.textContent = ''
      }
    }, 1000)
  }

  // Focus management
  const setFocus = (element: HTMLElement | string, options: { preventScroll?: boolean } = {}) => {
    nextTick(() => {
      let targetElement: HTMLElement | null = null

      if (typeof element === 'string') {
        targetElement = document.querySelector(element)
      } else {
        targetElement = element
      }

      if (targetElement) {
        targetElement.focus({ preventScroll: options.preventScroll })
        currentFocusedElement.value = targetElement
      }
    })
  }

  // Focus trap functionality
  const getFocusableElements = (container: HTMLElement): HTMLElement[] => {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ')

    return Array.from(container.querySelectorAll(focusableSelectors))
      .filter(el => {
        const element = el as HTMLElement
        return element.offsetWidth > 0 && element.offsetHeight > 0
      }) as HTMLElement[]
  }

  const trapFocusInContainer = (container: HTMLElement | string, options: FocusTrapOptions = {}) => {
    const containerElement = typeof container === 'string' 
      ? document.querySelector(container) as HTMLElement
      : container

    if (!containerElement) return

    focusableElements.value = getFocusableElements(containerElement)
    firstFocusableElement.value = focusableElements.value[0] || null
    lastFocusableElement.value = focusableElements.value[focusableElements.value.length - 1] || null

    focusTrapActive.value = true

    // Set initial focus
    if (options.initialFocus) {
      const initialElement = containerElement.querySelector(options.initialFocus) as HTMLElement
      if (initialElement) {
        setFocus(initialElement)
      }
    } else if (firstFocusableElement.value) {
      setFocus(firstFocusableElement.value)
    }

    // Add focus trap listener
    containerElement.addEventListener('keydown', handleFocusTrap)
  }

  const releaseFocusTrap = (returnFocus = true) => {
    focusTrapActive.value = false
    
    if (returnFocus && currentFocusedElement.value) {
      setFocus(currentFocusedElement.value)
    }

    // Remove focus trap listeners
    document.removeEventListener('keydown', handleFocusTrap)
  }

  const handleFocusTrap = (event: KeyboardEvent) => {
    if (!focusTrapActive.value || event.key !== 'Tab') return

    if (event.shiftKey) {
      // Shift + Tab (backward)
      if (document.activeElement === firstFocusableElement.value) {
        event.preventDefault()
        if (lastFocusableElement.value) {
          setFocus(lastFocusableElement.value)
        }
      }
    } else {
      // Tab (forward)
      if (document.activeElement === lastFocusableElement.value) {
        event.preventDefault()
        if (firstFocusableElement.value) {
          setFocus(firstFocusableElement.value)
        }
      }
    }
  }

  // Keyboard navigation
  const handleKeyboardNavigation = (event: KeyboardEvent) => {
    // Escape key handling
    if (event.key === 'Escape') {
      // Close modals, dropdowns, etc.
      const activeModal = document.querySelector('[role="dialog"][aria-modal="true"]')
      if (activeModal) {
        const closeButton = activeModal.querySelector('[aria-label*="close"], [aria-label*="Close"]') as HTMLElement
        if (closeButton) {
          closeButton.click()
        }
      }
    }

    // Arrow key navigation for lists and grids
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      const activeElement = document.activeElement as HTMLElement
      if (activeElement && activeElement.getAttribute('role') === 'option') {
        handleArrowKeyNavigation(event, activeElement)
      }
    }
  }

  const handleArrowKeyNavigation = (event: KeyboardEvent, currentElement: HTMLElement) => {
    const container = currentElement.closest('[role="listbox"], [role="grid"], [role="menu"]')
    if (!container) return

    const items = Array.from(container.querySelectorAll('[role="option"], [role="gridcell"], [role="menuitem"]')) as HTMLElement[]
    const currentIndex = items.indexOf(currentElement)

    let nextIndex = currentIndex

    switch (event.key) {
      case 'ArrowDown':
        nextIndex = Math.min(currentIndex + 1, items.length - 1)
        break
      case 'ArrowUp':
        nextIndex = Math.max(currentIndex - 1, 0)
        break
      case 'ArrowRight':
        if (container.getAttribute('role') === 'grid') {
          // Handle grid navigation
          nextIndex = Math.min(currentIndex + 1, items.length - 1)
        }
        break
      case 'ArrowLeft':
        if (container.getAttribute('role') === 'grid') {
          // Handle grid navigation
          nextIndex = Math.max(currentIndex - 1, 0)
        }
        break
    }

    if (nextIndex !== currentIndex) {
      event.preventDefault()
      setFocus(items[nextIndex])
    }
  }

  // Skip links functionality
  const addSkipLink = (targetId: string, label = 'Skip to main content') => {
    const skipLink = document.createElement('a')
    skipLink.href = `#${targetId}`
    skipLink.textContent = label
    skipLink.className = 'skip-link'
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: #fff;
      padding: 8px;
      text-decoration: none;
      z-index: 9999;
      border-radius: 4px;
    `

    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px'
    })

    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px'
    })

    skipLink.addEventListener('click', (e) => {
      e.preventDefault()
      const target = document.getElementById(targetId)
      if (target) {
        target.setAttribute('tabindex', '-1')
        setFocus(target)
        target.addEventListener('blur', () => {
          target.removeAttribute('tabindex')
        }, { once: true })
      }
    })

    document.body.insertBefore(skipLink, document.body.firstChild)
  }

  // High contrast mode detection
  const isHighContrast = ref(false)
  const checkHighContrast = () => {
    // Check for Windows high contrast mode
    const testElement = document.createElement('div')
    testElement.style.cssText = `
      border: 1px solid;
      border-color: ButtonText;
      position: absolute;
      top: -999px;
    `
    document.body.appendChild(testElement)
    
    const computedStyle = window.getComputedStyle(testElement)
    isHighContrast.value = computedStyle.borderTopColor !== computedStyle.borderRightColor
    
    document.body.removeChild(testElement)
  }

  // Cleanup
  const cleanup = () => {
    if (announcer.value) {
      document.body.removeChild(announcer.value)
      announcer.value = null
    }

    document.removeEventListener('keydown', handleKeyboardNavigation)
    releaseFocusTrap(false)
  }

  // Initialize on mount
  onMounted(() => {
    initializeAccessibility()
    checkHighContrast()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    isReducedMotion,
    isHighContrast,
    focusTrapActive,
    keyboardNavigationEnabled,

    // Methods
    announce,
    setFocus,
    trapFocusInContainer,
    releaseFocusTrap,
    addSkipLink,
    checkHighContrast,

    // Utilities
    getFocusableElements
  }
}
