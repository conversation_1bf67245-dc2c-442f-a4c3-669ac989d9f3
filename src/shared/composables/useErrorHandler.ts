import { ref, inject, onErrorCaptured } from 'vue'

export interface ErrorInfo {
  message: string
  code?: string | number
  type: 'network' | 'validation' | 'authentication' | 'authorization' | 'server' | 'client' | 'unknown'
  timestamp: Date
  context?: Record<string, any>
  stack?: string
}

export interface ErrorHandlerOptions {
  showToast?: boolean
  logToConsole?: boolean
  reportToService?: boolean
  fallbackMessage?: string
  retryable?: boolean
}

export function useErrorHandler(options: ErrorHandlerOptions = {}) {
  const {
    showToast = true,
    logToConsole = true,
    reportToService = false,
    fallbackMessage = 'An unexpected error occurred',
    retryable = false
  } = options

  // State
  const errors = ref<ErrorInfo[]>([])
  const lastError = ref<ErrorInfo | null>(null)
  const isRetrying = ref(false)

  // Try to get toast service from injection
  const toast = inject('toast', null) as any

  // Error classification
  const classifyError = (error: Error | any): ErrorInfo['type'] => {
    if (!error) return 'unknown'

    // Network errors
    if (error.name === 'NetworkError' || 
        error.message?.includes('fetch') ||
        error.message?.includes('network') ||
        error.code === 'NETWORK_ERROR') {
      return 'network'
    }

    // HTTP status codes
    if (error.status || error.response?.status) {
      const status = error.status || error.response?.status
      if (status === 401) return 'authentication'
      if (status === 403) return 'authorization'
      if (status >= 400 && status < 500) return 'client'
      if (status >= 500) return 'server'
    }

    // Validation errors
    if (error.name === 'ValidationError' ||
        error.message?.includes('validation') ||
        error.code === 'VALIDATION_ERROR') {
      return 'validation'
    }

    return 'unknown'
  }

  // Create error info object
  const createErrorInfo = (error: Error | any, context?: Record<string, any>): ErrorInfo => {
    return {
      message: error.message || error.toString() || fallbackMessage,
      code: error.code || error.status || error.response?.status,
      type: classifyError(error),
      timestamp: new Date(),
      context: {
        ...context,
        url: window.location.href,
        userAgent: navigator.userAgent,
        ...error.context
      },
      stack: error.stack
    }
  }

  // Handle error
  const handleError = async (
    error: Error | any, 
    context?: Record<string, any>,
    customOptions?: Partial<ErrorHandlerOptions>
  ): Promise<void> => {
    const mergedOptions = { ...options, ...customOptions }
    const errorInfo = createErrorInfo(error, context)

    // Store error
    errors.value.push(errorInfo)
    lastError.value = errorInfo

    // Log to console
    if (mergedOptions.logToConsole) {
      console.error('Error handled:', errorInfo)
      if (errorInfo.stack) {
        console.error('Stack trace:', errorInfo.stack)
      }
    }

    // Show toast notification
    if (mergedOptions.showToast && toast) {
      const toastMessage = getUserFriendlyMessage(errorInfo)
      
      toast.error(
        'Error',
        toastMessage,
        {
          persistent: errorInfo.type === 'network' || errorInfo.type === 'server',
          action: mergedOptions.retryable ? {
            label: 'Retry',
            handler: () => {
              // Emit retry event or call retry function
              console.log('Retry requested for error:', errorInfo)
            }
          } : undefined
        }
      )
    }

    // Report to error service
    if (mergedOptions.reportToService) {
      await reportError(errorInfo)
    }
  }

  // Get user-friendly error message
  const getUserFriendlyMessage = (errorInfo: ErrorInfo): string => {
    switch (errorInfo.type) {
      case 'network':
        return 'Unable to connect to the server. Please check your internet connection.'
      case 'authentication':
        return 'Your session has expired. Please log in again.'
      case 'authorization':
        return 'You do not have permission to perform this action.'
      case 'validation':
        return errorInfo.message || 'Please check your input and try again.'
      case 'server':
        return 'A server error occurred. Please try again later.'
      case 'client':
        return errorInfo.message || 'There was a problem with your request.'
      default:
        return errorInfo.message || fallbackMessage
    }
  }

  // Report error to external service
  const reportError = async (errorInfo: ErrorInfo): Promise<void> => {
    try {
      // In a real application, this would send to an error reporting service
      // like Sentry, LogRocket, or a custom endpoint
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorInfo)
      })
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }

  // Async error handler with retry logic
  const handleAsyncError = async <T>(
    asyncFn: () => Promise<T>,
    context?: Record<string, any>,
    maxRetries = 3,
    retryDelay = 1000
  ): Promise<T | null> => {
    let lastError: Error | null = null
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        isRetrying.value = attempt > 0
        return await asyncFn()
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxRetries) {
          await handleError(error, {
            ...context,
            attempt: attempt + 1,
            maxRetries
          })
          break
        }
        
        // Wait before retry
        if (retryDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)))
        }
      }
    }
    
    isRetrying.value = false
    return null
  }

  // Clear errors
  const clearErrors = () => {
    errors.value = []
    lastError.value = null
  }

  // Clear specific error
  const clearError = (index: number) => {
    if (index >= 0 && index < errors.value.length) {
      errors.value.splice(index, 1)
      if (errors.value.length === 0) {
        lastError.value = null
      }
    }
  }

  // Get errors by type
  const getErrorsByType = (type: ErrorInfo['type']) => {
    return errors.value.filter(error => error.type === type)
  }

  // Check if has specific error type
  const hasErrorType = (type: ErrorInfo['type']) => {
    return errors.value.some(error => error.type === type)
  }

  // Capture Vue component errors
  onErrorCaptured((error: Error, instance: any, info: string) => {
    handleError(error, {
      component: instance?.$options?.name || 'Unknown',
      errorInfo: info
    })
    
    // Return false to prevent the error from propagating further
    return false
  })

  // Global error handlers
  const setupGlobalErrorHandlers = () => {
    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      handleError(event.reason, {
        type: 'unhandledRejection',
        promise: event.promise
      })
    })

    // Global JavaScript errors
    window.addEventListener('error', (event) => {
      handleError(new Error(event.message), {
        type: 'globalError',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      })
    })
  }

  return {
    // State
    errors,
    lastError,
    isRetrying,

    // Methods
    handleError,
    handleAsyncError,
    clearErrors,
    clearError,
    getErrorsByType,
    hasErrorType,
    setupGlobalErrorHandlers,

    // Utilities
    classifyError,
    createErrorInfo,
    getUserFriendlyMessage
  }
}
