import { ref, onMounted, onUnmounted } from 'vue'

interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  
  // Navigation Timing
  domContentLoaded?: number
  loadComplete?: number
  firstPaint?: number
  firstContentfulPaint?: number
  
  // Resource Timing
  totalResources?: number
  totalResourceSize?: number
  slowestResource?: {
    name: string
    duration: number
    size: number
  }
  
  // Memory Usage
  usedJSHeapSize?: number
  totalJSHeapSize?: number
  jsHeapSizeLimit?: number
  
  // Custom Metrics
  routeChangeTime?: number
  apiResponseTimes?: Record<string, number[]>
  componentRenderTimes?: Record<string, number>
}

interface PerformanceThresholds {
  lcp: number // Good: < 2.5s
  fid: number // Good: < 100ms
  cls: number // Good: < 0.1
  domContentLoaded: number // Good: < 1.5s
  loadComplete: number // Good: < 3s
}

export function usePerformance() {
  // State
  const metrics = ref<PerformanceMetrics>({})
  const isSupported = ref(false)
  const observer = ref<PerformanceObserver | null>(null)
  
  // Thresholds for performance scoring
  const thresholds: PerformanceThresholds = {
    lcp: 2500, // 2.5 seconds
    fid: 100,  // 100 milliseconds
    cls: 0.1,  // 0.1 score
    domContentLoaded: 1500, // 1.5 seconds
    loadComplete: 3000 // 3 seconds
  }

  // Check if Performance API is supported
  const checkSupport = () => {
    isSupported.value = !!(
      window.performance &&
      window.performance.mark &&
      window.performance.measure &&
      window.PerformanceObserver
    )
  }

  // Measure Core Web Vitals
  const measureCoreWebVitals = () => {
    if (!isSupported.value) return

    try {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        metrics.value.lcp = lastEntry.startTime
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          metrics.value.fid = entry.processingStart - entry.startTime
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        metrics.value.cls = clsValue
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })

    } catch (error) {
      console.warn('Error measuring Core Web Vitals:', error)
    }
  }

  // Measure Navigation Timing
  const measureNavigationTiming = () => {
    if (!window.performance.timing) return

    const timing = window.performance.timing
    const navigationStart = timing.navigationStart

    metrics.value.domContentLoaded = timing.domContentLoadedEventEnd - navigationStart
    metrics.value.loadComplete = timing.loadEventEnd - navigationStart

    // Paint Timing
    if (window.performance.getEntriesByType) {
      const paintEntries = window.performance.getEntriesByType('paint')
      paintEntries.forEach((entry: any) => {
        if (entry.name === 'first-paint') {
          metrics.value.firstPaint = entry.startTime
        } else if (entry.name === 'first-contentful-paint') {
          metrics.value.firstContentfulPaint = entry.startTime
        }
      })
    }
  }

  // Measure Resource Performance
  const measureResourceTiming = () => {
    if (!window.performance.getEntriesByType) return

    const resources = window.performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    
    metrics.value.totalResources = resources.length
    metrics.value.totalResourceSize = resources.reduce((total, resource) => {
      return total + (resource.transferSize || 0)
    }, 0)

    // Find slowest resource
    let slowestResource = { name: '', duration: 0, size: 0 }
    resources.forEach((resource) => {
      const duration = resource.responseEnd - resource.startTime
      if (duration > slowestResource.duration) {
        slowestResource = {
          name: resource.name,
          duration,
          size: resource.transferSize || 0
        }
      }
    })
    metrics.value.slowestResource = slowestResource
  }

  // Measure Memory Usage
  const measureMemoryUsage = () => {
    if ('memory' in window.performance) {
      const memory = (window.performance as any).memory
      metrics.value.usedJSHeapSize = memory.usedJSHeapSize
      metrics.value.totalJSHeapSize = memory.totalJSHeapSize
      metrics.value.jsHeapSizeLimit = memory.jsHeapSizeLimit
    }
  }

  // Mark performance events
  const mark = (name: string) => {
    if (isSupported.value) {
      window.performance.mark(name)
    }
  }

  // Measure time between marks
  const measure = (name: string, startMark: string, endMark?: string) => {
    if (isSupported.value) {
      try {
        if (endMark) {
          window.performance.measure(name, startMark, endMark)
        } else {
          window.performance.measure(name, startMark)
        }
        
        const measures = window.performance.getEntriesByName(name, 'measure')
        return measures[measures.length - 1]?.duration || 0
      } catch (error) {
        console.warn('Error measuring performance:', error)
        return 0
      }
    }
    return 0
  }

  // Measure route change performance
  const measureRouteChange = (routeName: string) => {
    const startMark = `route-start-${routeName}`
    const endMark = `route-end-${routeName}`
    
    mark(startMark)
    
    return {
      end: () => {
        mark(endMark)
        const duration = measure(`route-${routeName}`, startMark, endMark)
        metrics.value.routeChangeTime = duration
        return duration
      }
    }
  }

  // Measure API response times
  const measureApiCall = (endpoint: string) => {
    const startTime = performance.now()
    
    return {
      end: () => {
        const duration = performance.now() - startTime
        
        if (!metrics.value.apiResponseTimes) {
          metrics.value.apiResponseTimes = {}
        }
        
        if (!metrics.value.apiResponseTimes[endpoint]) {
          metrics.value.apiResponseTimes[endpoint] = []
        }
        
        metrics.value.apiResponseTimes[endpoint].push(duration)
        return duration
      }
    }
  }

  // Measure component render time
  const measureComponentRender = (componentName: string) => {
    const startTime = performance.now()
    
    return {
      end: () => {
        const duration = performance.now() - startTime
        
        if (!metrics.value.componentRenderTimes) {
          metrics.value.componentRenderTimes = {}
        }
        
        metrics.value.componentRenderTimes[componentName] = duration
        return duration
      }
    }
  }

  // Get performance score (0-100)
  const getPerformanceScore = (): number => {
    let score = 100
    let factors = 0

    // LCP scoring
    if (metrics.value.lcp !== undefined) {
      factors++
      if (metrics.value.lcp > thresholds.lcp * 2) score -= 30
      else if (metrics.value.lcp > thresholds.lcp) score -= 15
    }

    // FID scoring
    if (metrics.value.fid !== undefined) {
      factors++
      if (metrics.value.fid > thresholds.fid * 3) score -= 25
      else if (metrics.value.fid > thresholds.fid) score -= 10
    }

    // CLS scoring
    if (metrics.value.cls !== undefined) {
      factors++
      if (metrics.value.cls > thresholds.cls * 2.5) score -= 25
      else if (metrics.value.cls > thresholds.cls) score -= 10
    }

    // Load time scoring
    if (metrics.value.loadComplete !== undefined) {
      factors++
      if (metrics.value.loadComplete > thresholds.loadComplete * 2) score -= 20
      else if (metrics.value.loadComplete > thresholds.loadComplete) score -= 10
    }

    return Math.max(0, Math.min(100, score))
  }

  // Get performance grade
  const getPerformanceGrade = (): 'A' | 'B' | 'C' | 'D' | 'F' => {
    const score = getPerformanceScore()
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }

  // Get recommendations
  const getRecommendations = (): string[] => {
    const recommendations: string[] = []

    if (metrics.value.lcp && metrics.value.lcp > thresholds.lcp) {
      recommendations.push('Optimize Largest Contentful Paint by reducing server response times and optimizing critical resources')
    }

    if (metrics.value.fid && metrics.value.fid > thresholds.fid) {
      recommendations.push('Reduce First Input Delay by minimizing JavaScript execution time and using web workers')
    }

    if (metrics.value.cls && metrics.value.cls > thresholds.cls) {
      recommendations.push('Improve Cumulative Layout Shift by setting dimensions for images and avoiding dynamic content insertion')
    }

    if (metrics.value.totalResourceSize && metrics.value.totalResourceSize > 2000000) { // 2MB
      recommendations.push('Reduce total resource size by compressing images and minifying CSS/JavaScript')
    }

    if (metrics.value.slowestResource && metrics.value.slowestResource.duration > 1000) {
      recommendations.push(`Optimize slow resource: ${metrics.value.slowestResource.name}`)
    }

    return recommendations
  }

  // Report performance data
  const reportPerformance = async (endpoint?: string) => {
    const report = {
      metrics: metrics.value,
      score: getPerformanceScore(),
      grade: getPerformanceGrade(),
      recommendations: getRecommendations(),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    if (endpoint) {
      try {
        await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(report)
        })
      } catch (error) {
        console.warn('Failed to report performance data:', error)
      }
    }

    return report
  }

  // Initialize performance monitoring
  const initializeMonitoring = () => {
    checkSupport()
    
    if (!isSupported.value) {
      console.warn('Performance API not supported')
      return
    }

    measureCoreWebVitals()
    
    // Measure navigation timing after page load
    if (document.readyState === 'complete') {
      measureNavigationTiming()
      measureResourceTiming()
      measureMemoryUsage()
    } else {
      window.addEventListener('load', () => {
        setTimeout(() => {
          measureNavigationTiming()
          measureResourceTiming()
          measureMemoryUsage()
        }, 0)
      })
    }
  }

  // Cleanup
  const cleanup = () => {
    if (observer.value) {
      observer.value.disconnect()
    }
  }

  // Initialize on mount
  onMounted(() => {
    initializeMonitoring()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    metrics,
    isSupported,

    // Methods
    mark,
    measure,
    measureRouteChange,
    measureApiCall,
    measureComponentRender,
    getPerformanceScore,
    getPerformanceGrade,
    getRecommendations,
    reportPerformance,

    // Utilities
    initializeMonitoring,
    cleanup
  }
}
