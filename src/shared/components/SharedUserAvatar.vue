<script setup lang="ts">
import { useAuthStore } from '@/shared/stores/auth'
import {
  LogOut,
  Settings,
  UserCircle,
  HelpCircle,
  BookOpen,
  GraduationCap,
  AlertTriangle,
  Shield,
} from 'lucide-vue-next'

import { Avatar, AvatarFallback, AvatarImage } from '@/shared/components/ui/avatar'
import { Badge } from '@/shared/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu'

import { useRouter } from 'vue-router'
import { computed } from 'vue'

const authStore = useAuthStore()
const router = useRouter()

const user = computed(() => authStore.user)

// Computed properties for user information display
const userDisplayName = computed(() => {
  return user.value?.full_name || user.value?.name || 'Student'
})

const userInitials = computed(() => {
  const name = userDisplayName.value
  return (
    name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2) || 'ST'
  )
})

const enrollmentStatusColor = computed(() => {
  const status = user.value?.enrollment_status?.toLowerCase()
  switch (status) {
    case 'active':
      return 'bg-green-500'
    case 'inactive':
      return 'bg-red-500'
    case 'suspended':
      return 'bg-yellow-500'
    default:
      return 'bg-gray-500'
  }
})

const handleNavigation = (path: string) => {
  router.push(path)
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout error:', error)
    router.push('/login')
  }
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <div class="relative cursor-pointer" role="button" aria-label="User menu" tabindex="0">
        <Avatar class="h-8 w-8">
          <AvatarImage
            :src="user?.avatar_url || '/placeholder.svg'"
            :alt="`${userDisplayName} avatar`"
          />
          <AvatarFallback>{{ userInitials }}</AvatarFallback>
        </Avatar>
        <!-- Status indicator -->
        <div
          v-if="user?.enrollment_status"
          :class="[
            'absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full border-2 border-background',
            enrollmentStatusColor,
          ]"
          :title="`Status: ${user.enrollment_status}`"
        />
      </div>
    </DropdownMenuTrigger>
    <DropdownMenuContent class="w-64" align="end">
      <!-- User Information Header -->
      <DropdownMenuLabel class="font-normal">
        <div class="flex flex-col space-y-2">
          <div class="flex items-center gap-3">
            <Avatar class="h-10 w-10">
              <AvatarImage
                :src="user?.avatar_url || '/placeholder.svg'"
                :alt="`${userDisplayName} avatar`"
              />
              <AvatarFallback>{{ userInitials }}</AvatarFallback>
            </Avatar>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium leading-none truncate">{{ userDisplayName }}</p>
              <p class="text-xs leading-none text-muted-foreground truncate mt-1">
                {{ user?.email || '<EMAIL>' }}
              </p>
              <div class="flex items-center gap-2 mt-1">
                <p v-if="user?.student_id" class="text-xs text-muted-foreground">
                  ID: {{ user.student_id }}
                </p>
                <Badge v-if="user?.enrollment_status" variant="secondary" class="text-xs px-1 py-0">
                  {{ user.enrollment_status }}
                </Badge>
              </div>
            </div>
          </div>

          <!-- Academic Information -->
          <div v-if="user?.program || user?.campus" class="text-xs text-muted-foreground space-y-1">
            <p v-if="user.program">
              <GraduationCap class="inline h-3 w-3 mr-1" />
              {{ user.program.name }}
            </p>
            <p v-if="user.campus">
              <Shield class="inline h-3 w-3 mr-1" />
              {{ user.campus.name }}
            </p>
          </div>
        </div>
      </DropdownMenuLabel>

      <DropdownMenuSeparator />

      <!-- Quick Actions -->
      <DropdownMenuItem @click="handleNavigation('/profile')" role="menuitem">
        <UserCircle class="mr-2 h-4 w-4" />
        <span>My Profile</span>
      </DropdownMenuItem>

      <DropdownMenuItem @click="handleNavigation('/courses')" role="menuitem">
        <BookOpen class="mr-2 h-4 w-4" />
        <span>My Courses</span>
      </DropdownMenuItem>

      <DropdownMenuItem @click="handleNavigation('/grades')" role="menuitem">
        <GraduationCap class="mr-2 h-4 w-4" />
        <span>Grades</span>
      </DropdownMenuItem>

      <DropdownMenuItem
        v-if="user?.has_active_holds"
        @click="handleNavigation('/holds')"
        role="menuitem"
        class="text-amber-600"
      >
        <AlertTriangle class="mr-2 h-4 w-4" />
        <span>Academic Holds</span>
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      <!-- Settings and Support -->
      <DropdownMenuItem @click="handleNavigation('/settings')" role="menuitem">
        <Settings class="mr-2 h-4 w-4" />
        <span>Settings</span>
      </DropdownMenuItem>

      <DropdownMenuItem role="menuitem">
        <HelpCircle class="mr-2 h-4 w-4" />
        <span>Help & Support</span>
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      <!-- Logout -->
      <DropdownMenuItem
        class="text-red-600 focus:text-red-600"
        @click="handleLogout"
        role="menuitem"
      >
        <LogOut class="mr-2 h-4 w-4" />
        <span>Log out</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
