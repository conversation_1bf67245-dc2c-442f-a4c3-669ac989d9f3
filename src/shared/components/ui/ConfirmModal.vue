<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Button } from './button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './dialog'
import { 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle,
  Trash2,
  Save,
  X
} from 'lucide-vue-next'

interface Props {
  open: boolean
  title: string
  description?: string
  type?: 'danger' | 'warning' | 'info' | 'success'
  confirmText?: string
  cancelText?: string
  icon?: string | 'alert' | 'info' | 'success' | 'error' | 'delete' | 'save'
  loading?: boolean
  persistent?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  confirmText: 'Confirm',
  cancelText: 'Cancel',
  icon: 'alert',
  loading: false,
  persistent: false,
})

const emit = defineEmits<{
  'update:open': [value: boolean]
  confirm: []
  cancel: []
}>()

const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const iconComponent = computed(() => {
  switch (props.icon) {
    case 'alert': return AlertTriangle
    case 'info': return Info
    case 'success': return CheckCircle
    case 'error': return XCircle
    case 'delete': return Trash2
    case 'save': return Save
    default: return AlertTriangle
  }
})

const typeClasses = computed(() => {
  switch (props.type) {
    case 'danger': return {
      icon: 'text-red-600 bg-red-100',
      confirmButton: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
    }
    case 'warning': return {
      icon: 'text-yellow-600 bg-yellow-100',
      confirmButton: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
    }
    case 'success': return {
      icon: 'text-green-600 bg-green-100',
      confirmButton: 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
    }
    case 'info': return {
      icon: 'text-blue-600 bg-blue-100',
      confirmButton: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
    }
    default: return {
      icon: 'text-blue-600 bg-blue-100',
      confirmButton: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
    }
  }
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  if (!props.persistent) {
    emit('cancel')
    isOpen.value = false
  }
}

const handleOpenChange = (open: boolean) => {
  if (!open && !props.persistent) {
    emit('cancel')
  }
  isOpen.value = open
}

// Keyboard event handling
const handleKeydown = (event: KeyboardEvent) => {
  if (!isOpen.value) return

  if (event.key === 'Enter' && !props.loading) {
    event.preventDefault()
    handleConfirm()
  } else if (event.key === 'Escape' && !props.persistent) {
    event.preventDefault()
    handleCancel()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <Dialog :open="isOpen" @update:open="handleOpenChange">
    <DialogContent 
      class="sm:max-w-md"
      :class="{ 'pointer-events-none': loading }"
      @escape-key-down="!persistent && handleCancel()"
      @pointer-down-outside="!persistent && handleCancel()"
    >
      <DialogHeader>
        <div class="flex items-center gap-4">
          <!-- Icon -->
          <div :class="['flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full', typeClasses.icon]">
            <component 
              :is="iconComponent" 
              class="h-6 w-6"
              aria-hidden="true"
            />
          </div>
          
          <!-- Title and Description -->
          <div class="flex-1">
            <DialogTitle class="text-left">
              {{ title }}
            </DialogTitle>
            <DialogDescription v-if="description" class="text-left mt-2">
              {{ description }}
            </DialogDescription>
          </div>
        </div>
      </DialogHeader>

      <!-- Custom Content Slot -->
      <div v-if="$slots.default" class="py-4">
        <slot />
      </div>

      <DialogFooter class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
        <!-- Cancel Button -->
        <Button
          variant="outline"
          @click="handleCancel"
          :disabled="loading || persistent"
          class="mt-3 sm:mt-0"
        >
          {{ cancelText }}
        </Button>
        
        <!-- Confirm Button -->
        <Button
          @click="handleConfirm"
          :disabled="loading"
          :class="[
            'text-white focus:ring-2 focus:ring-offset-2',
            typeClasses.confirmButton
          ]"
        >
          <svg
            v-if="loading"
            class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          {{ loading ? 'Processing...' : confirmText }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
