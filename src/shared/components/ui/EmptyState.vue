<script setup lang="ts">
import { computed } from 'vue'
import { Button } from './button'
import { 
  FileX, 
  Search, 
  Plus, 
  RefreshCw,
  AlertCircle,
  BookOpen,
  Users,
  Calendar,
  Mail
} from 'lucide-vue-next'

interface Props {
  icon?: string | 'file' | 'search' | 'plus' | 'refresh' | 'alert' | 'book' | 'users' | 'calendar' | 'mail'
  title: string
  description?: string
  actionText?: string
  secondaryActionText?: string
  illustration?: boolean
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'file',
  size: 'md',
  illustration: false,
})

const emit = defineEmits<{
  action: []
  secondaryAction: []
}>()

const iconComponent = computed(() => {
  switch (props.icon) {
    case 'file': return FileX
    case 'search': return Search
    case 'plus': return Plus
    case 'refresh': return RefreshCw
    case 'alert': return AlertCircle
    case 'book': return BookOpen
    case 'users': return Users
    case 'calendar': return Calendar
    case 'mail': return Mail
    default: return FileX
  }
})

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm': return {
      container: 'py-8',
      icon: 'h-8 w-8',
      title: 'text-lg',
      description: 'text-sm',
      spacing: 'space-y-3'
    }
    case 'md': return {
      container: 'py-12',
      icon: 'h-12 w-12',
      title: 'text-xl',
      description: 'text-base',
      spacing: 'space-y-4'
    }
    case 'lg': return {
      container: 'py-16',
      icon: 'h-16 w-16',
      title: 'text-2xl',
      description: 'text-lg',
      spacing: 'space-y-6'
    }
    default: return {
      container: 'py-12',
      icon: 'h-12 w-12',
      title: 'text-xl',
      description: 'text-base',
      spacing: 'space-y-4'
    }
  }
})

const handleAction = () => {
  emit('action')
}

const handleSecondaryAction = () => {
  emit('secondaryAction')
}
</script>

<template>
  <div :class="['text-center', sizeClasses.container]">
    <div :class="['flex flex-col items-center', sizeClasses.spacing]">
      <!-- Illustration or Icon -->
      <div v-if="illustration" class="mb-4">
        <!-- Simple SVG illustration -->
        <svg
          :class="['mx-auto text-muted-foreground', sizeClasses.icon]"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      </div>
      
      <div v-else class="mb-4">
        <div class="mx-auto flex items-center justify-center rounded-full bg-muted p-3">
          <component 
            :is="iconComponent" 
            :class="['text-muted-foreground', sizeClasses.icon]"
            aria-hidden="true"
          />
        </div>
      </div>

      <!-- Content -->
      <div class="max-w-md mx-auto">
        <h3 :class="['font-semibold text-foreground', sizeClasses.title]">
          {{ title }}
        </h3>
        
        <p 
          v-if="description" 
          :class="['mt-2 text-muted-foreground', sizeClasses.description]"
        >
          {{ description }}
        </p>
      </div>

      <!-- Actions -->
      <div v-if="actionText || secondaryActionText" class="flex flex-col sm:flex-row gap-3 mt-6">
        <Button
          v-if="actionText"
          @click="handleAction"
          class="inline-flex items-center"
        >
          <component 
            :is="iconComponent" 
            class="h-4 w-4 mr-2"
            aria-hidden="true"
          />
          {{ actionText }}
        </Button>
        
        <Button
          v-if="secondaryActionText"
          variant="outline"
          @click="handleSecondaryAction"
        >
          {{ secondaryActionText }}
        </Button>
      </div>

      <!-- Slot for custom content -->
      <div v-if="$slots.default" class="mt-4">
        <slot />
      </div>
    </div>
  </div>
</template>
