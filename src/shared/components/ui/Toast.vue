<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { Button } from './button'
import { 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  Info,
  X
} from 'lucide-vue-next'

export interface ToastProps {
  id: string
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  description?: string
  duration?: number
  persistent?: boolean
  action?: {
    label: string
    handler: () => void
  }
}

const props = withDefaults(defineProps<ToastProps>(), {
  type: 'info',
  duration: 5000,
  persistent: false,
})

const emit = defineEmits<{
  close: [id: string]
}>()

const isVisible = ref(false)
const isLeaving = ref(false)

const iconComponent = computed(() => {
  switch (props.type) {
    case 'success': return CheckCircle
    case 'error': return XCircle
    case 'warning': return AlertCircle
    case 'info': return Info
    default: return Info
  }
})

const typeClasses = computed(() => {
  switch (props.type) {
    case 'success': return {
      container: 'bg-green-50 border-green-200',
      icon: 'text-green-600',
      title: 'text-green-800',
      description: 'text-green-700',
      closeButton: 'text-green-500 hover:text-green-600'
    }
    case 'error': return {
      container: 'bg-red-50 border-red-200',
      icon: 'text-red-600',
      title: 'text-red-800',
      description: 'text-red-700',
      closeButton: 'text-red-500 hover:text-red-600'
    }
    case 'warning': return {
      container: 'bg-yellow-50 border-yellow-200',
      icon: 'text-yellow-600',
      title: 'text-yellow-800',
      description: 'text-yellow-700',
      closeButton: 'text-yellow-500 hover:text-yellow-600'
    }
    case 'info': return {
      container: 'bg-blue-50 border-blue-200',
      icon: 'text-blue-600',
      title: 'text-blue-800',
      description: 'text-blue-700',
      closeButton: 'text-blue-500 hover:text-blue-600'
    }
    default: return {
      container: 'bg-blue-50 border-blue-200',
      icon: 'text-blue-600',
      title: 'text-blue-800',
      description: 'text-blue-700',
      closeButton: 'text-blue-500 hover:text-blue-600'
    }
  }
})

const close = () => {
  isLeaving.value = true
  setTimeout(() => {
    emit('close', props.id)
  }, 300) // Match animation duration
}

const handleAction = () => {
  if (props.action) {
    props.action.handler()
    close()
  }
}

onMounted(() => {
  // Trigger enter animation
  setTimeout(() => {
    isVisible.value = true
  }, 10)

  // Auto-close if not persistent
  if (!props.persistent && props.duration > 0) {
    setTimeout(() => {
      close()
    }, props.duration)
  }
})
</script>

<template>
  <div
    :class="[
      'pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg border shadow-lg transition-all duration-300 ease-in-out',
      typeClasses.container,
      isVisible && !isLeaving ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
    ]"
    role="alert"
    aria-live="polite"
    :aria-labelledby="`toast-title-${id}`"
    :aria-describedby="description ? `toast-desc-${id}` : undefined"
  >
    <div class="p-4">
      <div class="flex items-start">
        <!-- Icon -->
        <div class="flex-shrink-0">
          <component 
            :is="iconComponent" 
            :class="['h-5 w-5', typeClasses.icon]"
            aria-hidden="true"
          />
        </div>
        
        <!-- Content -->
        <div class="ml-3 w-0 flex-1">
          <p 
            :id="`toast-title-${id}`"
            :class="['text-sm font-medium', typeClasses.title]"
          >
            {{ title }}
          </p>
          
          <p 
            v-if="description"
            :id="`toast-desc-${id}`"
            :class="['mt-1 text-sm', typeClasses.description]"
          >
            {{ description }}
          </p>
          
          <!-- Action Button -->
          <div v-if="action" class="mt-3">
            <Button
              size="sm"
              variant="outline"
              @click="handleAction"
              :class="[
                'text-xs',
                type === 'success' ? 'border-green-300 text-green-700 hover:bg-green-100' :
                type === 'error' ? 'border-red-300 text-red-700 hover:bg-red-100' :
                type === 'warning' ? 'border-yellow-300 text-yellow-700 hover:bg-yellow-100' :
                'border-blue-300 text-blue-700 hover:bg-blue-100'
              ]"
            >
              {{ action.label }}
            </Button>
          </div>
        </div>
        
        <!-- Close Button -->
        <div class="ml-4 flex flex-shrink-0">
          <Button
            variant="ghost"
            size="sm"
            @click="close"
            :class="['inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2', typeClasses.closeButton]"
            :aria-label="`Close ${title} notification`"
          >
            <X class="h-4 w-4" aria-hidden="true" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
