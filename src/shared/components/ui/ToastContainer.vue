<script setup lang="ts">
import { ref, computed } from 'vue'
import Toast, { type ToastProps } from './Toast.vue'

interface ToastItem extends ToastProps {
  id: string
}

interface Props {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
  maxToasts?: number
}

const props = withDefaults(defineProps<Props>(), {
  position: 'top-right',
  maxToasts: 5,
})

const toasts = ref<ToastItem[]>([])

const positionClasses = computed(() => {
  switch (props.position) {
    case 'top-right': return 'top-4 right-4'
    case 'top-left': return 'top-4 left-4'
    case 'bottom-right': return 'bottom-4 right-4'
    case 'bottom-left': return 'bottom-4 left-4'
    case 'top-center': return 'top-4 left-1/2 transform -translate-x-1/2'
    case 'bottom-center': return 'bottom-4 left-1/2 transform -translate-x-1/2'
    default: return 'top-4 right-4'
  }
})

const addToast = (toast: Omit<ToastProps, 'id'>) => {
  const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  const newToast: ToastItem = { ...toast, id }
  
  toasts.value.push(newToast)
  
  // Remove oldest toast if we exceed max
  if (toasts.value.length > props.maxToasts) {
    toasts.value.shift()
  }
  
  return id
}

const removeToast = (id: string) => {
  const index = toasts.value.findIndex(toast => toast.id === id)
  if (index > -1) {
    toasts.value.splice(index, 1)
  }
}

const clearAllToasts = () => {
  toasts.value = []
}

// Convenience methods
const success = (title: string, description?: string, options?: Partial<ToastProps>) => {
  return addToast({ type: 'success', title, description, ...options })
}

const error = (title: string, description?: string, options?: Partial<ToastProps>) => {
  return addToast({ type: 'error', title, description, ...options })
}

const warning = (title: string, description?: string, options?: Partial<ToastProps>) => {
  return addToast({ type: 'warning', title, description, ...options })
}

const info = (title: string, description?: string, options?: Partial<ToastProps>) => {
  return addToast({ type: 'info', title, description, ...options })
}

// Expose methods for parent components
defineExpose({
  addToast,
  removeToast,
  clearAllToasts,
  success,
  error,
  warning,
  info
})
</script>

<template>
  <div
    :class="[
      'pointer-events-none fixed z-50 flex flex-col gap-2',
      positionClasses
    ]"
    aria-live="polite"
    aria-label="Notifications"
  >
    <TransitionGroup
      name="toast"
      tag="div"
      class="flex flex-col gap-2"
    >
      <Toast
        v-for="toast in toasts"
        :key="toast.id"
        v-bind="toast"
        @close="removeToast"
      />
    </TransitionGroup>
  </div>
</template>

<style scoped>
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}
</style>
