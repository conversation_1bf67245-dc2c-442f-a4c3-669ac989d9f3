<script setup lang="ts">
import { ref, onErrorCaptured, provide, inject } from 'vue'
import { Button } from './button'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug,
  Home,
  ChevronDown,
  ChevronUp
} from 'lucide-vue-next'

interface Props {
  fallback?: 'card' | 'page' | 'inline'
  showDetails?: boolean
  showReload?: boolean
  showHome?: boolean
  onError?: (error: Error, instance: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  fallback: 'card',
  showDetails: false,
  showReload: true,
  showHome: false,
})

const hasError = ref(false)
const error = ref<Error | null>(null)
const errorInfo = ref<any>(null)
const showErrorDetails = ref(false)

// Error boundary logic
onErrorCaptured((err: Error, instance: any, info: string) => {
  console.error('ErrorBoundary caught an error:', err)
  console.error('Error info:', info)
  console.error('Component instance:', instance)

  hasError.value = true
  error.value = err
  errorInfo.value = { instance, info }

  // Call custom error handler if provided
  if (props.onError) {
    props.onError(err, instance)
  }

  // Prevent the error from propagating further
  return false
})

// Provide error state to child components
provide('errorBoundary', {
  hasError,
  error,
  reset: () => {
    hasError.value = false
    error.value = null
    errorInfo.value = null
  }
})

const resetError = () => {
  hasError.value = false
  error.value = null
  errorInfo.value = null
}

const reloadPage = () => {
  window.location.reload()
}

const goHome = () => {
  window.location.href = '/'
}

const toggleDetails = () => {
  showErrorDetails.value = !showErrorDetails.value
}

const formatError = (err: Error) => {
  return {
    name: err.name,
    message: err.message,
    stack: err.stack?.split('\n').slice(0, 10).join('\n') // Limit stack trace
  }
}
</script>

<template>
  <!-- Error State -->
  <div v-if="hasError">
    <!-- Page-level Error -->
    <div v-if="fallback === 'page'" class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div class="text-center">
          <AlertTriangle class="mx-auto h-16 w-16 text-red-500" />
          <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
            Something went wrong
          </h2>
          <p class="mt-2 text-sm text-gray-600">
            We're sorry, but something unexpected happened. Please try again.
          </p>
        </div>
        
        <div class="space-y-4">
          <Button
            v-if="showReload"
            @click="reloadPage"
            class="w-full flex justify-center items-center"
          >
            <RefreshCw class="h-4 w-4 mr-2" />
            Reload Page
          </Button>
          
          <Button
            v-if="showHome"
            @click="goHome"
            variant="outline"
            class="w-full flex justify-center items-center"
          >
            <Home class="h-4 w-4 mr-2" />
            Go Home
          </Button>
          
          <Button
            @click="resetError"
            variant="outline"
            class="w-full"
          >
            Try Again
          </Button>
        </div>
        
        <!-- Error Details -->
        <div v-if="showDetails && error" class="mt-6">
          <Button
            @click="toggleDetails"
            variant="ghost"
            size="sm"
            class="w-full flex items-center justify-center text-gray-500"
          >
            <Bug class="h-4 w-4 mr-2" />
            {{ showErrorDetails ? 'Hide' : 'Show' }} Error Details
            <component :is="showErrorDetails ? ChevronUp : ChevronDown" class="h-4 w-4 ml-2" />
          </Button>
          
          <div v-if="showErrorDetails" class="mt-4 p-4 bg-gray-100 rounded-lg">
            <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ formatError(error) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- Card-level Error -->
    <Card v-else-if="fallback === 'card'" class="border-red-200 bg-red-50">
      <CardHeader>
        <CardTitle class="flex items-center text-red-800">
          <AlertTriangle class="h-5 w-5 mr-2" />
          Error Occurred
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p class="text-red-700 mb-4">
          Something went wrong while loading this component.
        </p>
        
        <div class="flex flex-wrap gap-2">
          <Button
            @click="resetError"
            size="sm"
            variant="outline"
            class="border-red-300 text-red-700 hover:bg-red-100"
          >
            Try Again
          </Button>
          
          <Button
            v-if="showReload"
            @click="reloadPage"
            size="sm"
            variant="outline"
            class="border-red-300 text-red-700 hover:bg-red-100"
          >
            <RefreshCw class="h-3 w-3 mr-1" />
            Reload
          </Button>
        </div>
        
        <!-- Error Details -->
        <div v-if="showDetails && error" class="mt-4">
          <Button
            @click="toggleDetails"
            variant="ghost"
            size="sm"
            class="text-red-600 hover:text-red-700"
          >
            <Bug class="h-3 w-3 mr-1" />
            {{ showErrorDetails ? 'Hide' : 'Show' }} Details
            <component :is="showErrorDetails ? ChevronUp : ChevronDown" class="h-3 w-3 ml-1" />
          </Button>
          
          <div v-if="showErrorDetails" class="mt-2 p-3 bg-red-100 rounded text-xs">
            <pre class="text-red-800 whitespace-pre-wrap">{{ formatError(error) }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Inline Error -->
    <div v-else class="flex items-center justify-center p-4 text-red-600 bg-red-50 rounded-lg border border-red-200">
      <AlertTriangle class="h-4 w-4 mr-2" />
      <span class="text-sm">Error loading component</span>
      <Button
        @click="resetError"
        variant="ghost"
        size="sm"
        class="ml-2 text-red-600 hover:text-red-700"
      >
        Retry
      </Button>
    </div>
  </div>

  <!-- Normal Content -->
  <slot v-else />
</template>
