<script setup lang="ts">
import { computed, ref } from 'vue'
import { Bell, Check, X, Wifi, WifiOff } from 'lucide-vue-next'
import { useNotifications } from '@/shared/composables/useNotifications'

import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover'
import { ScrollArea } from '@/shared/components/ui/scroll-area'
import { Separator } from '@/shared/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'

const {
  notifications,
  unreadCount,
  isLoading,
  error,
  isConnected,
  markAsRead,
  markAsUnread,
  markAllAsRead,
  removeNotification,
  filterByCategory,
  refreshNotifications,
  clearError,
} = useNotifications()

const isOpen = ref(false)

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
  return `${Math.floor(diffInMinutes / 1440)}d ago`
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'critical':
      return 'bg-red-500'
    case 'high':
      return 'bg-orange-500'
    case 'medium':
      return 'bg-blue-500'
    default:
      return 'bg-gray-500'
  }
}

const getSourceAvatar = (type: string) => {
  switch (type) {
    case 'academic':
      return '🎓'
    case 'finance':
      return '💰'
    case 'system':
      return '⚙️'
    default:
      return '📢'
  }
}

// Handle pull-to-refresh
const handleRefresh = async () => {
  await refreshNotifications()
}

// Filter handlers
const handleCategoryFilter = (category: 'all' | 'academic' | 'finance' | 'system') => {
  filterByCategory(category)
}
</script>

<template>
  <Popover v-model:open="isOpen">
    <PopoverTrigger as-child>
      <Button variant="ghost" size="sm" class="relative h-9 w-9 p-0">
        <Bell class="h-4 w-4" />
        <Badge
          v-if="unreadCount > 0"
          variant="destructive"
          class="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
        >
          {{ unreadCount > 9 ? '9+' : unreadCount }}
        </Badge>
        <!-- Connection status indicator -->
        <div class="absolute -bottom-1 -right-1">
          <Wifi v-if="isConnected" class="h-2 w-2 text-green-500" />
          <WifiOff v-else class="h-2 w-2 text-red-500" />
        </div>
      </Button>
    </PopoverTrigger>

    <PopoverContent class="w-96 p-0" align="end">
      <div class="flex items-center justify-between p-4 pb-2">
        <h4 class="font-semibold">Notifications</h4>
        <div class="flex items-center gap-2">
          <Button
            v-if="unreadCount > 0"
            variant="ghost"
            size="sm"
            @click="markAllAsRead"
            class="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
          >
            Mark all as read
          </Button>
          <Button
            variant="ghost"
            size="sm"
            @click="handleRefresh"
            :disabled="isLoading"
            class="h-6 w-6 p-0"
          >
            <svg
              class="h-3 w-3"
              :class="{ 'animate-spin': isLoading }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </Button>
        </div>
      </div>

      <!-- Error display -->
      <div v-if="error" class="px-4 pb-2">
        <div
          class="flex items-center justify-between bg-red-50 border border-red-200 rounded-md p-2"
        >
          <span class="text-sm text-red-600">{{ error }}</span>
          <Button variant="ghost" size="sm" @click="clearError" class="h-4 w-4 p-0">
            <X class="h-3 w-3" />
          </Button>
        </div>
      </div>

      <Separator />

      <!-- Category tabs -->
      <Tabs default-value="all" class="w-full">
        <TabsList class="grid w-full grid-cols-4 mx-2 mt-2">
          <TabsTrigger value="all" @click="handleCategoryFilter('all')">All</TabsTrigger>
          <TabsTrigger value="academic" @click="handleCategoryFilter('academic')"
            >Academic</TabsTrigger
          >
          <TabsTrigger value="finance" @click="handleCategoryFilter('finance')"
            >Finance</TabsTrigger
          >
          <TabsTrigger value="system" @click="handleCategoryFilter('system')">System</TabsTrigger>
        </TabsList>

        <TabsContent value="all" class="mt-0">
          <ScrollArea class="h-[400px]">
            <div
              v-if="notifications.length === 0"
              class="p-4 text-center text-sm text-muted-foreground"
            >
              <div class="flex flex-col items-center gap-2">
                <Bell class="h-8 w-8 text-muted-foreground/50" />
                <span>No notifications</span>
              </div>
            </div>

            <div v-else class="space-y-1 p-1">
              <div
                v-for="notification in notifications"
                :key="notification.id"
                :class="[
                  'group relative flex gap-3 rounded-md p-3 hover:bg-accent cursor-pointer',
                  !notification.read ? 'bg-muted/50' : '',
                ]"
              >
                <!-- Priority indicator -->
                <div
                  v-if="!notification.read"
                  :class="[
                    'absolute left-2 top-4 h-2 w-2 rounded-full',
                    getPriorityColor(notification.priority),
                  ]"
                />

                <!-- Source avatar -->
                <div
                  class="flex-shrink-0 w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm"
                >
                  {{ getSourceAvatar(notification.type) }}
                </div>

                <div class="flex-1 space-y-1 min-w-0">
                  <div class="flex items-start justify-between gap-2">
                    <p class="text-sm font-medium leading-none truncate">
                      {{ notification.title }}
                    </p>
                    <div class="flex opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        v-if="!notification.read"
                        variant="ghost"
                        size="sm"
                        @click.stop="markAsRead(notification.id)"
                        class="h-6 w-6 p-0"
                        :aria-label="`Mark ${notification.title} as read`"
                      >
                        <Check class="h-3 w-3" />
                      </Button>
                      <Button
                        v-else
                        variant="ghost"
                        size="sm"
                        @click.stop="markAsUnread(notification.id)"
                        class="h-6 w-6 p-0"
                        :aria-label="`Mark ${notification.title} as unread`"
                      >
                        <Bell class="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        @click.stop="removeNotification(notification.id)"
                        class="h-6 w-6 p-0"
                        :aria-label="`Remove ${notification.title}`"
                      >
                        <X class="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <p class="text-xs text-muted-foreground line-clamp-2">
                    {{ notification.message }}
                  </p>
                  <div class="flex items-center justify-between">
                    <p class="text-xs text-muted-foreground">
                      {{ formatTimeAgo(notification.created_at) }}
                    </p>
                    <Badge
                      v-if="notification.priority === 'critical'"
                      variant="destructive"
                      class="text-xs px-1 py-0"
                    >
                      Critical
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>

      <template v-if="notifications.length > 0">
        <Separator />
        <div class="p-2">
          <Button variant="ghost" class="w-full justify-center text-sm">
            View all notifications
          </Button>
        </div>
      </template>
    </PopoverContent>
  </Popover>
</template>
