import { ref, computed, watch } from 'vue'
import { defineStore } from 'pinia'
import { use<PERSON>pi, useBaseApi } from '@/shared/composables/useBaseApi'
import type {
  Notification,
  NotificationFilters,
  NotificationType,
} from '@/shared/types/models/notification'

export const useNotificationStore = defineStore('notification', () => {
  const api = useBaseApi()

  // State
  const notifications = ref<Notification[]>([])
  const filters = ref<NotificationFilters>({
    category: 'all',
    read_status: 'all',
  })
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<string | null>(null)
  const isConnected = ref(false)
  const wsConnection = ref<WebSocket | null>(null)
  const pollingInterval = ref<NodeJS.Timeout | null>(null)

  // Computed
  const filteredNotifications = computed(() => {
    return notifications.value.filter((notification) => {
      const categoryMatch =
        filters.value.category === 'all' || notification.type === filters.value.category

      const readMatch =
        filters.value.read_status === 'all' ||
        (filters.value.read_status === 'read' && notification.read) ||
        (filters.value.read_status === 'unread' && !notification.read)

      return categoryMatch && readMatch
    })
  })

  const unreadCount = computed(() => notifications.value.filter((n) => !n.read).length)

  const criticalNotifications = computed(() =>
    notifications.value.filter((n) => n.priority === 'critical' && !n.read),
  )

  const notificationsByCategory = computed(() => ({
    academic: notifications.value.filter((n) => n.type === 'academic'),
    finance: notifications.value.filter((n) => n.type === 'finance'),
    system: notifications.value.filter((n) => n.type === 'system'),
  }))

  // Actions
  const fetchNotifications = async (refresh = false) => {
    if (isLoading.value && !refresh) return

    try {
      isLoading.value = true
      error.value = null

      const response = await api.get<{ notifications: Notification[] }>('/notifications')

      if (response.success && response.data) {
        notifications.value = response.data.notifications
        lastUpdated.value = new Date().toISOString()

        // Persist to localStorage
        persistNotifications()
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch notifications'
      console.error('Failed to fetch notifications:', err)
    } finally {
      isLoading.value = false
    }
  }

  const markAsRead = async (notificationId: string) => {
    const notification = notifications.value.find((n) => n.id === notificationId)
    if (!notification || notification.read) return

    // Optimistic update
    notification.read = true
    persistNotifications()

    try {
      await api.put(`/notifications/${notificationId}/read`)
    } catch (err) {
      // Rollback on error
      notification.read = false
      persistNotifications()
      error.value = 'Failed to mark notification as read'
      throw err
    }
  }

  const markAsUnread = async (notificationId: string) => {
    const notification = notifications.value.find((n) => n.id === notificationId)
    if (!notification || !notification.read) return

    // Optimistic update
    notification.read = false
    persistNotifications()

    try {
      await api.put(`/notifications/${notificationId}/unread`)
    } catch (err) {
      // Rollback on error
      notification.read = true
      persistNotifications()
      error.value = 'Failed to mark notification as unread'
      throw err
    }
  }

  const markAllAsRead = async () => {
    const unreadNotifications = notifications.value.filter((n) => !n.read)
    if (unreadNotifications.length === 0) return

    // Optimistic update
    unreadNotifications.forEach((n) => (n.read = true))
    persistNotifications()

    try {
      await api.put('/notifications/mark-all-read')
    } catch (err) {
      // Rollback on error
      unreadNotifications.forEach((n) => (n.read = false))
      persistNotifications()
      error.value = 'Failed to mark all notifications as read'
      throw err
    }
  }

  const removeNotification = async (notificationId: string) => {
    const index = notifications.value.findIndex((n) => n.id === notificationId)
    if (index === -1) return

    const removedNotification = notifications.value[index]

    // Optimistic update
    notifications.value.splice(index, 1)
    persistNotifications()

    try {
      await api.delete(`/notifications/${notificationId}`)
    } catch (err) {
      // Rollback on error
      notifications.value.splice(index, 0, removedNotification)
      persistNotifications()
      error.value = 'Failed to remove notification'
      throw err
    }
  }

  const setFilter = (filterType: keyof NotificationFilters, value: string) => {
    filters.value[filterType] = value as any
    persistFilters()
  }

  const clearError = () => {
    error.value = null
  }

  // Real-time updates via WebSocket
  const connectWebSocket = () => {
    if (wsConnection.value?.readyState === WebSocket.OPEN) return

    try {
      const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws/notifications'
      wsConnection.value = new WebSocket(wsUrl)

      wsConnection.value.onopen = () => {
        isConnected.value = true
        console.log('WebSocket connected for notifications')
      }

      wsConnection.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleRealtimeUpdate(data)
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      }

      wsConnection.value.onclose = () => {
        isConnected.value = false
        console.log('WebSocket disconnected, attempting to reconnect...')

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (!isConnected.value) {
            connectWebSocket()
          }
        }, 5000)
      }

      wsConnection.value.onerror = (error) => {
        console.error('WebSocket error:', error)
        isConnected.value = false
      }
    } catch (err) {
      console.error('Failed to connect WebSocket:', err)
      // Fallback to polling
      startPolling()
    }
  }

  // Fallback polling mechanism
  const startPolling = () => {
    if (pollingInterval.value) return

    pollingInterval.value = setInterval(async () => {
      if (!isConnected.value) {
        await fetchNotifications(true)
      }
    }, 30000) // Poll every 30 seconds
  }

  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value)
      pollingInterval.value = null
    }
  }

  const disconnectWebSocket = () => {
    if (wsConnection.value) {
      wsConnection.value.close()
      wsConnection.value = null
    }
    isConnected.value = false
    stopPolling()
  }

  // Handle real-time updates
  const handleRealtimeUpdate = (data: any) => {
    switch (data.type) {
      case 'new_notification':
        addNotification(data.notification)
        break
      case 'notification_read':
        updateNotificationStatus(data.notification_id, true)
        break
      case 'notification_deleted':
        removeNotificationById(data.notification_id)
        break
      default:
        console.warn('Unknown real-time update type:', data.type)
    }
  }

  const addNotification = (notification: Notification) => {
    // Check if notification already exists
    const exists = notifications.value.some((n) => n.id === notification.id)
    if (!exists) {
      notifications.value.unshift(notification)
      persistNotifications()

      // Trigger push notification for critical alerts
      if (notification.priority === 'critical') {
        triggerPushNotification(notification)
      }
    }
  }

  const updateNotificationStatus = (notificationId: string, read: boolean) => {
    const notification = notifications.value.find((n) => n.id === notificationId)
    if (notification) {
      notification.read = read
      persistNotifications()
    }
  }

  const removeNotificationById = (notificationId: string) => {
    const index = notifications.value.findIndex((n) => n.id === notificationId)
    if (index !== -1) {
      notifications.value.splice(index, 1)
      persistNotifications()
    }
  }

  // Push notification integration
  const triggerPushNotification = (notification: Notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/badge-icon.png',
        tag: notification.id,
        requireInteraction: notification.priority === 'critical',
      })
    }
  }

  // Persistence
  const persistNotifications = () => {
    try {
      localStorage.setItem('notifications', JSON.stringify(notifications.value))
      localStorage.setItem('notifications_last_updated', lastUpdated.value || '')
    } catch (err) {
      console.error('Failed to persist notifications:', err)
    }
  }

  const persistFilters = () => {
    try {
      localStorage.setItem('notification_filters', JSON.stringify(filters.value))
    } catch (err) {
      console.error('Failed to persist filters:', err)
    }
  }

  const loadPersistedData = () => {
    try {
      const savedNotifications = localStorage.getItem('notifications')
      const savedFilters = localStorage.getItem('notification_filters')
      const savedLastUpdated = localStorage.getItem('notifications_last_updated')

      if (savedNotifications) {
        notifications.value = JSON.parse(savedNotifications)
      }

      if (savedFilters) {
        filters.value = JSON.parse(savedFilters)
      }

      if (savedLastUpdated) {
        lastUpdated.value = savedLastUpdated
      }
    } catch (err) {
      console.error('Failed to load persisted data:', err)
    }
  }

  // Initialize store
  const initialize = async () => {
    loadPersistedData()
    await fetchNotifications()
    connectWebSocket()

    // Start polling as fallback
    if (!isConnected.value) {
      startPolling()
    }
  }

  // Cleanup
  const cleanup = () => {
    disconnectWebSocket()
    stopPolling()
  }

  // Watch for visibility changes to manage connections
  if (typeof document !== 'undefined') {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        stopPolling()
      } else {
        if (!isConnected.value) {
          connectWebSocket()
          startPolling()
        }
      }
    })
  }

  return {
    // State
    notifications: filteredNotifications,
    allNotifications: notifications,
    filters,
    isLoading,
    error,
    lastUpdated,
    isConnected,

    // Computed
    unreadCount,
    criticalNotifications,
    notificationsByCategory,

    // Actions
    fetchNotifications,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    removeNotification,
    setFilter,
    clearError,
    initialize,
    cleanup,

    // Real-time
    connectWebSocket,
    disconnectWebSocket,
    startPolling,
    stopPolling,
  }
})
