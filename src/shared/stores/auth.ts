import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

export interface Campus {
  id: number
  name: string
  code: string
}

export interface Program {
  id: number
  name: string
  code?: string
}

export interface Specialization {
  id: number
  name: string
}

export interface Department {
  id: number
  name: string
  code: string
}

export interface User {
  id: string
  email: string
  name: string
  role: 'student' | 'lecturer'

  // Student-specific fields
  student_id?: string
  enrollment_status?: string
  admission_date?: string
  expected_graduation_date?: string
  campus?: Campus | null
  program?: Program | null
  specialization?: Specialization | null

  // Lecturer-specific fields
  employee_id?: string
  department?: Department | null
  title?: string
  office_location?: string

  // Shared fields
  full_name?: string
  phone?: string
  date_of_birth?: string
  gender?: string
  nationality?: string
  address?: string
  avatar_url?: string
  can_register?: boolean
  has_active_holds?: boolean
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)

  // Initialize from localStorage
  const initializeAuth = () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUser = localStorage.getItem('auth_user')

    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)
    }
  }

  // Computed properties for authentication and role management
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const role = computed(() => user.value?.role || null)
  const isStudent = computed(() => role.value === 'student')
  const isLecturer = computed(() => role.value === 'lecturer')

  // API prefix based on user role
  const getApiPrefix = computed(() => {
    return role.value ? `/api/${role.value}` : '/api'
  })

  const login = async (email: string, password: string) => {
    try {
      // Simulate API call - replace with real authentication
      if (email === '<EMAIL>' && password === 'password') {
        const mockUser: User = {
          id: '1',
          email: email,
          name: 'Admin User',
          role: 'student', // Default role for mock login
        }
        const mockToken = 'mock_jwt_token_' + Date.now()

        user.value = mockUser
        token.value = mockToken

        // Persist to localStorage
        localStorage.setItem('auth_token', mockToken)
        localStorage.setItem('auth_user', JSON.stringify(mockUser))

        return { success: true }
      } else if (email === '<EMAIL>' && password === 'password') {
        // Mock lecturer login for testing
        const mockLecturer: User = {
          id: '2',
          email: email,
          name: 'Dr. Jane Smith',
          role: 'lecturer',
          employee_id: 'EMP001',
          title: 'Senior Lecturer',
          department: {
            id: 1,
            name: 'Computer Science',
            code: 'CS',
          },
          office_location: 'Building A, Room 301',
        }
        const mockToken = 'mock_jwt_token_lecturer_' + Date.now()

        user.value = mockLecturer
        token.value = mockToken

        // Persist to localStorage
        localStorage.setItem('auth_token', mockToken)
        localStorage.setItem('auth_user', JSON.stringify(mockLecturer))

        return { success: true }
      } else {
        throw new Error('Invalid credentials')
      }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Login failed' }
    }
  }

  const loginWithGoogle = async (accessToken: string, deviceName?: string) => {
    try {
      console.log('accessToken', accessToken)
      console.log('deviceName', deviceName)
      // Use environment variable or fallback to relative URL
      const apiUrl = apiBaseUrl ? `${apiBaseUrl}/auth/login/google` : '/auth/login/google'

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify({
          access_token: accessToken,
          device_name: deviceName || 'Vue Portal',
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Google login failed')
      }

      if (data.success) {
        const authToken = data.data.token
        let userInfo: User

        // Handle both student and lecturer data from API response
        if (data.data.student) {
          const studentData = data.data.student
          userInfo = {
            id: studentData.id.toString(),
            email: studentData.email,
            name: studentData.full_name,
            role: 'student',
            student_id: studentData.student_id,
            enrollment_status: studentData.enrollment_status,
            campus: studentData.campus,
            program: studentData.program,
          }
        } else if (data.data.lecturer) {
          const lecturerData = data.data.lecturer
          userInfo = {
            id: lecturerData.id.toString(),
            email: lecturerData.email,
            name: lecturerData.full_name || lecturerData.name,
            role: 'lecturer',
            employee_id: lecturerData.employee_id,
            title: lecturerData.title,
            department: lecturerData.department,
            office_location: lecturerData.office_location,
          }
        } else {
          throw new Error('Invalid user data received from authentication')
        }

        user.value = userInfo
        token.value = authToken

        // Persist to localStorage
        localStorage.setItem('auth_token', authToken)
        localStorage.setItem('auth_user', JSON.stringify(userInfo))

        return { success: true, message: data.message }
      } else {
        throw new Error(data.message || 'Google authentication failed')
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Google login failed',
      }
    }
  }

  const logout = async () => {
    try {
      // Call backend logout API if token exists
      if (token.value) {
        const apiUrl = `${apiBaseUrl}/auth/logout`

        await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            Authorization: `Bearer ${token.value}`,
          },
        })
      }
    } catch (error) {
      console.error('Logout API error:', error)
      // Continue with local logout even if API call fails
    } finally {
      // Always clear local state
      user.value = null
      token.value = null
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
    }
  }

  const getMe = async () => {
    try {
      if (!token.value) {
        throw new Error('No authentication token')
      }
      const apiUrl = `${apiBaseUrl}/auth/me`

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${token.value}`,
        },
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid, logout user
          await logout()
          throw new Error('Authentication expired')
        }
        throw new Error(data.message || 'Failed to fetch user data')
      }

      if (data.success) {
        let userInfo: User

        // Handle both student and lecturer data from API response
        if (data.data.student) {
          const studentData = data.data.student
          userInfo = {
            id: studentData.id.toString(),
            email: studentData.email,
            name: studentData.full_name || studentData.email,
            role: 'student',
            full_name: studentData.full_name,
            student_id: studentData.student_id,
            phone: studentData.phone,
            date_of_birth: studentData.date_of_birth,
            gender: studentData.gender,
            nationality: studentData.nationality,
            address: studentData.address,
            avatar_url: studentData.avatar_url,
            enrollment_status: studentData.enrollment_status,
            admission_date: studentData.admission_date,
            expected_graduation_date: studentData.expected_graduation_date,
            campus: studentData.campus,
            program: studentData.program,
            specialization: studentData.specialization,
            can_register: studentData.can_register,
            has_active_holds: studentData.has_active_holds,
          }
        } else if (data.data.lecturer) {
          const lecturerData = data.data.lecturer
          userInfo = {
            id: lecturerData.id.toString(),
            email: lecturerData.email,
            name: lecturerData.full_name || lecturerData.name || lecturerData.email,
            role: 'lecturer',
            full_name: lecturerData.full_name,
            employee_id: lecturerData.employee_id,
            phone: lecturerData.phone,
            date_of_birth: lecturerData.date_of_birth,
            gender: lecturerData.gender,
            nationality: lecturerData.nationality,
            address: lecturerData.address,
            avatar_url: lecturerData.avatar_url,
            title: lecturerData.title,
            department: lecturerData.department,
            office_location: lecturerData.office_location,
            can_register: false, // Lecturers don't register for courses
            has_active_holds: lecturerData.has_active_holds || false,
          }
        } else {
          throw new Error('Invalid user data received')
        }

        user.value = userInfo
        localStorage.setItem('auth_user', JSON.stringify(userInfo))

        return { success: true, user: userInfo }
      } else {
        throw new Error(data.message || 'Failed to fetch user data')
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user data',
      }
    }
  }

  const refreshToken = async () => {
    try {
      if (!token.value) {
        throw new Error('No authentication token')
      }
      const apiUrl = `${apiBaseUrl}/auth/refresh`

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${token.value}`,
        },
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 401) {
          // Token cannot be refreshed, logout user
          await logout()
          throw new Error('Authentication expired')
        }
        throw new Error(data.message || 'Token refresh failed')
      }

      if (data.success) {
        const newToken = data.data.token
        token.value = newToken
        localStorage.setItem('auth_token', newToken)

        return { success: true, token: newToken }
      } else {
        throw new Error(data.message || 'Token refresh failed')
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Token refresh failed',
      }
    }
  }

  // Initialize auth state on store creation
  initializeAuth()

  return {
    user,
    token,
    isAuthenticated,
    role,
    isStudent,
    isLecturer,
    getApiPrefix,
    login,
    loginWithGoogle,
    logout,
    getMe,
    refreshToken,
    initializeAuth,
  }
})
