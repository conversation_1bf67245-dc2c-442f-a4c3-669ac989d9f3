import { useBaseApi } from '@/shared/composables/useBaseApi'
import { useAuthStore } from '@/shared/stores/auth'

export function useStudentApi() {
  const authStore = useAuthStore()
  const baseApi = useBaseApi()

  // Create student-specific API instance with base URL prefix
  const studentApi = {
    ...baseApi,
    
    // Student Dashboard APIs
    dashboard: {
      getData: () => baseApi.get(`/api/student/dashboard`),
      getSummary: () => baseApi.get(`/api/student/dashboard/summary`),
      getAcademicProgress: () => baseApi.get(`/api/student/dashboard/academic-progress`),
    },

    // Student Course APIs
    courses: {
      getRegistered: () => baseApi.get(`/api/student/courses/registered`),
      getAvailable: () => baseApi.get(`/api/student/courses/available`),
      register: (courseId: string) => baseApi.post(`/api/student/courses/register`, { course_id: courseId }),
      drop: (courseId: string) => baseApi.post(`/api/student/courses/drop`, { course_id: courseId }),
    },

    // Student Grades APIs
    grades: {
      getAll: () => baseApi.get(`/api/student/grades`),
      getTrend: () => baseApi.get(`/api/student/grades/trend`),
      getByCourse: (courseId: string) => baseApi.get(`/api/student/grades/course/${courseId}`),
    },

    // Student Attendance APIs
    attendance: {
      getRecords: () => baseApi.get(`/api/student/attendance`),
      getByCourse: (courseId: string) => baseApi.get(`/api/student/attendance/course/${courseId}`),
    },

    // Student Profile APIs
    profile: {
      get: () => baseApi.get(`/api/student/profile`),
      update: (data: any) => baseApi.put(`/api/student/profile`, data),
      getStudyPlan: () => baseApi.get(`/api/student/profile/study-plan`),
    },

    // Student Holds APIs
    holds: {
      getActive: () => baseApi.get(`/api/student/holds/active`),
      getHistory: () => baseApi.get(`/api/student/holds/history`),
    },
  }

  return studentApi
}