<script setup lang="ts">
import { ref, computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Badge } from '@/shared/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/shared/components/ui/avatar'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  Edit,
  Save,
  X,
  Check,
  AlertCircle
} from 'lucide-vue-next'
import type { Student } from '../../types/models/student'

interface Props {
  student: Student | null
  isLoading?: boolean
  onSave?: (data: Partial<Student>) => Promise<void>
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Local state
const editingField = ref<string | null>(null)
const editingValue = ref<string>('')
const isSaving = ref(false)
const saveSuccess = ref<string | null>(null)

// Mock student data for demonstration
const mockStudent = computed(() => props.student || {
  id: 'student-1',
  student_id: 'S12345678',
  email: '<EMAIL>',
  phone: '+61 400 123 456',
  address: '123 Student Street, Melbourne VIC 3000',
  date_of_birth: '1995-06-15',
  enrollment_date: '2022-02-28',
  program_id: 'prog-1',
  specialization_id: 'spec-1',
  curriculum_version_id: 'cv-1',
  program: {
    id: 'prog-1',
    name: 'Bachelor of Computer Science',
    code: 'BCS',
    description: 'Comprehensive computer science program'
  },
  specialization: {
    id: 'spec-1',
    program_id: 'prog-1',
    name: 'Software Engineering',
    code: 'SE',
    program: {
      id: 'prog-1',
      name: 'Bachelor of Computer Science',
      code: 'BCS',
      description: 'Comprehensive computer science program'
    }
  },
  curriculum_version: {
    id: 'cv-1',
    program_id: 'prog-1',
    specialization_id: 'spec-1',
    semester_id: 'sem-1',
    program: {
      id: 'prog-1',
      name: 'Bachelor of Computer Science',
      code: 'BCS',
      description: 'Comprehensive computer science program'
    },
    specialization: {
      id: 'spec-1',
      program_id: 'prog-1',
      name: 'Software Engineering',
      code: 'SE',
      program: {
        id: 'prog-1',
        name: 'Bachelor of Computer Science',
        code: 'BCS',
        description: 'Comprehensive computer science program'
      }
    },
    semester: {
      id: 'sem-1',
      name: 'Spring 2024',
      code: 'SP24'
    }
  }
})

// Computed properties
const studentInitials = computed(() => {
  const email = mockStudent.value.email
  const name = email.split('@')[0].split('.')
  return name.map(n => n.charAt(0).toUpperCase()).join('')
})

const formattedDateOfBirth = computed(() => {
  return new Date(mockStudent.value.date_of_birth).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

const formattedEnrollmentDate = computed(() => {
  return new Date(mockStudent.value.enrollment_date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// Editable fields configuration
const editableFields = [
  { key: 'email', label: 'Email', icon: Mail, type: 'email' },
  { key: 'phone', label: 'Phone', icon: Phone, type: 'tel' },
  { key: 'address', label: 'Address', icon: MapPin, type: 'text' }
]

// Actions
const startEditing = (field: string, currentValue: string) => {
  editingField.value = field
  editingValue.value = currentValue
}

const cancelEditing = () => {
  editingField.value = null
  editingValue.value = ''
}

const saveField = async () => {
  if (!editingField.value || !props.onSave) return
  
  isSaving.value = true
  saveSuccess.value = null
  
  try {
    await props.onSave({
      [editingField.value]: editingValue.value
    })
    
    saveSuccess.value = editingField.value
    editingField.value = null
    editingValue.value = ''
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      saveSuccess.value = null
    }, 3000)
    
  } catch (error) {
    console.error('Failed to save field:', error)
  } finally {
    isSaving.value = false
  }
}

const getFieldValue = (key: string) => {
  return (mockStudent.value as any)[key] || ''
}
</script>

<template>
  <div class="space-y-6">
    <!-- Profile Header -->
    <Card>
      <CardHeader>
        <div class="flex items-center gap-4">
          <Avatar class="h-16 w-16">
            <AvatarImage src="" alt="Profile picture" />
            <AvatarFallback class="text-lg font-semibold">
              {{ studentInitials }}
            </AvatarFallback>
          </Avatar>
          
          <div class="flex-1">
            <CardTitle class="text-xl">{{ mockStudent.email.split('@')[0].replace('.', ' ').replace(/\b\w/g, l => l.toUpperCase()) }}</CardTitle>
            <p class="text-muted-foreground">{{ mockStudent.student_id }}</p>
            <div class="flex items-center gap-2 mt-2">
              <Badge variant="secondary">{{ mockStudent.program.code }}</Badge>
              <Badge variant="outline">{{ mockStudent.specialization?.name }}</Badge>
            </div>
          </div>
        </div>
      </CardHeader>
    </Card>

    <!-- Personal Information -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <User class="h-5 w-5" />
          Personal Information
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div v-if="isLoading" class="space-y-4">
          <div v-for="i in 3" :key="i" class="h-12 bg-muted animate-pulse rounded" />
        </div>
        
        <div v-else class="space-y-4">
          <!-- Editable Fields -->
          <div
            v-for="field in editableFields"
            :key="field.key"
            class="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/30 transition-colors"
          >
            <div class="flex items-center gap-3 flex-1">
              <component :is="field.icon" class="h-4 w-4 text-muted-foreground" />
              <div class="flex-1">
                <Label class="text-sm font-medium">{{ field.label }}</Label>
                
                <!-- Edit Mode -->
                <div v-if="editingField === field.key" class="flex items-center gap-2 mt-1">
                  <Input
                    v-model="editingValue"
                    :type="field.type"
                    class="flex-1"
                    @keyup.enter="saveField"
                    @keyup.escape="cancelEditing"
                  />
                  <Button
                    size="sm"
                    @click="saveField"
                    :disabled="isSaving"
                  >
                    <Save class="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    @click="cancelEditing"
                    :disabled="isSaving"
                  >
                    <X class="h-3 w-3" />
                  </Button>
                </div>
                
                <!-- Display Mode -->
                <div v-else class="flex items-center gap-2 mt-1">
                  <span class="text-sm">{{ getFieldValue(field.key) }}</span>
                  <div v-if="saveSuccess === field.key" class="flex items-center gap-1 text-green-600">
                    <Check class="h-3 w-3" />
                    <span class="text-xs">Saved</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Edit Button -->
            <Button
              v-if="editingField !== field.key"
              size="sm"
              variant="ghost"
              @click="startEditing(field.key, getFieldValue(field.key))"
            >
              <Edit class="h-3 w-3" />
            </Button>
          </div>
          
          <!-- Read-only Fields -->
          <div class="space-y-3 pt-4 border-t">
            <div class="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div class="flex items-center gap-3">
                <Calendar class="h-4 w-4 text-muted-foreground" />
                <div>
                  <Label class="text-sm font-medium">Date of Birth</Label>
                  <div class="text-sm mt-1">{{ formattedDateOfBirth }}</div>
                </div>
              </div>
            </div>
            
            <div class="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div class="flex items-center gap-3">
                <Calendar class="h-4 w-4 text-muted-foreground" />
                <div>
                  <Label class="text-sm font-medium">Enrollment Date</Label>
                  <div class="text-sm mt-1">{{ formattedEnrollmentDate }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Academic Information -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <AlertCircle class="h-5 w-5" />
          Academic Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="space-y-4">
          <div v-for="i in 2" :key="i" class="h-12 bg-muted animate-pulse rounded" />
        </div>
        
        <div v-else class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-3 bg-muted/30 rounded-lg">
              <Label class="text-sm font-medium">Program</Label>
              <div class="text-sm mt-1">{{ mockStudent.program.name }}</div>
              <div class="text-xs text-muted-foreground">{{ mockStudent.program.code }}</div>
            </div>
            
            <div class="p-3 bg-muted/30 rounded-lg">
              <Label class="text-sm font-medium">Specialization</Label>
              <div class="text-sm mt-1">{{ mockStudent.specialization?.name || 'Not specified' }}</div>
              <div class="text-xs text-muted-foreground">{{ mockStudent.specialization?.code || '' }}</div>
            </div>
          </div>
          
          <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-start gap-2">
              <AlertCircle class="h-4 w-4 text-blue-600 mt-0.5" />
              <div>
                <div class="font-medium text-sm text-blue-800">Academic Information</div>
                <div class="text-xs text-blue-700">
                  Contact your academic advisor to make changes to your program or specialization
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
