<script setup lang="ts">
import { ref, computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Avatar, AvatarFallback } from '@/shared/components/ui/avatar'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/shared/components/ui/sheet'
import { 
  CheckCircle, 
  Circle, 
  Lock,
  BookOpen,
  Target,
  Calendar,
  Award,
  Clock
} from 'lucide-vue-next'
import type { CurriculumVersion, CurriculumUnit, AcademicRecord } from '../../types/models/student'

interface Props {
  curriculumVersion: CurriculumVersion | null
  curriculumUnits: CurriculumUnit[]
  academicRecords: AcademicRecord[]
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Local state
const selectedUnit = ref<CurriculumUnit | null>(null)

// Computed properties
const unitsBySemester = computed(() => {
  const grouped: Record<string, CurriculumUnit[]> = {}
  
  props.curriculumUnits.forEach(unit => {
    const semesterName = unit.semester.name
    if (!grouped[semesterName]) {
      grouped[semesterName] = []
    }
    grouped[semesterName].push(unit)
  })
  
  return grouped
})

const completedUnitIds = computed(() => {
  return props.academicRecords.map(record => record.unit_id)
})

const studyPlanStats = computed(() => {
  const total = props.curriculumUnits.length
  const completed = completedUnitIds.value.length
  const totalCredits = props.curriculumUnits.reduce((sum, unit) => sum + unit.unit.credit_points, 0)
  const completedCredits = props.curriculumUnits
    .filter(unit => completedUnitIds.value.includes(unit.unit.id))
    .reduce((sum, unit) => sum + unit.unit.credit_points, 0)
  
  return {
    total,
    completed,
    remaining: total - completed,
    totalCredits,
    completedCredits,
    remainingCredits: totalCredits - completedCredits,
    completionPercentage: total > 0 ? Math.round((completed / total) * 100) : 0
  }
})

// Helper functions
const getUnitStatus = (unit: CurriculumUnit): 'completed' | 'available' | 'locked' => {
  if (completedUnitIds.value.includes(unit.unit.id)) return 'completed'
  
  // Mock availability logic - in real app, this would check prerequisites
  const unitIndex = props.curriculumUnits.findIndex(u => u.id === unit.id)
  const completedCount = completedUnitIds.value.length
  return unitIndex <= completedCount + 2 ? 'available' : 'locked'
}

const getUnitIcon = (status: string) => {
  switch (status) {
    case 'completed': return CheckCircle
    case 'available': return Circle
    case 'locked': return Lock
    default: return Circle
  }
}

const getUnitColor = (status: string) => {
  switch (status) {
    case 'completed': return 'text-green-600'
    case 'available': return 'text-blue-600'
    case 'locked': return 'text-muted-foreground'
    default: return 'text-muted-foreground'
  }
}

const getUnitAvatarColor = (status: string) => {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-700'
    case 'available': return 'bg-blue-100 text-blue-700'
    case 'locked': return 'bg-muted text-muted-foreground'
    default: return 'bg-muted text-muted-foreground'
  }
}

const getGradeForUnit = (unitId: string) => {
  const record = props.academicRecords.find(r => r.unit_id === unitId)
  if (!record) return null
  
  // Mock grade calculation
  const grades = ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C']
  return grades[Math.floor(Math.random() * grades.length)]
}

const openUnitDetails = (unit: CurriculumUnit) => {
  selectedUnit.value = unit
}

const formatSemesterOrder = (semesterName: string) => {
  // Extract semester number for ordering
  const match = semesterName.match(/(\d+)/)
  return match ? parseInt(match[1]) : 999
}

const orderedSemesters = computed(() => {
  return Object.keys(unitsBySemester.value).sort((a, b) => {
    return formatSemesterOrder(a) - formatSemesterOrder(b)
  })
})
</script>

<template>
  <div class="space-y-6">
    <!-- Study Plan Overview -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Target class="h-5 w-5" />
          Study Plan Progress
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="space-y-4">
          <div class="h-8 bg-muted animate-pulse rounded" />
          <div class="grid grid-cols-3 gap-4">
            <div v-for="i in 3" :key="i" class="h-16 bg-muted animate-pulse rounded" />
          </div>
        </div>
        
        <div v-else class="space-y-4">
          <!-- Overall Progress -->
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">
              {{ studyPlanStats.completionPercentage }}%
            </div>
            <div class="text-sm text-muted-foreground">Study Plan Complete</div>
          </div>
          
          <!-- Detailed Stats -->
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-xl font-bold text-green-600">{{ studyPlanStats.completed }}</div>
              <div class="text-sm text-muted-foreground">Units Completed</div>
            </div>
            <div>
              <div class="text-xl font-bold text-blue-600">{{ studyPlanStats.remaining }}</div>
              <div class="text-sm text-muted-foreground">Units Remaining</div>
            </div>
            <div>
              <div class="text-xl font-bold text-purple-600">{{ studyPlanStats.completedCredits }}</div>
              <div class="text-sm text-muted-foreground">Credits Earned</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Semester-based Study Plan -->
    <div v-if="isLoading" class="space-y-4">
      <div v-for="i in 4" :key="i" class="space-y-3">
        <div class="h-6 bg-muted animate-pulse rounded w-32" />
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          <div v-for="j in 3" :key="j" class="h-20 bg-muted animate-pulse rounded" />
        </div>
      </div>
    </div>
    
    <div v-else-if="orderedSemesters.length > 0" class="space-y-6">
      <div
        v-for="semesterName in orderedSemesters"
        :key="semesterName"
        class="space-y-4"
      >
        <!-- Semester Header -->
        <div class="flex items-center gap-3">
          <h3 class="text-lg font-semibold">{{ semesterName }}</h3>
          <Badge variant="outline">
            {{ unitsBySemester[semesterName].length }} Units
          </Badge>
          <Badge variant="secondary">
            {{ unitsBySemester[semesterName].reduce((sum, unit) => sum + unit.unit.credit_points, 0) }} Credits
          </Badge>
        </div>

        <!-- Unit Avatars Grid -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3">
          <Sheet>
            <SheetTrigger as-child>
              <Button
                v-for="unit in unitsBySemester[semesterName]"
                :key="unit.id"
                variant="ghost"
                class="h-auto p-3 flex flex-col items-center gap-2 hover:bg-muted/50"
                @click="openUnitDetails(unit)"
              >
                <!-- Unit Avatar -->
                <Avatar :class="['h-12 w-12', getUnitAvatarColor(getUnitStatus(unit))]">
                  <AvatarFallback class="text-xs font-bold">
                    {{ unit.unit.code.replace(/[^A-Z0-9]/g, '').slice(0, 3) }}
                  </AvatarFallback>
                </Avatar>
                
                <!-- Unit Info -->
                <div class="text-center">
                  <div class="text-xs font-medium truncate w-full">
                    {{ unit.unit.code }}
                  </div>
                  <div class="flex items-center justify-center gap-1 mt-1">
                    <component 
                      :is="getUnitIcon(getUnitStatus(unit))" 
                      :class="['h-3 w-3', getUnitColor(getUnitStatus(unit))]"
                    />
                    <span v-if="getUnitStatus(unit) === 'completed'" class="text-xs text-green-600">
                      {{ getGradeForUnit(unit.unit.id) }}
                    </span>
                  </div>
                </div>
              </Button>
            </SheetTrigger>
            
            <SheetContent>
              <SheetHeader>
                <SheetTitle>{{ selectedUnit?.unit.code }} - {{ selectedUnit?.unit.name }}</SheetTitle>
                <SheetDescription>
                  Unit details and progress information
                </SheetDescription>
              </SheetHeader>
              
              <div v-if="selectedUnit" class="space-y-6 mt-6">
                <!-- Status Badge -->
                <div class="flex items-center gap-2">
                  <component 
                    :is="getUnitIcon(getUnitStatus(selectedUnit))" 
                    :class="['h-5 w-5', getUnitColor(getUnitStatus(selectedUnit))]"
                  />
                  <Badge :variant="getUnitStatus(selectedUnit) === 'completed' ? 'default' : 'outline'">
                    {{ getUnitStatus(selectedUnit).charAt(0).toUpperCase() + getUnitStatus(selectedUnit).slice(1) }}
                  </Badge>
                  <Badge v-if="getUnitStatus(selectedUnit) === 'completed'" variant="secondary">
                    Grade: {{ getGradeForUnit(selectedUnit.unit.id) }}
                  </Badge>
                </div>
                
                <!-- Unit Details -->
                <div class="space-y-4">
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <div class="flex items-center gap-1 text-muted-foreground text-sm">
                        <Target class="h-3 w-3" />
                        <span>Credit Points</span>
                      </div>
                      <div class="font-medium">{{ selectedUnit.unit.credit_points }}</div>
                    </div>
                    <div>
                      <div class="flex items-center gap-1 text-muted-foreground text-sm">
                        <Calendar class="h-3 w-3" />
                        <span>Semester</span>
                      </div>
                      <div class="font-medium">{{ selectedUnit.semester.name }}</div>
                    </div>
                  </div>
                  
                  <!-- Unit Description -->
                  <div>
                    <div class="flex items-center gap-1 text-muted-foreground text-sm mb-2">
                      <BookOpen class="h-3 w-3" />
                      <span>Description</span>
                    </div>
                    <div class="text-sm text-muted-foreground">
                      This unit covers fundamental concepts and practical applications in {{ selectedUnit.unit.name.toLowerCase() }}.
                      Students will develop skills through lectures, tutorials, and hands-on projects.
                    </div>
                  </div>
                  
                  <!-- Prerequisites (Mock) -->
                  <div>
                    <div class="flex items-center gap-1 text-muted-foreground text-sm mb-2">
                      <Clock class="h-3 w-3" />
                      <span>Prerequisites</span>
                    </div>
                    <div class="text-sm">
                      <Badge variant="outline" class="mr-1 mb-1">CS101</Badge>
                      <Badge variant="outline" class="mr-1 mb-1">MATH101</Badge>
                    </div>
                  </div>
                  
                  <!-- Status-specific Information -->
                  <div v-if="getUnitStatus(selectedUnit) === 'completed'" class="p-3 bg-green-50 border border-green-200 rounded">
                    <div class="flex items-start gap-2">
                      <Award class="h-4 w-4 text-green-600 mt-0.5" />
                      <div>
                        <div class="font-medium text-sm text-green-800">Unit Completed</div>
                        <div class="text-xs text-green-700">
                          You successfully completed this unit with grade {{ getGradeForUnit(selectedUnit.unit.id) }}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div v-else-if="getUnitStatus(selectedUnit) === 'available'" class="p-3 bg-blue-50 border border-blue-200 rounded">
                    <div class="flex items-start gap-2">
                      <Circle class="h-4 w-4 text-blue-600 mt-0.5" />
                      <div>
                        <div class="font-medium text-sm text-blue-800">Available for Enrollment</div>
                        <div class="text-xs text-blue-700">
                          This unit is available for registration in the upcoming semester
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div v-else class="p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <div class="flex items-start gap-2">
                      <Lock class="h-4 w-4 text-yellow-600 mt-0.5" />
                      <div>
                        <div class="font-medium text-sm text-yellow-800">Prerequisites Required</div>
                        <div class="text-xs text-yellow-700">
                          Complete prerequisite units to unlock this unit
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <BookOpen class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <p class="text-muted-foreground">No study plan available</p>
      <p class="text-sm text-muted-foreground">
        Your study plan will appear here once your curriculum is loaded
      </p>
    </div>
  </div>
</template>
