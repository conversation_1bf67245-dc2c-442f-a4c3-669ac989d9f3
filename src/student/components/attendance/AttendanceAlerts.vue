<script setup lang="ts">
import { computed } from 'vue'
import { <PERSON>ert, AlertDescription } from '@/shared/components/ui/alert'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { 
  AlertTriangle, 
  XCircle, 
  Clock,
  CheckCircle,
  Mail,
  Phone,
  X
} from 'lucide-vue-next'
import type { CourseOffering } from '../../types/models/course'

interface AttendanceAlert {
  type: 'critical' | 'warning'
  message: string
  courseOffering: CourseOffering
  percentage: number
  sessionsRemaining?: number
  actionRequired?: string
  contactInfo?: {
    name: string
    email: string
    phone?: string
  }
}

interface Props {
  alerts: AttendanceAlert[]
  onDismiss?: (alertIndex: number) => void
  onContact?: (contactInfo: any) => void
}

const props = defineProps<Props>()

// Computed properties
const criticalAlerts = computed(() => 
  props.alerts.filter(alert => alert.type === 'critical')
)

const warningAlerts = computed(() => 
  props.alerts.filter(alert => alert.type === 'warning')
)

const hasAlerts = computed(() => 
  props.alerts.length > 0
)

const getAlertIcon = (type: string) => {
  switch (type) {
    case 'critical': return XCircle
    case 'warning': return AlertTriangle
    default: return Clock
  }
}

const getAlertVariant = (type: string) => {
  switch (type) {
    case 'critical': return 'destructive'
    case 'warning': return 'default'
    default: return 'secondary'
  }
}

const getRecommendation = (alert: AttendanceAlert) => {
  if (alert.type === 'critical') {
    return {
      title: 'Immediate Action Required',
      actions: [
        'Contact your lecturer immediately',
        'Attend all remaining sessions',
        'Provide medical certificates if applicable',
        'Consider course withdrawal if necessary'
      ]
    }
  } else {
    return {
      title: 'Recommended Actions',
      actions: [
        'Attend all upcoming sessions',
        'Set calendar reminders',
        'Contact lecturer if you have concerns',
        'Review attendance policy'
      ]
    }
  }
}

// Actions
const handleDismiss = (index: number) => {
  if (props.onDismiss) {
    props.onDismiss(index)
  }
}

const handleContact = (contactInfo: any) => {
  if (props.onContact) {
    props.onContact(contactInfo)
  }
}
</script>

<template>
  <div v-if="hasAlerts" class="space-y-4">
    <!-- Critical Alerts -->
    <div v-if="criticalAlerts.length > 0" class="space-y-3">
      <div class="flex items-center gap-2">
        <XCircle class="h-5 w-5 text-destructive" />
        <h3 class="font-semibold text-destructive">Critical Attendance Issues</h3>
        <Badge variant="destructive">{{ criticalAlerts.length }}</Badge>
      </div>
      
      <div class="space-y-3">
        <Alert
          v-for="(alert, index) in criticalAlerts"
          :key="index"
          variant="destructive"
        >
          <XCircle class="h-4 w-4" />
          <AlertDescription class="space-y-3">
            <div>
              <div class="font-medium">{{ alert.message }}</div>
              <div class="text-sm mt-1">
                Course: {{ alert.courseOffering.curriculum_unit.unit.code }} - 
                {{ alert.courseOffering.curriculum_unit.unit.name }}
              </div>
              <div class="text-sm">
                Current Attendance: {{ alert.percentage }}%
                <span v-if="alert.sessionsRemaining">
                  • {{ alert.sessionsRemaining }} sessions remaining
                </span>
              </div>
            </div>
            
            <!-- Action Required -->
            <div v-if="alert.actionRequired" class="p-2 bg-destructive/10 rounded border">
              <div class="font-medium text-sm">Action Required:</div>
              <div class="text-sm">{{ alert.actionRequired }}</div>
            </div>
            
            <!-- Contact Information -->
            <div v-if="alert.contactInfo" class="flex items-center justify-between">
              <div class="text-sm">
                <div class="font-medium">Contact: {{ alert.contactInfo.name }}</div>
                <div class="flex items-center gap-4 mt-1">
                  <div class="flex items-center gap-1">
                    <Mail class="h-3 w-3" />
                    <span>{{ alert.contactInfo.email }}</span>
                  </div>
                  <div v-if="alert.contactInfo.phone" class="flex items-center gap-1">
                    <Phone class="h-3 w-3" />
                    <span>{{ alert.contactInfo.phone }}</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  @click="handleContact(alert.contactInfo)"
                >
                  Contact
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  @click="handleDismiss(index)"
                >
                  <X class="h-3 w-3" />
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>

    <!-- Warning Alerts -->
    <div v-if="warningAlerts.length > 0" class="space-y-3">
      <div class="flex items-center gap-2">
        <AlertTriangle class="h-5 w-5 text-yellow-600" />
        <h3 class="font-semibold text-yellow-700">Attendance Warnings</h3>
        <Badge variant="secondary">{{ warningAlerts.length }}</Badge>
      </div>
      
      <div class="space-y-3">
        <Alert
          v-for="(alert, index) in warningAlerts"
          :key="index"
          class="border-yellow-200 bg-yellow-50"
        >
          <AlertTriangle class="h-4 w-4 text-yellow-600" />
          <AlertDescription class="space-y-3">
            <div>
              <div class="font-medium text-yellow-800">{{ alert.message }}</div>
              <div class="text-sm text-yellow-700 mt-1">
                Course: {{ alert.courseOffering.curriculum_unit.unit.code }} - 
                {{ alert.courseOffering.curriculum_unit.unit.name }}
              </div>
              <div class="text-sm text-yellow-700">
                Current Attendance: {{ alert.percentage }}%
                <span v-if="alert.sessionsRemaining">
                  • {{ alert.sessionsRemaining }} sessions remaining
                </span>
              </div>
            </div>
            
            <!-- Recommendations -->
            <div class="p-2 bg-yellow-100 rounded border border-yellow-200">
              <div class="font-medium text-sm text-yellow-800 mb-1">
                {{ getRecommendation(alert).title }}:
              </div>
              <ul class="text-sm text-yellow-700 space-y-1">
                <li v-for="action in getRecommendation(alert).actions" :key="action" class="flex items-start gap-1">
                  <span class="text-yellow-600">•</span>
                  <span>{{ action }}</span>
                </li>
              </ul>
            </div>
            
            <!-- Actions -->
            <div class="flex justify-end gap-2">
              <Button
                size="sm"
                variant="outline"
                @click="handleDismiss(index + criticalAlerts.length)"
              >
                <X class="h-3 w-3 mr-1" />
                Dismiss
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>

    <!-- Recommendations Card -->
    <Card v-if="hasAlerts">
      <CardHeader>
        <CardTitle class="flex items-center gap-2 text-base">
          <CheckCircle class="h-4 w-4" />
          General Attendance Tips
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-2 text-sm">
          <div class="flex items-start gap-2">
            <span class="text-blue-600">•</span>
            <span>Set up calendar notifications for all your classes</span>
          </div>
          <div class="flex items-start gap-2">
            <span class="text-blue-600">•</span>
            <span>Plan your commute and arrive 10 minutes early</span>
          </div>
          <div class="flex items-start gap-2">
            <span class="text-blue-600">•</span>
            <span>Keep medical certificates for any absences due to illness</span>
          </div>
          <div class="flex items-start gap-2">
            <span class="text-blue-600">•</span>
            <span>Contact your lecturer in advance if you know you'll miss a class</span>
          </div>
          <div class="flex items-start gap-2">
            <span class="text-blue-600">•</span>
            <span>Review your course outline for specific attendance requirements</span>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
