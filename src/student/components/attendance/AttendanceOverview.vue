<script setup lang="ts">
import { computed } from 'vue'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Progress } from '@/shared/components/ui/progress'
import { 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Calendar,
  Users
} from 'lucide-vue-next'
import type { CourseOffering } from '../../types/models/course'

interface AttendanceSummary {
  course_offering: CourseOffering
  total_sessions: number
  attended_sessions: number
  attendance_percentage: number
  minimum_required: number
  status: 'good' | 'warning' | 'critical'
}

interface Props {
  attendanceSummary: AttendanceSummary[]
  overallAttendanceRate: number
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Computed properties
const totalSessions = computed(() => {
  return props.attendanceSummary.reduce((sum, summary) => sum + summary.total_sessions, 0)
})

const totalAttended = computed(() => {
  return props.attendanceSummary.reduce((sum, summary) => sum + summary.attended_sessions, 0)
})

const goodAttendance = computed(() => {
  return props.attendanceSummary.filter(s => s.status === 'good')
})

const warningAttendance = computed(() => {
  return props.attendanceSummary.filter(s => s.status === 'warning')
})

const criticalAttendance = computed(() => {
  return props.attendanceSummary.filter(s => s.status === 'critical')
})

const attendanceStatus = computed(() => {
  const rate = props.overallAttendanceRate
  if (rate >= 90) return { label: 'Excellent', color: 'text-green-600', variant: 'default' as const }
  if (rate >= 80) return { label: 'Good', color: 'text-blue-600', variant: 'secondary' as const }
  if (rate >= 75) return { label: 'Warning', color: 'text-yellow-600', variant: 'outline' as const }
  return { label: 'Critical', color: 'text-red-600', variant: 'destructive' as const }
})

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'good': return CheckCircle
    case 'warning': return AlertTriangle
    case 'critical': return XCircle
    default: return Clock
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'good': return 'text-green-600'
    case 'warning': return 'text-yellow-600'
    case 'critical': return 'text-red-600'
    default: return 'text-muted-foreground'
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'good': return 'default' as const
    case 'warning': return 'outline' as const
    case 'critical': return 'destructive' as const
    default: return 'secondary' as const
  }
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return 'bg-green-500'
  if (percentage >= 80) return 'bg-blue-500'
  if (percentage >= 75) return 'bg-yellow-500'
  return 'bg-red-500'
}
</script>

<template>
  <div class="space-y-6">
    <!-- Overall Attendance Summary -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Users class="h-5 w-5" />
          Attendance Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="space-y-4">
          <div class="h-8 bg-muted animate-pulse rounded" />
          <div class="grid grid-cols-4 gap-4">
            <div v-for="i in 4" :key="i" class="h-16 bg-muted animate-pulse rounded" />
          </div>
        </div>
        
        <div v-else class="space-y-6">
          <!-- Overall Rate -->
          <div class="text-center">
            <div :class="['text-4xl font-bold', attendanceStatus.color]">
              {{ overallAttendanceRate }}%
            </div>
            <div class="text-sm text-muted-foreground mb-2">Overall Attendance Rate</div>
            <Badge :variant="attendanceStatus.variant">
              {{ attendanceStatus.label }}
            </Badge>
          </div>
          
          <!-- Statistics Grid -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ totalSessions }}</div>
              <div class="text-sm text-muted-foreground">Total Sessions</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{{ totalAttended }}</div>
              <div class="text-sm text-muted-foreground">Attended</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-yellow-600">{{ warningAttendance.length }}</div>
              <div class="text-sm text-muted-foreground">Warnings</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-red-600">{{ criticalAttendance.length }}</div>
              <div class="text-sm text-muted-foreground">Critical</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Course-wise Attendance -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Course Attendance</h3>
      
      <div v-if="isLoading" class="space-y-3">
        <div v-for="i in 3" :key="i" class="h-24 bg-muted animate-pulse rounded-lg" />
      </div>
      
      <div v-else-if="attendanceSummary.length > 0" class="space-y-3">
        <Card
          v-for="summary in attendanceSummary"
          :key="summary.course_offering.id"
          class="transition-all duration-200 hover:shadow-md"
        >
          <CardContent class="p-4">
            <div class="flex items-center justify-between">
              <!-- Course Info -->
              <div class="flex items-center gap-3 flex-1">
                <component 
                  :is="getStatusIcon(summary.status)" 
                  :class="['h-5 w-5', getStatusColor(summary.status)]"
                />
                <div class="min-w-0 flex-1">
                  <h4 class="font-medium">
                    {{ summary.course_offering.curriculum_unit.unit.code }} - 
                    {{ summary.course_offering.curriculum_unit.unit.name }}
                  </h4>
                  <div class="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                    <div class="flex items-center gap-1">
                      <Calendar class="h-4 w-4" />
                      <span>{{ summary.attended_sessions }}/{{ summary.total_sessions }} sessions</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span>Required: {{ summary.minimum_required }}%</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Attendance Progress -->
              <div class="flex items-center gap-4 shrink-0">
                <!-- Progress Bar -->
                <div class="flex items-center gap-2">
                  <Progress 
                    :value="summary.attendance_percentage" 
                    class="w-24 h-2"
                  />
                  <span class="font-bold text-lg w-12 text-right">
                    {{ summary.attendance_percentage }}%
                  </span>
                </div>
                
                <!-- Status Badge -->
                <Badge :variant="getStatusVariant(summary.status)">
                  {{ summary.status.charAt(0).toUpperCase() + summary.status.slice(1) }}
                </Badge>
              </div>
            </div>
            
            <!-- Warning Messages -->
            <div v-if="summary.status === 'warning'" class="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
              <div class="flex items-center gap-2">
                <AlertTriangle class="h-4 w-4" />
                <span>Attendance below 80% - Monitor closely to avoid restrictions</span>
              </div>
            </div>
            
            <div v-if="summary.status === 'critical'" class="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-800">
              <div class="flex items-center gap-2">
                <XCircle class="h-4 w-4" />
                <span>Attendance below 75% - Risk of exam restriction or course failure</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <Calendar class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <p class="text-muted-foreground">No attendance data available</p>
        <p class="text-sm text-muted-foreground">
          Attendance records will appear here once classes begin
        </p>
      </div>
    </div>
  </div>
</template>
