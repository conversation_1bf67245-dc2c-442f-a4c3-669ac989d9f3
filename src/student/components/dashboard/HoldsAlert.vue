<script setup lang="ts">
import { computed } from 'vue'
import { Alert<PERSON>riangle, Clock, ExternalLink, X } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { useRouter } from 'vue-router'
import type { AcademicHold } from '../../types/models/hold'

interface Props {
  holds: AcademicHold[]
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  holds: () => [],
})

const router = useRouter()

// Computed properties for holds display
// Note: AcademicHold from database schema doesn't include severity, status, etc.
// These would need to be added to the API response or handled differently
const activeHolds = computed(() => {
  return props.holds // All holds are considered active for now
})

const criticalHolds = computed(() => {
  // For now, treat all holds as critical since we don't have severity info
  return props.holds
})

const warningHolds = computed(() => {
  return [] // No warning holds without severity classification
})

const getSeverityColor = () => {
  return 'destructive' // Default to critical styling
}

const getSeverityIcon = () => {
  return AlertTriangle // Default to alert triangle
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
}

const navigateToHolds = () => {
  router.push('/holds')
}

const resolveHold = (holdId: string) => {
  // This would typically call an API to resolve the hold
  console.log('Resolving hold:', holdId)
  // For now, just navigate to the holds page
  navigateToHolds()
}
</script>

<template>
  <Card
    v-if="!isLoading && activeHolds.length > 0"
    class="border-destructive/50 bg-destructive/5"
    data-testid="holds-alert"
  >
    <CardHeader class="pb-3">
      <CardTitle class="flex items-center gap-2 text-lg text-destructive">
        <AlertTriangle class="h-5 w-5" />
        <span>Academic Holds</span>
        <Badge variant="destructive" class="ml-auto">
          {{ activeHolds.length }}
        </Badge>
      </CardTitle>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- Critical Holds -->
      <div v-if="criticalHolds.length > 0" class="space-y-3">
        <h4 class="text-sm font-medium text-destructive flex items-center gap-2">
          <AlertTriangle class="h-4 w-4" />
          Critical Issues ({{ criticalHolds.length }})
        </h4>
        <div class="space-y-2">
          <template v-for="hold in criticalHolds.slice(0, 2)" :key="hold.id">
            <div
              class="flex items-start gap-3 p-3 bg-destructive/10 rounded-lg border border-destructive/20"
            >
              <component
                :is="getSeverityIcon()"
                class="h-4 w-4 text-destructive mt-0.5 flex-shrink-0"
              />
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between gap-2">
                  <div class="space-y-1">
                    <p class="text-sm font-medium">Academic Hold</p>
                    <p class="text-xs text-muted-foreground">
                      Hold placed by {{ hold.placed_by.name }}
                    </p>
                    <div class="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>Student: {{ hold.student.full_name }}</span>
                    </div>
                  </div>
                  <Badge :variant="getSeverityColor()" class="text-xs"> Critical </Badge>
                </div>
                <div class="mt-2">
                  <p class="text-xs font-medium text-muted-foreground mb-1">Contact:</p>
                  <p class="text-xs">{{ hold.placed_by.email }}</p>
                </div>
              </div>
            </div>
          </template>

          <!-- Show more critical holds if there are more than 2 -->
          <div v-if="criticalHolds.length > 2" class="text-center">
            <Button variant="outline" size="sm" @click="navigateToHolds">
              View {{ criticalHolds.length - 2 }} more critical holds
            </Button>
          </div>
        </div>
      </div>

      <!-- Warning Holds -->
      <div v-if="warningHolds.length > 0" class="space-y-3">
        <h4 class="text-sm font-medium text-yellow-600 flex items-center gap-2">
          <Clock class="h-4 w-4" />
          Warnings ({{ warningHolds.length }})
        </h4>
        <div class="space-y-2">
          <template v-for="hold in warningHolds.slice(0, 1)" :key="hold.id">
            <div
              class="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200"
            >
              <component
                :is="getSeverityIcon(hold.severity)"
                class="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0"
              />
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between gap-2">
                  <div class="space-y-1">
                    <p class="text-sm font-medium">{{ hold.title }}</p>
                    <p class="text-xs text-muted-foreground">{{ hold.description }}</p>
                    <div class="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>Due: {{ formatDate(hold.due_date) }}</span>
                      <span>•</span>
                      <span>{{ hold.department }}</span>
                    </div>
                  </div>
                  <Badge :variant="getSeverityColor(hold.severity)" class="text-xs">
                    {{ hold.severity }}
                  </Badge>
                </div>
              </div>
            </div>
          </template>

          <!-- Show more warning holds if there are more than 1 -->
          <div v-if="warningHolds.length > 1" class="text-center">
            <Button variant="outline" size="sm" @click="navigateToHolds">
              View {{ warningHolds.length - 1 }} more warnings
            </Button>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-2 pt-2">
        <Button @click="navigateToHolds" class="flex-1">
          <ExternalLink class="h-4 w-4 mr-2" />
          View All Holds
        </Button>
        <Button
          v-if="criticalHolds.length === 1"
          variant="outline"
          @click="resolveHold(criticalHolds[0].id)"
        >
          Resolve Now
        </Button>
      </div>
    </CardContent>
  </Card>

  <!-- Loading State -->
  <Card v-else-if="isLoading" class="border-muted">
    <CardHeader class="pb-3">
      <div class="flex items-center gap-2">
        <div class="h-5 w-5 bg-muted animate-pulse rounded" />
        <div class="h-5 w-32 bg-muted animate-pulse rounded" />
        <div class="h-5 w-8 bg-muted animate-pulse rounded ml-auto" />
      </div>
    </CardHeader>
    <CardContent class="space-y-4">
      <div class="space-y-2">
        <div class="h-16 w-full bg-muted animate-pulse rounded" />
        <div class="h-16 w-full bg-muted animate-pulse rounded" />
      </div>
      <div class="flex gap-2">
        <div class="h-9 flex-1 bg-muted animate-pulse rounded" />
        <div class="h-9 w-24 bg-muted animate-pulse rounded" />
      </div>
    </CardContent>
  </Card>
</template>
