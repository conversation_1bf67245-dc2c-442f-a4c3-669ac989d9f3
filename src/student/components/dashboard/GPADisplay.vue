<script setup lang="ts">
import { computed } from 'vue'
import { TrendingUp, TrendingDown, Minus, Award } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import type { GPACalculation } from '../../types/models/student'

interface Props {
  gpaData:
    | (GPACalculation & {
        rank?: number
        trend: 'up' | 'down' | 'stable'
        semester_gpa: number
        cumulative_gpa: number
      })
    | null
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Computed properties for GPA display
const gpaColor = computed(() => {
  if (!props.gpaData) return 'text-muted-foreground'

  const gpa = props.gpaData.cumulative_gpa
  if (gpa >= 3.7) return 'text-green-600'
  if (gpa >= 3.0) return 'text-blue-600'
  if (gpa >= 2.5) return 'text-yellow-600'
  return 'text-red-600'
})

const gpaStatus = computed(() => {
  if (!props.gpaData) return 'Unknown'

  const gpa = props.gpaData.cumulative_gpa
  if (gpa >= 3.7) return 'Excellent'
  if (gpa >= 3.0) return 'Good'
  if (gpa >= 2.5) return 'Satisfactory'
  return 'Needs Improvement'
})

const trendIcon = computed(() => {
  if (!props.gpaData?.trend) return Minus

  switch (props.gpaData.trend) {
    case 'up':
      return TrendingUp
    case 'down':
      return TrendingDown
    default:
      return Minus
  }
})

const trendColor = computed(() => {
  if (!props.gpaData?.trend) return 'text-muted-foreground'

  switch (props.gpaData.trend) {
    case 'up':
      return 'text-green-600'
    case 'down':
      return 'text-red-600'
    default:
      return 'text-muted-foreground'
  }
})

const formatGPA = (gpa: number) => {
  return gpa.toFixed(2)
}

// Note: Grade distribution would need to be added to the API response
// if this feature is needed in the future
const getGradeDistribution = () => {
  return []
}
</script>

<template>
  <Card data-testid="gpa-display">
    <CardHeader class="pb-3">
      <CardTitle class="flex items-center gap-2 text-lg">
        <Award class="h-5 w-5 text-primary" />
        <span>Academic Performance</span>
      </CardTitle>
    </CardHeader>

    <CardContent class="space-y-6">
      <!-- Main GPA Display -->
      <div class="text-center space-y-2">
        <div v-if="!isLoading && gpaData" class="space-y-1">
          <div :class="['text-4xl font-bold', gpaColor]">
            {{ formatGPA(gpaData.cumulative_gpa) }}
          </div>
          <div class="text-sm text-muted-foreground">Current GPA</div>
          <Badge :variant="gpaData.cumulative_gpa >= 3.0 ? 'default' : 'secondary'" class="text-xs">
            {{ gpaStatus }}
          </Badge>
        </div>
        <div v-else-if="isLoading" class="space-y-2">
          <div class="h-12 w-20 bg-muted animate-pulse rounded mx-auto" />
          <div class="h-4 w-24 bg-muted animate-pulse rounded mx-auto" />
          <div class="h-5 w-16 bg-muted animate-pulse rounded mx-auto" />
        </div>
      </div>

      <!-- GPA Comparison -->
      <div v-if="!isLoading && gpaData" class="grid grid-cols-2 gap-4">
        <div class="text-center space-y-1">
          <div class="text-lg font-semibold">{{ formatGPA(gpaData.semester_gpa) }}</div>
          <div class="text-xs text-muted-foreground">This Semester</div>
        </div>
        <div class="text-center space-y-1">
          <div class="text-lg font-semibold">{{ formatGPA(gpaData.cumulative_gpa) }}</div>
          <div class="text-xs text-muted-foreground">Cumulative</div>
        </div>
      </div>
      <div v-else-if="isLoading" class="grid grid-cols-2 gap-4">
        <div class="text-center space-y-1">
          <div class="h-6 w-12 bg-muted animate-pulse rounded mx-auto" />
          <div class="h-3 w-20 bg-muted animate-pulse rounded mx-auto" />
        </div>
        <div class="text-center space-y-1">
          <div class="h-6 w-12 bg-muted animate-pulse rounded mx-auto" />
          <div class="h-3 w-16 bg-muted animate-pulse rounded mx-auto" />
        </div>
      </div>

      <!-- Trend Indicator -->
      <div
        v-if="!isLoading && gpaData?.trend"
        class="flex items-center justify-center gap-2 p-2 bg-muted/50 rounded-lg"
      >
        <component :is="trendIcon" :class="['h-4 w-4', trendColor]" />
        <span class="text-sm font-medium capitalize">{{ gpaData.trend }} trend</span>
      </div>
      <div v-else-if="isLoading" class="h-8 w-full bg-muted animate-pulse rounded" />

      <!-- Rank Display (if available) -->
      <div v-if="!isLoading && gpaData?.rank" class="p-3 bg-muted/50 rounded-lg">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">Class Rank</span>
          <Badge variant="outline" class="text-xs"> #{{ gpaData.rank }} </Badge>
        </div>
      </div>
      <div v-else-if="isLoading" class="h-12 w-full bg-muted animate-pulse rounded" />
    </CardContent>
  </Card>
</template>
