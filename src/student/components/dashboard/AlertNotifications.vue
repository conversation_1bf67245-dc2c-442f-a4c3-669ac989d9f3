<script setup lang="ts">
import { computed, ref } from 'vue'
import { Bell, Calendar, Clock, BookOpen, X, ChevronRight } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { useRouter } from 'vue-router'
import type { Notification } from '@/shared/types/models/notification'

interface Props {
  notifications: Notification[]
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  notifications: () => []
})

const router = useRouter()
const dismissedNotifications = ref<Set<string>>(new Set())

// Computed properties for notifications
const activeNotifications = computed(() => {
  return props.notifications
    .filter(notification => 
      notification.type === 'academic_alert' && 
      !dismissedNotifications.value.has(notification.id)
    )
    .sort((a, b) => {
      // Sort by priority and then by date
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 1
      const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 1
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority
      }
      
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    })
})

const urgentNotifications = computed(() => {
  return activeNotifications.value.filter(n => n.priority === 'high')
})

const getNotificationIcon = (category: string) => {
  switch (category) {
    case 'registration':
      return BookOpen
    case 'deadline':
      return Clock
    case 'schedule':
      return Calendar
    default:
      return Bell
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'destructive'
    case 'medium':
      return 'secondary'
    default:
      return 'outline'
  }
}

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
}

const dismissNotification = (notificationId: string) => {
  dismissedNotifications.value.add(notificationId)
  // In a real app, this would also call an API to mark as dismissed
}

const handleNotificationClick = (notification: Notification) => {
  if (notification.action_url) {
    router.push(notification.action_url)
  }
}

const viewAllNotifications = () => {
  router.push('/notifications')
}
</script>

<template>
  <Card 
    v-if="!isLoading && activeNotifications.length > 0"
    data-testid="alert-notifications"
  >
    <CardHeader class="pb-3">
      <CardTitle class="flex items-center gap-2 text-lg">
        <Bell class="h-5 w-5 text-primary" />
        <span>Academic Alerts</span>
        <Badge 
          v-if="urgentNotifications.length > 0"
          variant="destructive" 
          class="ml-auto"
        >
          {{ urgentNotifications.length }} urgent
        </Badge>
        <Badge v-else variant="secondary" class="ml-auto">
          {{ activeNotifications.length }}
        </Badge>
      </CardTitle>
    </CardHeader>
    
    <CardContent class="space-y-3">
      <!-- Notifications List -->
      <div class="space-y-2 max-h-80 overflow-y-auto">
        <template v-for="notification in activeNotifications.slice(0, 5)" :key="notification.id">
          <div 
            class="flex items-start gap-3 p-3 rounded-lg border transition-colors hover:bg-muted/50 cursor-pointer group"
            :class="{
              'border-destructive/50 bg-destructive/5': notification.priority === 'high',
              'border-yellow-200 bg-yellow-50': notification.priority === 'medium',
              'border-muted': notification.priority === 'low'
            }"
            @click="handleNotificationClick(notification)"
          >
            <!-- Icon -->
            <div class="flex-shrink-0 mt-0.5">
              <component 
                :is="getNotificationIcon(notification.category)" 
                class="h-4 w-4"
                :class="{
                  'text-destructive': notification.priority === 'high',
                  'text-yellow-600': notification.priority === 'medium',
                  'text-muted-foreground': notification.priority === 'low'
                }"
              />
            </div>
            
            <!-- Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between gap-2">
                <div class="space-y-1">
                  <p class="text-sm font-medium line-clamp-1">{{ notification.title }}</p>
                  <p class="text-xs text-muted-foreground line-clamp-2">{{ notification.message }}</p>
                  <div class="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>{{ formatTimeAgo(notification.created_at) }}</span>
                    <span v-if="notification.category">•</span>
                    <span v-if="notification.category" class="capitalize">{{ notification.category }}</span>
                  </div>
                </div>
                
                <!-- Priority Badge and Actions -->
                <div class="flex items-center gap-1">
                  <Badge :variant="getPriorityColor(notification.priority)" class="text-xs">
                    {{ notification.priority }}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    class="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    @click.stop="dismissNotification(notification.id)"
                  >
                    <X class="h-3 w-3" />
                  </Button>
                </div>
              </div>
              
              <!-- Action Button -->
              <div v-if="notification.action_text" class="mt-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  class="text-xs h-7"
                  @click.stop="handleNotificationClick(notification)"
                >
                  {{ notification.action_text }}
                  <ChevronRight class="h-3 w-3 ml-1" />
                </Button>
              </div>
            </div>
          </div>
        </template>
      </div>
      
      <!-- Show More Button -->
      <div v-if="activeNotifications.length > 5" class="text-center pt-2 border-t">
        <Button variant="outline" size="sm" @click="viewAllNotifications">
          View {{ activeNotifications.length - 5 }} more alerts
        </Button>
      </div>
      
      <!-- View All Button -->
      <div v-else class="text-center pt-2 border-t">
        <Button variant="outline" size="sm" @click="viewAllNotifications">
          <Bell class="h-4 w-4 mr-2" />
          View All Notifications
        </Button>
      </div>
    </CardContent>
  </Card>
  
  <!-- Empty State -->
  <Card v-else-if="!isLoading && activeNotifications.length === 0">
    <CardContent class="text-center py-8">
      <Bell class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <h3 class="text-lg font-medium mb-2">No Active Alerts</h3>
      <p class="text-sm text-muted-foreground mb-4">
        You're all caught up! Check back later for important academic updates.
      </p>
      <Button variant="outline" size="sm" @click="viewAllNotifications">
        View All Notifications
      </Button>
    </CardContent>
  </Card>
  
  <!-- Loading State -->
  <Card v-else-if="isLoading">
    <CardHeader class="pb-3">
      <div class="flex items-center gap-2">
        <div class="h-5 w-5 bg-muted animate-pulse rounded" />
        <div class="h-5 w-32 bg-muted animate-pulse rounded" />
        <div class="h-5 w-16 bg-muted animate-pulse rounded ml-auto" />
      </div>
    </CardHeader>
    <CardContent class="space-y-3">
      <div class="space-y-2">
        <div class="h-16 w-full bg-muted animate-pulse rounded" />
        <div class="h-16 w-full bg-muted animate-pulse rounded" />
        <div class="h-16 w-full bg-muted animate-pulse rounded" />
      </div>
      <div class="text-center pt-2 border-t">
        <div class="h-8 w-32 bg-muted animate-pulse rounded mx-auto" />
      </div>
    </CardContent>
  </Card>
</template>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
