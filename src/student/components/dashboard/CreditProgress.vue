<script setup lang="ts">
import { computed } from 'vue'
import { <PERSON><PERSON>uationCap, BookOpen, Clock } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Progress } from '@/shared/components/ui/progress'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/shared/components/ui/tooltip'
import type { CreditProgress } from '@/shared/types/models/student'

interface Props {
  creditProgress: CreditProgress | null
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

// Computed properties for credit progress
const completionPercentage = computed(() => {
  if (!props.creditProgress) return 0
  return Math.round((props.creditProgress.earned_credits / props.creditProgress.required_credits) * 100)
})

const remainingCredits = computed(() => {
  if (!props.creditProgress) return 0
  return props.creditProgress.required_credits - props.creditProgress.earned_credits
})

const progressColor = computed(() => {
  const percentage = completionPercentage.value
  if (percentage >= 75) return 'bg-green-500'
  if (percentage >= 50) return 'bg-blue-500'
  if (percentage >= 25) return 'bg-yellow-500'
  return 'bg-red-500'
})

const progressStatus = computed(() => {
  const percentage = completionPercentage.value
  if (percentage >= 90) return 'Nearing Graduation'
  if (percentage >= 75) return 'On Track'
  if (percentage >= 50) return 'Good Progress'
  if (percentage >= 25) return 'Early Stage'
  return 'Getting Started'
})

const formatRequirements = (requirements: string[]) => {
  if (requirements.length === 0) return 'All requirements completed!'
  if (requirements.length <= 3) return requirements.join(', ')
  return `${requirements.slice(0, 2).join(', ')} and ${requirements.length - 2} more`
}
</script>

<template>
  <Card data-testid="credit-progress">
    <CardHeader class="pb-3">
      <CardTitle class="flex items-center gap-2 text-lg">
        <GraduationCap class="h-5 w-5 text-primary" />
        <span>Credit Progress</span>
      </CardTitle>
    </CardHeader>
    
    <CardContent class="space-y-6">
      <!-- Main Progress Ring/Circle -->
      <div class="flex items-center justify-center">
        <div class="relative w-32 h-32">
          <!-- Background Circle -->
          <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
            <circle
              cx="60"
              cy="60"
              r="50"
              stroke="currentColor"
              stroke-width="8"
              fill="none"
              class="text-muted"
            />
            <!-- Progress Circle -->
            <circle
              v-if="!isLoading"
              cx="60"
              cy="60"
              r="50"
              stroke="currentColor"
              stroke-width="8"
              fill="none"
              :class="progressColor"
              stroke-linecap="round"
              :stroke-dasharray="`${completionPercentage * 3.14} 314`"
              class="transition-all duration-1000 ease-out"
            />
          </svg>
          
          <!-- Center Content -->
          <div class="absolute inset-0 flex flex-col items-center justify-center">
            <div v-if="!isLoading" class="text-center">
              <div class="text-2xl font-bold">{{ completionPercentage }}%</div>
              <div class="text-xs text-muted-foreground">Complete</div>
            </div>
            <div v-else class="space-y-1">
              <div class="h-6 w-12 bg-muted animate-pulse rounded" />
              <div class="h-3 w-16 bg-muted animate-pulse rounded" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- Progress Details -->
      <div v-if="!isLoading && creditProgress" class="space-y-4">
        <!-- Status Badge -->
        <div class="text-center">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
            {{ progressStatus }}
          </span>
        </div>
        
        <!-- Credit Numbers -->
        <div class="grid grid-cols-3 gap-4 text-center">
          <div class="space-y-1">
            <div class="text-2xl font-bold text-green-600">{{ creditProgress.earned_credits }}</div>
            <div class="text-xs text-muted-foreground">Earned</div>
          </div>
          <div class="space-y-1">
            <div class="text-2xl font-bold text-blue-600">{{ creditProgress.credits_in_progress }}</div>
            <div class="text-xs text-muted-foreground">In Progress</div>
          </div>
          <div class="space-y-1">
            <div class="text-2xl font-bold text-muted-foreground">{{ remainingCredits }}</div>
            <div class="text-xs text-muted-foreground">Remaining</div>
          </div>
        </div>
        
        <!-- Current Semester Credits -->
        <div class="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div class="flex items-center gap-2">
            <BookOpen class="h-4 w-4 text-muted-foreground" />
            <span class="text-sm font-medium">This Semester</span>
          </div>
          <span class="text-sm font-bold">{{ creditProgress.credits_this_semester }} credits</span>
        </div>
        
        <!-- Remaining Requirements -->
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div class="flex items-center justify-between p-3 bg-muted/50 rounded-lg cursor-help">
                <div class="flex items-center gap-2">
                  <Clock class="h-4 w-4 text-muted-foreground" />
                  <span class="text-sm font-medium">Requirements</span>
                </div>
                <span class="text-sm text-muted-foreground">
                  {{ creditProgress.remaining_requirements.length }} remaining
                </span>
              </div>
            </TooltipTrigger>
            <TooltipContent class="max-w-xs">
              <div class="space-y-1">
                <p class="font-medium">Missing Requirements:</p>
                <p class="text-sm">{{ formatRequirements(creditProgress.remaining_requirements) }}</p>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        <!-- Progress Bar -->
        <div class="space-y-2">
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>{{ creditProgress.earned_credits }} / {{ creditProgress.required_credits }} credits</span>
            <span>{{ completionPercentage }}%</span>
          </div>
          <Progress :value="completionPercentage" class="h-2" />
        </div>
      </div>
      
      <!-- Loading State -->
      <div v-else-if="isLoading" class="space-y-4">
        <div class="text-center">
          <div class="h-5 w-24 bg-muted animate-pulse rounded mx-auto" />
        </div>
        <div class="grid grid-cols-3 gap-4">
          <div class="space-y-1 text-center">
            <div class="h-8 w-12 bg-muted animate-pulse rounded mx-auto" />
            <div class="h-3 w-12 bg-muted animate-pulse rounded mx-auto" />
          </div>
          <div class="space-y-1 text-center">
            <div class="h-8 w-12 bg-muted animate-pulse rounded mx-auto" />
            <div class="h-3 w-16 bg-muted animate-pulse rounded mx-auto" />
          </div>
          <div class="space-y-1 text-center">
            <div class="h-8 w-12 bg-muted animate-pulse rounded mx-auto" />
            <div class="h-3 w-16 bg-muted animate-pulse rounded mx-auto" />
          </div>
        </div>
        <div class="space-y-2">
          <div class="h-12 w-full bg-muted animate-pulse rounded" />
          <div class="h-12 w-full bg-muted animate-pulse rounded" />
          <div class="h-2 w-full bg-muted animate-pulse rounded" />
        </div>
      </div>
    </CardContent>
  </Card>
</template>
