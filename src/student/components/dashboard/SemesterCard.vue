<script setup lang="ts">
import { computed, ref } from 'vue'
import { Calendar, ExternalLink, Clock } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/shared/components/ui/dialog'
import type { Semester } from '@/shared/types/models/student'

interface Props {
  semester: Semester | null
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

const isModalOpen = ref(false)

// Computed properties for semester information
const semesterProgress = computed(() => {
  if (!props.semester) return 0
  
  const start = new Date(props.semester.start_date)
  const end = new Date(props.semester.end_date)
  const now = new Date()
  
  if (now < start) return 0
  if (now > end) return 100
  
  const total = end.getTime() - start.getTime()
  const elapsed = now.getTime() - start.getTime()
  
  return Math.round((elapsed / total) * 100)
})

const daysRemaining = computed(() => {
  if (!props.semester) return 0
  
  const end = new Date(props.semester.end_date)
  const now = new Date()
  
  if (now > end) return 0
  
  const diffTime = end.getTime() - now.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

const semesterStatus = computed(() => {
  if (!props.semester) return 'unknown'
  
  const now = new Date()
  const start = new Date(props.semester.start_date)
  const end = new Date(props.semester.end_date)
  
  if (now < start) return 'upcoming'
  if (now > end) return 'completed'
  return 'active'
})

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const openCalendar = () => {
  if (props.semester?.academic_calendar_url) {
    window.open(props.semester.academic_calendar_url, '_blank')
  } else {
    isModalOpen.value = true
  }
}
</script>

<template>
  <Card 
    class="cursor-pointer transition-all duration-200 hover:shadow-md"
    @click="openCalendar"
    data-testid="semester-card"
  >
    <CardHeader class="pb-3">
      <div class="flex items-center justify-between">
        <CardTitle class="flex items-center gap-2 text-lg">
          <Calendar class="h-5 w-5 text-primary" />
          <span v-if="!isLoading">{{ semester?.name || 'Current Semester' }}</span>
          <div v-else class="h-5 w-32 bg-muted animate-pulse rounded" />
        </CardTitle>
        
        <Badge 
          v-if="!isLoading && semester"
          :variant="semesterStatus === 'active' ? 'default' : 'secondary'"
          class="capitalize"
        >
          {{ semesterStatus }}
        </Badge>
        <div v-else-if="isLoading" class="h-5 w-16 bg-muted animate-pulse rounded" />
      </div>
    </CardHeader>
    
    <CardContent class="space-y-4">
      <!-- Date Range -->
      <div v-if="!isLoading && semester" class="flex items-center justify-between text-sm">
        <div class="flex items-center gap-1 text-muted-foreground">
          <Clock class="h-4 w-4" />
          <span>{{ formatDate(semester.start_date) }} - {{ formatDate(semester.end_date) }}</span>
        </div>
        <span v-if="daysRemaining > 0" class="text-muted-foreground">
          {{ daysRemaining }} days left
        </span>
      </div>
      <div v-else-if="isLoading" class="space-y-2">
        <div class="h-4 w-full bg-muted animate-pulse rounded" />
        <div class="h-4 w-24 bg-muted animate-pulse rounded" />
      </div>
      
      <!-- Progress Bar -->
      <div v-if="!isLoading && semester && semesterStatus === 'active'" class="space-y-2">
        <div class="flex justify-between text-xs text-muted-foreground">
          <span>Semester Progress</span>
          <span>{{ semesterProgress }}%</span>
        </div>
        <div class="w-full bg-muted rounded-full h-2">
          <div 
            class="bg-primary h-2 rounded-full transition-all duration-300"
            :style="{ width: `${semesterProgress}%` }"
          />
        </div>
      </div>
      <div v-else-if="isLoading" class="space-y-2">
        <div class="h-3 w-full bg-muted animate-pulse rounded" />
        <div class="h-2 w-full bg-muted animate-pulse rounded" />
      </div>
      
      <!-- Academic Year -->
      <div v-if="!isLoading && semester" class="text-xs text-muted-foreground">
        Academic Year: {{ semester.academic_year }}
      </div>
      <div v-else-if="isLoading" class="h-3 w-32 bg-muted animate-pulse rounded" />
    </CardContent>
  </Card>

  <!-- Academic Calendar Modal -->
  <Dialog v-model:open="isModalOpen">
    <DialogContent class="max-w-md">
      <DialogHeader>
        <DialogTitle>Academic Calendar</DialogTitle>
      </DialogHeader>
      <div class="space-y-4">
        <p class="text-sm text-muted-foreground">
          View the complete academic calendar for important dates and deadlines.
        </p>
        <div class="flex gap-2">
          <Button 
            v-if="semester?.academic_calendar_url"
            @click="() => window.open(semester.academic_calendar_url, '_blank')"
            class="flex-1"
          >
            <ExternalLink class="h-4 w-4 mr-2" />
            Open Calendar
          </Button>
          <Button variant="outline" @click="isModalOpen = false" class="flex-1">
            Close
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
