<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Progress } from '@/shared/components/ui/progress'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/shared/components/ui/collapsible'
import { 
  BookOpen, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  ChevronDown,
  ChevronRight,
  Award,
  Target
} from 'lucide-vue-next'
import type { AcademicRecord, AssessmentScore } from '../../types/models/academic'
import type { Semester } from '../../types/models/student'

interface Props {
  records: AcademicRecord[]
  assessmentScores: AssessmentScore[]
  semesterSummaries: Array<{
    semester: Semester
    total_credits: number
    semester_gpa: number
    units_completed: number
  }>
  selectedSemester?: string
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Computed properties
const filteredRecords = computed(() => {
  if (!props.selectedSemester || props.selectedSemester === 'all') {
    return props.records
  }
  return props.records.filter(record => record.semester_id === props.selectedSemester)
})

const recordsBySemester = computed(() => {
  const grouped: Record<string, AcademicRecord[]> = {}
  
  filteredRecords.value.forEach(record => {
    const semesterId = record.semester_id
    if (!grouped[semesterId]) {
      grouped[semesterId] = []
    }
    grouped[semesterId].push(record)
  })
  
  return grouped
})

const getSemesterSummary = (semesterId: string) => {
  return props.semesterSummaries.find(summary => summary.semester.id === semesterId)
}

const getAssessmentScores = (courseOfferingId: string) => {
  return props.assessmentScores.filter(score => score.course_offering_id === courseOfferingId)
}

// Mock grade calculation (in real app, this would come from API)
const calculateGrade = (record: AcademicRecord) => {
  const scores = getAssessmentScores(record.course_offering_id)
  if (scores.length === 0) return { letter: 'N/A', points: 0, percentage: 0 }
  
  // Mock calculation
  const mockGrades = ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D', 'F']
  const mockPoints = [4.0, 4.0, 3.7, 3.3, 3.0, 2.7, 2.3, 2.0, 1.7, 1.0, 0.0]
  const index = Math.floor(Math.random() * 8) // Bias towards better grades
  
  return {
    letter: mockGrades[index],
    points: mockPoints[index],
    percentage: Math.floor(Math.random() * 20) + 75 // 75-95%
  }
}

const getGradeColor = (letter: string) => {
  if (['A+', 'A', 'A-'].includes(letter)) return 'text-green-600'
  if (['B+', 'B', 'B-'].includes(letter)) return 'text-blue-600'
  if (['C+', 'C', 'C-'].includes(letter)) return 'text-yellow-600'
  if (['D'].includes(letter)) return 'text-orange-600'
  if (['F'].includes(letter)) return 'text-red-600'
  return 'text-muted-foreground'
}

const getGradeBadgeVariant = (letter: string) => {
  if (['A+', 'A', 'A-'].includes(letter)) return 'default'
  if (['B+', 'B', 'B-'].includes(letter)) return 'secondary'
  if (['C+', 'C', 'C-'].includes(letter)) return 'outline'
  return 'destructive'
}

const getTrendIcon = (gpa: number) => {
  if (gpa >= 3.5) return TrendingUp
  if (gpa >= 2.5) return Minus
  return TrendingDown
}

const getTrendColor = (gpa: number) => {
  if (gpa >= 3.5) return 'text-green-600'
  if (gpa >= 2.5) return 'text-yellow-600'
  return 'text-red-600'
}
</script>

<template>
  <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="h-32 bg-muted animate-pulse rounded-lg" />
    </div>

    <!-- Grades by Semester -->
    <div v-else-if="Object.keys(recordsBySemester).length > 0" class="space-y-4">
      <div
        v-for="(records, semesterId) in recordsBySemester"
        :key="semesterId"
        class="space-y-3"
      >
        <!-- Semester Header -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <h3 class="text-lg font-semibold">
              {{ records[0]?.semester.name || 'Unknown Semester' }}
            </h3>
            <Badge variant="outline">
              {{ records.length }} {{ records.length === 1 ? 'Unit' : 'Units' }}
            </Badge>
          </div>
          
          <!-- Semester Summary -->
          <div v-if="getSemesterSummary(semesterId)" class="flex items-center gap-4 text-sm">
            <div class="flex items-center gap-1">
              <Award class="h-4 w-4 text-muted-foreground" />
              <span>GPA: {{ getSemesterSummary(semesterId)?.semester_gpa.toFixed(2) || 'N/A' }}</span>
            </div>
            <div class="flex items-center gap-1">
              <Target class="h-4 w-4 text-muted-foreground" />
              <span>{{ getSemesterSummary(semesterId)?.total_credits || 0 }} Credits</span>
            </div>
          </div>
        </div>

        <!-- Course Records -->
        <div class="grid gap-3">
          <Card
            v-for="record in records"
            :key="record.id"
            class="transition-all duration-200 hover:shadow-md"
          >
            <Collapsible>
              <CollapsibleTrigger as-child>
                <CardHeader class="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <ChevronRight class="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
                      <div>
                        <CardTitle class="text-base">
                          {{ record.unit.code }} - {{ record.unit.name }}
                        </CardTitle>
                        <div class="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                          <BookOpen class="h-4 w-4" />
                          <span>{{ record.unit.credit_points }} Credit Points</span>
                        </div>
                      </div>
                    </div>
                    
                    <div class="flex items-center gap-3">
                      <!-- Grade Display -->
                      <div class="text-right">
                        <Badge 
                          :variant="getGradeBadgeVariant(calculateGrade(record).letter)"
                          class="text-sm font-bold"
                        >
                          {{ calculateGrade(record).letter }}
                        </Badge>
                        <div class="text-xs text-muted-foreground mt-1">
                          {{ calculateGrade(record).percentage }}%
                        </div>
                      </div>
                      
                      <!-- GPA Points -->
                      <div class="text-right">
                        <div :class="['font-semibold', getGradeColor(calculateGrade(record).letter)]">
                          {{ calculateGrade(record).points.toFixed(1) }}
                        </div>
                        <div class="text-xs text-muted-foreground">
                          GPA Points
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              
              <CollapsibleContent>
                <CardContent class="pt-0">
                  <!-- Assessment Breakdown -->
                  <div class="space-y-3">
                    <h4 class="font-medium text-sm">Assessment Breakdown</h4>
                    
                    <!-- Mock Assessment Items -->
                    <div class="space-y-2">
                      <div
                        v-for="(assessment, index) in ['Assignment 1', 'Quiz 1', 'Midterm Exam', 'Final Project']"
                        :key="index"
                        class="flex items-center justify-between p-2 bg-muted/30 rounded"
                      >
                        <span class="text-sm">{{ assessment }}</span>
                        <div class="flex items-center gap-2">
                          <Progress 
                            :value="Math.floor(Math.random() * 30) + 70" 
                            class="w-20 h-2"
                          />
                          <span class="text-sm font-medium w-12 text-right">
                            {{ Math.floor(Math.random() * 30) + 70 }}%
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Overall Performance -->
                    <div class="pt-2 border-t">
                      <div class="flex items-center justify-between">
                        <span class="font-medium">Overall Performance</span>
                        <div class="flex items-center gap-2">
                          <Progress 
                            :value="calculateGrade(record).percentage" 
                            class="w-24 h-2"
                          />
                          <span class="font-bold">
                            {{ calculateGrade(record).percentage }}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <BookOpen class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <p class="text-muted-foreground">No grades available</p>
      <p class="text-sm text-muted-foreground">
        Grades will appear here once they are posted by your instructors
      </p>
    </div>
  </div>
</template>
