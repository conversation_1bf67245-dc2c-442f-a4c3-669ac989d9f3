<script setup lang="ts">
import { computed } from 'vue'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Progress } from '@/shared/components/ui/progress'
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Award,
  Target,
  BarChart3
} from 'lucide-vue-next'
import type { GPACalculation, Semester } from '../../types/models/student'

interface Props {
  gpaCalculations: GPACalculation[]
  semesterSummaries: Array<{
    semester: Semester
    total_credits: number
    semester_gpa: number
    units_completed: number
  }>
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Mock GPA history data (in real app, this would come from API)
const mockGPAHistory = computed(() => {
  return props.semesterSummaries.map((summary, index) => ({
    semester: summary.semester.name,
    semesterGPA: summary.semester_gpa,
    cumulativeGPA: Math.max(2.0, summary.semester_gpa - (index * 0.1)), // Mock cumulative trend
    credits: summary.total_credits,
    unitsCompleted: summary.units_completed,
    trend: index === 0 ? 'stable' : 
           summary.semester_gpa > (props.semesterSummaries[index - 1]?.semester_gpa || 0) ? 'up' : 'down'
  }))
})

const currentGPA = computed(() => {
  if (mockGPAHistory.value.length === 0) return 0
  return mockGPAHistory.value[mockGPAHistory.value.length - 1].cumulativeGPA
})

const gpaStatus = computed(() => {
  const gpa = currentGPA.value
  if (gpa >= 3.7) return { label: 'Excellent', color: 'text-green-600', variant: 'default' as const }
  if (gpa >= 3.0) return { label: 'Good', color: 'text-blue-600', variant: 'secondary' as const }
  if (gpa >= 2.5) return { label: 'Satisfactory', color: 'text-yellow-600', variant: 'outline' as const }
  return { label: 'Needs Improvement', color: 'text-red-600', variant: 'destructive' as const }
})

const totalCredits = computed(() => {
  return mockGPAHistory.value.reduce((sum, item) => sum + item.credits, 0)
})

const getTrendIcon = (trend: string) => {
  switch (trend) {
    case 'up': return TrendingUp
    case 'down': return TrendingDown
    default: return Minus
  }
}

const getTrendColor = (trend: string) => {
  switch (trend) {
    case 'up': return 'text-green-600'
    case 'down': return 'text-red-600'
    default: return 'text-muted-foreground'
  }
}

const getGPABarWidth = (gpa: number) => {
  return Math.min((gpa / 4.0) * 100, 100)
}

const getGPABarColor = (gpa: number) => {
  if (gpa >= 3.5) return 'bg-green-500'
  if (gpa >= 3.0) return 'bg-blue-500'
  if (gpa >= 2.5) return 'bg-yellow-500'
  return 'bg-red-500'
}
</script>

<template>
  <div class="space-y-6">
    <!-- Current GPA Summary -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Award class="h-5 w-5" />
          Academic Performance Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="space-y-4">
          <div class="h-8 bg-muted animate-pulse rounded" />
          <div class="h-4 bg-muted animate-pulse rounded w-3/4" />
          <div class="h-4 bg-muted animate-pulse rounded w-1/2" />
        </div>
        
        <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Current GPA -->
          <div class="text-center">
            <div :class="['text-3xl font-bold', gpaStatus.color]">
              {{ currentGPA.toFixed(2) }}
            </div>
            <div class="text-sm text-muted-foreground mb-2">Cumulative GPA</div>
            <Badge :variant="gpaStatus.variant">
              {{ gpaStatus.label }}
            </Badge>
          </div>
          
          <!-- Total Credits -->
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">
              {{ totalCredits }}
            </div>
            <div class="text-sm text-muted-foreground mb-2">Total Credits</div>
            <div class="flex items-center justify-center gap-1 text-sm">
              <Target class="h-4 w-4 text-muted-foreground" />
              <span>{{ Math.floor(totalCredits / 120 * 100) }}% Complete</span>
            </div>
          </div>
          
          <!-- Units Completed -->
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600">
              {{ mockGPAHistory.reduce((sum, item) => sum + item.unitsCompleted, 0) }}
            </div>
            <div class="text-sm text-muted-foreground mb-2">Units Completed</div>
            <Progress 
              :value="Math.floor(mockGPAHistory.reduce((sum, item) => sum + item.unitsCompleted, 0) / 24 * 100)" 
              class="w-full h-2"
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- GPA History Chart -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <BarChart3 class="h-5 w-5" />
          GPA History
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="space-y-3">
          <div v-for="i in 4" :key="i" class="h-16 bg-muted animate-pulse rounded" />
        </div>
        
        <div v-else-if="mockGPAHistory.length > 0" class="space-y-4">
          <!-- Chart Visualization -->
          <div class="space-y-3">
            <div
              v-for="(item, index) in mockGPAHistory"
              :key="index"
              class="flex items-center gap-4 p-3 bg-muted/30 rounded-lg"
            >
              <!-- Semester Info -->
              <div class="min-w-0 flex-1">
                <div class="flex items-center gap-2">
                  <span class="font-medium">{{ item.semester }}</span>
                  <component 
                    :is="getTrendIcon(item.trend)" 
                    :class="['h-4 w-4', getTrendColor(item.trend)]"
                  />
                </div>
                <div class="text-sm text-muted-foreground">
                  {{ item.credits }} credits • {{ item.unitsCompleted }} units
                </div>
              </div>
              
              <!-- GPA Bars -->
              <div class="flex-1 max-w-xs space-y-2">
                <!-- Semester GPA -->
                <div class="flex items-center gap-2">
                  <span class="text-xs w-16">Semester:</span>
                  <div class="flex-1 bg-muted rounded-full h-2 relative">
                    <div 
                      :class="['h-2 rounded-full transition-all duration-300', getGPABarColor(item.semesterGPA)]"
                      :style="{ width: `${getGPABarWidth(item.semesterGPA)}%` }"
                    />
                  </div>
                  <span class="text-sm font-medium w-8">{{ item.semesterGPA.toFixed(1) }}</span>
                </div>
                
                <!-- Cumulative GPA -->
                <div class="flex items-center gap-2">
                  <span class="text-xs w-16">Overall:</span>
                  <div class="flex-1 bg-muted rounded-full h-2 relative">
                    <div 
                      :class="['h-2 rounded-full transition-all duration-300', getGPABarColor(item.cumulativeGPA)]"
                      :style="{ width: `${getGPABarWidth(item.cumulativeGPA)}%` }"
                    />
                  </div>
                  <span class="text-sm font-medium w-8">{{ item.cumulativeGPA.toFixed(1) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- GPA Scale Reference -->
          <div class="pt-4 border-t">
            <div class="text-sm font-medium mb-2">GPA Scale Reference</div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-green-500 rounded-full" />
                <span>3.5+ Excellent</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-blue-500 rounded-full" />
                <span>3.0+ Good</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-yellow-500 rounded-full" />
                <span>2.5+ Satisfactory</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-3 h-3 bg-red-500 rounded-full" />
                <span>&lt;2.5 Needs Improvement</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Empty State -->
        <div v-else class="text-center py-8">
          <BarChart3 class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p class="text-muted-foreground">No GPA history available</p>
          <p class="text-sm text-muted-foreground">
            Complete your first semester to see your GPA trends
          </p>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
