<script setup lang="ts">
import { computed } from 'vue'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Progress } from '@/shared/components/ui/progress'
import { But<PERSON> } from '@/shared/components/ui/button'
import { 
  FileText, 
  Calendar, 
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Eye,
  Download
} from 'lucide-vue-next'
import type { AssessmentScore, AssessmentComponentDetail } from '../../types/models/academic'

interface Props {
  assessmentScores: AssessmentScore[]
  upcomingAssessments: AssessmentComponentDetail[]
  courseOfferingId?: string
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Mock assessment data (in real app, this would come from API)
const mockAssessments = computed(() => {
  const assessments = [
    {
      id: '1',
      title: 'Assignment 1: Research Paper',
      type: 'assignment',
      dueDate: '2024-03-15',
      submittedDate: '2024-03-14',
      status: 'graded',
      score: 85,
      maxScore: 100,
      weight: 25,
      feedback: 'Excellent research and analysis. Well-structured arguments.',
      grade: 'A-'
    },
    {
      id: '2',
      title: 'Quiz 1: Chapter 1-3',
      type: 'quiz',
      dueDate: '2024-03-08',
      submittedDate: '2024-03-08',
      status: 'graded',
      score: 92,
      maxScore: 100,
      weight: 10,
      feedback: 'Great understanding of the concepts.',
      grade: 'A'
    },
    {
      id: '3',
      title: 'Midterm Examination',
      type: 'exam',
      dueDate: '2024-03-22',
      submittedDate: '2024-03-22',
      status: 'graded',
      score: 78,
      maxScore: 100,
      weight: 30,
      feedback: 'Good performance overall. Review section 4.2 for improvement.',
      grade: 'B+'
    },
    {
      id: '4',
      title: 'Final Project',
      type: 'project',
      dueDate: '2024-04-15',
      submittedDate: null,
      status: 'pending',
      score: null,
      maxScore: 100,
      weight: 35,
      feedback: null,
      grade: null
    }
  ]
  
  return assessments
})

const completedAssessments = computed(() => {
  return mockAssessments.value.filter(a => a.status === 'graded')
})

const pendingAssessments = computed(() => {
  return mockAssessments.value.filter(a => a.status === 'pending')
})

const overallScore = computed(() => {
  const completed = completedAssessments.value
  if (completed.length === 0) return 0
  
  const weightedSum = completed.reduce((sum, assessment) => {
    return sum + (assessment.score! / assessment.maxScore * assessment.weight)
  }, 0)
  
  const totalWeight = completed.reduce((sum, assessment) => sum + assessment.weight, 0)
  
  return totalWeight > 0 ? (weightedSum / totalWeight) * 100 : 0
})

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'graded': return CheckCircle
    case 'submitted': return Clock
    case 'pending': return AlertCircle
    case 'overdue': return XCircle
    default: return FileText
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'graded': return 'text-green-600'
    case 'submitted': return 'text-blue-600'
    case 'pending': return 'text-yellow-600'
    case 'overdue': return 'text-red-600'
    default: return 'text-muted-foreground'
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'graded': return 'default' as const
    case 'submitted': return 'secondary' as const
    case 'pending': return 'outline' as const
    case 'overdue': return 'destructive' as const
    default: return 'outline' as const
  }
}

const getGradeColor = (grade: string | null) => {
  if (!grade) return 'text-muted-foreground'
  if (['A+', 'A', 'A-'].includes(grade)) return 'text-green-600'
  if (['B+', 'B', 'B-'].includes(grade)) return 'text-blue-600'
  if (['C+', 'C', 'C-'].includes(grade)) return 'text-yellow-600'
  return 'text-red-600'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'assignment': return FileText
    case 'quiz': return CheckCircle
    case 'exam': return AlertCircle
    case 'project': return Eye
    default: return FileText
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- Overall Progress -->
    <Card>
      <CardHeader>
        <CardTitle>Assessment Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="space-y-4">
          <div class="h-8 bg-muted animate-pulse rounded" />
          <div class="h-4 bg-muted animate-pulse rounded w-3/4" />
        </div>
        
        <div v-else class="space-y-4">
          <!-- Overall Score -->
          <div class="flex items-center justify-between">
            <span class="font-medium">Current Grade</span>
            <div class="flex items-center gap-2">
              <Progress :value="overallScore" class="w-32 h-2" />
              <span class="font-bold text-lg">{{ overallScore.toFixed(1) }}%</span>
            </div>
          </div>
          
          <!-- Progress Stats -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div class="text-2xl font-bold text-green-600">{{ completedAssessments.length }}</div>
              <div class="text-sm text-muted-foreground">Completed</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-yellow-600">{{ pendingAssessments.length }}</div>
              <div class="text-sm text-muted-foreground">Pending</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-blue-600">
                {{ completedAssessments.reduce((sum, a) => sum + a.weight, 0) }}%
              </div>
              <div class="text-sm text-muted-foreground">Weight Complete</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-purple-600">
                {{ (completedAssessments.reduce((sum, a) => sum + (a.score! / a.maxScore), 0) / completedAssessments.length * 100).toFixed(0) }}%
              </div>
              <div class="text-sm text-muted-foreground">Avg Score</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Assessment List -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Assessment Details</h3>
      
      <div v-if="isLoading" class="space-y-3">
        <div v-for="i in 4" :key="i" class="h-24 bg-muted animate-pulse rounded-lg" />
      </div>
      
      <div v-else class="space-y-3">
        <Card
          v-for="assessment in mockAssessments"
          :key="assessment.id"
          class="transition-all duration-200 hover:shadow-md"
        >
          <CardContent class="p-4">
            <div class="flex items-start justify-between">
              <!-- Assessment Info -->
              <div class="flex items-start gap-3 flex-1">
                <component 
                  :is="getTypeIcon(assessment.type)" 
                  class="h-5 w-5 text-muted-foreground mt-0.5" 
                />
                <div class="min-w-0 flex-1">
                  <h4 class="font-medium">{{ assessment.title }}</h4>
                  <div class="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                    <div class="flex items-center gap-1">
                      <Calendar class="h-4 w-4" />
                      <span>Due: {{ formatDate(assessment.dueDate) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span>Weight: {{ assessment.weight }}%</span>
                    </div>
                  </div>
                  
                  <!-- Feedback -->
                  <div v-if="assessment.feedback" class="mt-2 p-2 bg-muted/50 rounded text-sm">
                    <strong>Feedback:</strong> {{ assessment.feedback }}
                  </div>
                </div>
              </div>
              
              <!-- Score and Status -->
              <div class="flex items-center gap-3 shrink-0">
                <!-- Score -->
                <div v-if="assessment.score !== null" class="text-right">
                  <div class="font-bold text-lg">
                    {{ assessment.score }}/{{ assessment.maxScore }}
                  </div>
                  <div :class="['text-sm font-medium', getGradeColor(assessment.grade)]">
                    {{ assessment.grade }}
                  </div>
                </div>
                
                <!-- Status -->
                <div class="flex flex-col items-center gap-2">
                  <Badge :variant="getStatusVariant(assessment.status)">
                    <component 
                      :is="getStatusIcon(assessment.status)" 
                      class="h-3 w-3 mr-1" 
                    />
                    {{ assessment.status.charAt(0).toUpperCase() + assessment.status.slice(1) }}
                  </Badge>
                  
                  <!-- Actions -->
                  <div class="flex gap-1">
                    <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                      <Download class="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && mockAssessments.length === 0" class="text-center py-12">
      <FileText class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <p class="text-muted-foreground">No assessments available</p>
      <p class="text-sm text-muted-foreground">
        Assessment details will appear here once they are created
      </p>
    </div>
  </div>
</template>
