<script setup lang="ts">
import { computed } from 'vue'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { 
  AlertT<PERSON>gle, 
  Clock, 
  MapPin, 
  X,
  Info
} from 'lucide-vue-next'
import type { CourseOffering } from '../../types/models/course'

interface Conflict {
  id: string
  type: 'time' | 'prerequisite' | 'corequisite' | 'capacity'
  severity: 'error' | 'warning' | 'info'
  offering1: CourseOffering
  offering2?: CourseOffering
  message: string
  details?: string
  canResolve?: boolean
}

interface Props {
  conflicts: Conflict[]
  onResolve?: (conflictId: string) => void
  onIgnore?: (conflictId: string) => void
}

const props = defineProps<Props>()

// Computed properties
const errorConflicts = computed(() => 
  props.conflicts.filter(c => c.severity === 'error')
)

const warningConflicts = computed(() => 
  props.conflicts.filter(c => c.severity === 'warning')
)

const infoConflicts = computed(() => 
  props.conflicts.filter(c => c.severity === 'info')
)

const hasBlockingConflicts = computed(() => 
  errorConflicts.value.length > 0
)

const getConflictIcon = (type: string) => {
  switch (type) {
    case 'time':
      return Clock
    case 'prerequisite':
    case 'corequisite':
      return Info
    case 'capacity':
      return AlertTriangle
    default:
      return AlertTriangle
  }
}

const getConflictVariant = (severity: string) => {
  switch (severity) {
    case 'error':
      return 'destructive'
    case 'warning':
      return 'default'
    case 'info':
      return 'secondary'
    default:
      return 'default'
  }
}

// Actions
const handleResolve = (conflictId: string) => {
  if (props.onResolve) {
    props.onResolve(conflictId)
  }
}

const handleIgnore = (conflictId: string) => {
  if (props.onIgnore) {
    props.onIgnore(conflictId)
  }
}
</script>

<template>
  <div v-if="conflicts.length > 0" class="space-y-4">
    <!-- Error Conflicts -->
    <div v-if="errorConflicts.length > 0" class="space-y-3">
      <div class="flex items-center gap-2">
        <AlertTriangle class="h-5 w-5 text-destructive" />
        <h3 class="font-semibold text-destructive">Registration Blocked</h3>
        <Badge variant="destructive">{{ errorConflicts.length }}</Badge>
      </div>
      
      <div class="space-y-2">
        <Alert
          v-for="conflict in errorConflicts"
          :key="conflict.id"
          variant="destructive"
        >
          <component :is="getConflictIcon(conflict.type)" class="h-4 w-4" />
          <AlertDescription class="space-y-2">
            <div class="font-medium">{{ conflict.message }}</div>
            <div v-if="conflict.details" class="text-sm opacity-90">
              {{ conflict.details }}
            </div>
            
            <!-- Course Information -->
            <div class="flex flex-wrap gap-2 text-sm">
              <Badge variant="outline" class="text-xs">
                {{ conflict.offering1.curriculum_unit.unit.code }}
              </Badge>
              <span v-if="conflict.offering2">conflicts with</span>
              <Badge v-if="conflict.offering2" variant="outline" class="text-xs">
                {{ conflict.offering2.curriculum_unit.unit.code }}
              </Badge>
            </div>

            <!-- Action Buttons -->
            <div v-if="conflict.canResolve" class="flex gap-2 pt-2">
              <Button
                size="sm"
                variant="outline"
                @click="handleResolve(conflict.id)"
                class="h-8"
              >
                Resolve
              </Button>
              <Button
                size="sm"
                variant="ghost"
                @click="handleIgnore(conflict.id)"
                class="h-8"
              >
                <X class="h-3 w-3 mr-1" />
                Ignore
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>

    <!-- Warning Conflicts -->
    <div v-if="warningConflicts.length > 0" class="space-y-3">
      <div class="flex items-center gap-2">
        <AlertTriangle class="h-5 w-5 text-yellow-600" />
        <h3 class="font-semibold text-yellow-700">Warnings</h3>
        <Badge variant="secondary">{{ warningConflicts.length }}</Badge>
      </div>
      
      <div class="space-y-2">
        <Alert
          v-for="conflict in warningConflicts"
          :key="conflict.id"
          class="border-yellow-200 bg-yellow-50"
        >
          <component :is="getConflictIcon(conflict.type)" class="h-4 w-4 text-yellow-600" />
          <AlertDescription class="space-y-2">
            <div class="font-medium text-yellow-800">{{ conflict.message }}</div>
            <div v-if="conflict.details" class="text-sm text-yellow-700">
              {{ conflict.details }}
            </div>
            
            <!-- Course Information -->
            <div class="flex flex-wrap gap-2 text-sm">
              <Badge variant="outline" class="text-xs">
                {{ conflict.offering1.curriculum_unit.unit.code }}
              </Badge>
              <span v-if="conflict.offering2">conflicts with</span>
              <Badge v-if="conflict.offering2" variant="outline" class="text-xs">
                {{ conflict.offering2.curriculum_unit.unit.code }}
              </Badge>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-2 pt-2">
              <Button
                size="sm"
                variant="outline"
                @click="handleIgnore(conflict.id)"
                class="h-8"
              >
                <X class="h-3 w-3 mr-1" />
                Dismiss
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>

    <!-- Info Conflicts -->
    <div v-if="infoConflicts.length > 0" class="space-y-3">
      <div class="flex items-center gap-2">
        <Info class="h-5 w-5 text-blue-600" />
        <h3 class="font-semibold text-blue-700">Information</h3>
        <Badge variant="secondary">{{ infoConflicts.length }}</Badge>
      </div>
      
      <div class="space-y-2">
        <Alert
          v-for="conflict in infoConflicts"
          :key="conflict.id"
          class="border-blue-200 bg-blue-50"
        >
          <component :is="getConflictIcon(conflict.type)" class="h-4 w-4 text-blue-600" />
          <AlertDescription class="space-y-2">
            <div class="font-medium text-blue-800">{{ conflict.message }}</div>
            <div v-if="conflict.details" class="text-sm text-blue-700">
              {{ conflict.details }}
            </div>
            
            <!-- Course Information -->
            <div class="flex flex-wrap gap-2 text-sm">
              <Badge variant="outline" class="text-xs">
                {{ conflict.offering1.curriculum_unit.unit.code }}
              </Badge>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-2 pt-2">
              <Button
                size="sm"
                variant="outline"
                @click="handleIgnore(conflict.id)"
                class="h-8"
              >
                <X class="h-3 w-3 mr-1" />
                Dismiss
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>

    <!-- Summary -->
    <div v-if="hasBlockingConflicts" class="p-4 bg-destructive/10 rounded-lg border border-destructive/20">
      <div class="flex items-center gap-2 text-destructive">
        <AlertTriangle class="h-4 w-4" />
        <span class="font-medium">Registration cannot proceed</span>
      </div>
      <p class="text-sm text-destructive/80 mt-1">
        Please resolve all error conflicts before submitting your registration.
      </p>
    </div>
  </div>
</template>
