<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { 
  Clock, 
  MapPin, 
  Users, 
  BookOpen, 
  AlertTriangle,
  CheckCircle,
  Plus,
  Minus
} from 'lucide-vue-next'
import type { CourseOffering } from '../../types/models/course'

interface Props {
  offering: CourseOffering
  isSelected?: boolean
  isRegistered?: boolean
  hasConflicts?: boolean
  isRegistrationOpen?: boolean
  onAdd?: (offering: CourseOffering) => void
  onRemove?: (offering: CourseOffering) => void
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  isRegistered: false,
  hasConflicts: false,
  isRegistrationOpen: true,
})

// Computed properties
const statusColor = computed(() => {
  if (props.isRegistered) return 'default'
  if (props.hasConflicts) return 'destructive'
  if (props.isSelected) return 'secondary'
  return 'outline'
})

const statusText = computed(() => {
  if (props.isRegistered) return 'Registered'
  if (props.hasConflicts) return 'Conflict'
  if (props.isSelected) return 'Selected'
  return 'Available'
})

const canAdd = computed(() => {
  return props.isRegistrationOpen && !props.isRegistered && !props.isSelected && !props.hasConflicts
})

const canRemove = computed(() => {
  return props.isSelected && !props.isRegistered
})

// Format class sessions for display
const formatClassSessions = computed(() => {
  if (!props.offering.class_sessions?.length) return 'TBA'
  
  // Group sessions by day and time (simplified for now)
  return props.offering.class_sessions.map(session => {
    return `${session.room.name} (${session.room.campus.name})`
  }).join(', ')
})

// Actions
const handleAdd = () => {
  if (canAdd.value && props.onAdd) {
    props.onAdd(props.offering)
  }
}

const handleRemove = () => {
  if (canRemove.value && props.onRemove) {
    props.onRemove(props.offering)
  }
}
</script>

<template>
  <Card 
    :class="[
      'transition-all duration-200 hover:shadow-md',
      {
        'ring-2 ring-primary': isSelected,
        'ring-2 ring-destructive': hasConflicts,
        'opacity-75': isRegistered
      }
    ]"
  >
    <CardHeader class="pb-3">
      <div class="flex items-start justify-between">
        <div class="space-y-1">
          <CardTitle class="text-lg">
            {{ offering.curriculum_unit.unit.code }} - {{ offering.curriculum_unit.unit.name }}
          </CardTitle>
          <div class="flex items-center gap-2 text-sm text-muted-foreground">
            <BookOpen class="h-4 w-4" />
            <span>{{ offering.curriculum_unit.unit.credit_points }} Credit Points</span>
            <span>•</span>
            <span>{{ offering.semester.name }}</span>
          </div>
        </div>
        <Badge :variant="statusColor" class="shrink-0">
          {{ statusText }}
        </Badge>
      </div>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- Lecturer Information -->
      <div class="flex items-center gap-2 text-sm">
        <Users class="h-4 w-4 text-muted-foreground" />
        <span>{{ offering.lecturer.employee_id }} - {{ offering.lecturer.email }}</span>
      </div>

      <!-- Class Sessions -->
      <div class="flex items-start gap-2 text-sm">
        <Clock class="h-4 w-4 text-muted-foreground mt-0.5" />
        <div>
          <div class="font-medium">Class Sessions</div>
          <div class="text-muted-foreground">{{ formatClassSessions }}</div>
        </div>
      </div>

      <!-- Location -->
      <div class="flex items-center gap-2 text-sm">
        <MapPin class="h-4 w-4 text-muted-foreground" />
        <span>{{ offering.lecturer.campus.name }} Campus</span>
      </div>

      <!-- Conflicts Warning -->
      <div v-if="hasConflicts" class="flex items-center gap-2 p-2 bg-destructive/10 rounded-md">
        <AlertTriangle class="h-4 w-4 text-destructive" />
        <span class="text-sm text-destructive">Schedule conflict detected</span>
      </div>

      <!-- Registration Status -->
      <div v-if="isRegistered" class="flex items-center gap-2 p-2 bg-green-50 rounded-md">
        <CheckCircle class="h-4 w-4 text-green-600" />
        <span class="text-sm text-green-700">Successfully registered</span>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-2 pt-2">
        <Button
          v-if="canAdd"
          @click="handleAdd"
          size="sm"
          class="flex-1"
        >
          <Plus class="h-4 w-4 mr-2" />
          Add to Cart
        </Button>
        
        <Button
          v-if="canRemove"
          @click="handleRemove"
          variant="outline"
          size="sm"
          class="flex-1"
        >
          <Minus class="h-4 w-4 mr-2" />
          Remove
        </Button>

        <Button
          v-if="isRegistered"
          variant="outline"
          size="sm"
          class="flex-1"
          disabled
        >
          <CheckCircle class="h-4 w-4 mr-2" />
          Registered
        </Button>

        <Button
          v-if="!isRegistrationOpen"
          variant="outline"
          size="sm"
          class="flex-1"
          disabled
        >
          Registration Closed
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
