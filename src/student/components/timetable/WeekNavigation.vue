<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar,
  Home
} from 'lucide-vue-next'

interface Props {
  currentWeek: Date
  onPreviousWeek?: () => void
  onNextWeek?: () => void
  onToday?: () => void
}

const props = defineProps<Props>()

// Computed properties
const weekRange = computed(() => {
  const start = new Date(props.currentWeek)
  const end = new Date(props.currentWeek)
  end.setDate(start.getDate() + 6)
  
  const formatOptions: Intl.DateTimeFormatOptions = { 
    month: 'short', 
    day: 'numeric',
    year: start.getFullYear() !== end.getFullYear() ? 'numeric' : undefined
  }
  
  return {
    start: start.toLocaleDateString('en-US', formatOptions),
    end: end.toLocaleDateString('en-US', formatOptions),
    year: start.getFullYear()
  }
})

const isCurrentWeek = computed(() => {
  const today = new Date()
  const weekStart = new Date(props.currentWeek)
  const weekEnd = new Date(props.currentWeek)
  weekEnd.setDate(weekStart.getDate() + 6)
  
  return today >= weekStart && today <= weekEnd
})

const weekTitle = computed(() => {
  if (isCurrentWeek.value) {
    return 'This Week'
  }
  
  const today = new Date()
  const weekStart = new Date(props.currentWeek)
  const diffTime = weekStart.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays > 0 && diffDays <= 7) {
    return 'Next Week'
  } else if (diffDays < 0 && diffDays >= -7) {
    return 'Last Week'
  }
  
  return `Week of ${weekRange.value.start}`
})

// Actions
const handlePreviousWeek = () => {
  if (props.onPreviousWeek) {
    props.onPreviousWeek()
  }
}

const handleNextWeek = () => {
  if (props.onNextWeek) {
    props.onNextWeek()
  }
}

const handleToday = () => {
  if (props.onToday) {
    props.onToday()
  }
}
</script>

<template>
  <div class="flex items-center justify-between">
    <!-- Week Title and Range -->
    <div class="flex items-center gap-3">
      <div>
        <div class="flex items-center gap-2">
          <h2 class="text-xl font-semibold">{{ weekTitle }}</h2>
          <Badge v-if="isCurrentWeek" variant="default" class="text-xs">
            Current
          </Badge>
        </div>
        <p class="text-sm text-muted-foreground">
          {{ weekRange.start }} - {{ weekRange.end }}
          <span v-if="weekRange.year !== new Date().getFullYear()">
            , {{ weekRange.year }}
          </span>
        </p>
      </div>
    </div>

    <!-- Navigation Controls -->
    <div class="flex items-center gap-2">
      <!-- Today Button -->
      <Button
        variant="outline"
        size="sm"
        @click="handleToday"
        :disabled="isCurrentWeek"
        class="hidden sm:flex"
      >
        <Home class="h-4 w-4 mr-2" />
        Today
      </Button>

      <!-- Mobile Today Button -->
      <Button
        variant="outline"
        size="sm"
        @click="handleToday"
        :disabled="isCurrentWeek"
        class="sm:hidden"
      >
        <Home class="h-4 w-4" />
      </Button>

      <!-- Week Navigation -->
      <div class="flex items-center border rounded-md">
        <Button
          variant="ghost"
          size="sm"
          @click="handlePreviousWeek"
          class="rounded-r-none border-r"
        >
          <ChevronLeft class="h-4 w-4" />
          <span class="sr-only">Previous week</span>
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          @click="handleNextWeek"
          class="rounded-l-none"
        >
          <ChevronRight class="h-4 w-4" />
          <span class="sr-only">Next week</span>
        </Button>
      </div>
    </div>
  </div>
</template>
