<script setup lang="ts">
import { computed, ref } from 'vue'
import { Card, CardContent } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/shared/components/ui/popover'
import { 
  Clock, 
  MapPin, 
  User, 
  BookOpen,
  Calendar
} from 'lucide-vue-next'
import type { ClassSession } from '../../types/models/course'

interface Props {
  sessions: ClassSession[]
  weekStart: Date
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Time slots (8 AM to 8 PM in 1-hour intervals)
const timeSlots = Array.from({ length: 12 }, (_, i) => {
  const hour = i + 8
  return {
    time: `${hour.toString().padStart(2, '0')}:00`,
    hour: hour,
    label: hour <= 12 ? `${hour}:00 AM` : `${hour - 12}:00 PM`
  }
})

// Days of the week
const weekDays = [
  { key: 'monday', label: 'Mon', fullLabel: 'Monday' },
  { key: 'tuesday', label: 'Tue', fullLabel: 'Tuesday' },
  { key: 'wednesday', label: 'Wed', fullLabel: 'Wednesday' },
  { key: 'thursday', label: 'Thu', fullLabel: 'Thursday' },
  { key: 'friday', label: 'Fri', fullLabel: 'Friday' },
  { key: 'saturday', label: 'Sat', fullLabel: 'Saturday' },
  { key: 'sunday', label: 'Sun', fullLabel: 'Sunday' },
]

// Get date for each day of the week
const weekDates = computed(() => {
  const dates: Record<string, Date> = {}
  weekDays.forEach((day, index) => {
    const date = new Date(props.weekStart)
    date.setDate(props.weekStart.getDate() + index)
    dates[day.key] = date
  })
  return dates
})

// Group sessions by day and time
const sessionsByDay = computed(() => {
  const grouped: Record<string, ClassSession[]> = {}
  
  // Initialize empty arrays for each day
  weekDays.forEach(day => {
    grouped[day.key] = []
  })
  
  // For now, we'll mock the day assignment since ClassSession doesn't have timing info
  // In a real implementation, this would be based on actual session timing
  props.sessions.forEach((session, index) => {
    const dayIndex = index % 5 // Distribute across weekdays
    const dayKey = weekDays[dayIndex].key
    grouped[dayKey].push(session)
  })
  
  return grouped
})

// Get sessions for a specific time slot and day
const getSessionsForSlot = (dayKey: string, hour: number) => {
  // Mock implementation - in real app, this would check actual session times
  const daySessions = sessionsByDay.value[dayKey] || []
  return daySessions.filter((_, index) => (index + 8) === hour) // Mock time assignment
}

// Format session for display
const formatSession = (session: ClassSession) => {
  return {
    title: session.course_offering.curriculum_unit.unit.code,
    subtitle: session.course_offering.curriculum_unit.unit.name,
    instructor: session.instructor.email,
    location: `${session.room.name}, ${session.room.campus.name}`,
    duration: '1 hour', // Mock duration
    color: getSessionColor(session.course_offering.curriculum_unit.unit.code)
  }
}

// Get color for session based on course code
const getSessionColor = (courseCode: string) => {
  const colors = [
    'bg-blue-100 border-blue-300 text-blue-800',
    'bg-green-100 border-green-300 text-green-800',
    'bg-purple-100 border-purple-300 text-purple-800',
    'bg-orange-100 border-orange-300 text-orange-800',
    'bg-pink-100 border-pink-300 text-pink-800',
    'bg-indigo-100 border-indigo-300 text-indigo-800',
  ]
  
  // Simple hash function to assign consistent colors
  let hash = 0
  for (let i = 0; i < courseCode.length; i++) {
    hash = courseCode.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  return colors[Math.abs(hash) % colors.length]
}

// Format date for display
const formatDate = (date: Date) => {
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric' 
  })
}
</script>

<template>
  <div class="w-full">
    <!-- Loading State -->
    <div v-if="isLoading" class="space-y-4">
      <div class="h-8 bg-muted animate-pulse rounded" />
      <div class="grid grid-cols-8 gap-2">
        <div v-for="i in 56" :key="i" class="h-16 bg-muted animate-pulse rounded" />
      </div>
    </div>

    <!-- Timetable Grid -->
    <div v-else class="overflow-x-auto">
      <div class="min-w-[800px]">
        <!-- Header Row -->
        <div class="grid grid-cols-8 gap-1 mb-2">
          <!-- Time column header -->
          <div class="p-2 text-center font-medium text-sm text-muted-foreground">
            Time
          </div>
          
          <!-- Day headers -->
          <div
            v-for="day in weekDays"
            :key="day.key"
            class="p-2 text-center"
          >
            <div class="font-medium text-sm">{{ day.label }}</div>
            <div class="text-xs text-muted-foreground">
              {{ formatDate(weekDates[day.key]) }}
            </div>
          </div>
        </div>

        <!-- Time Slots -->
        <div class="space-y-1">
          <div
            v-for="slot in timeSlots"
            :key="slot.time"
            class="grid grid-cols-8 gap-1 min-h-[60px]"
          >
            <!-- Time Label -->
            <div class="p-2 text-xs text-muted-foreground text-center border-r">
              {{ slot.label }}
            </div>

            <!-- Day Columns -->
            <div
              v-for="day in weekDays"
              :key="`${day.key}-${slot.time}`"
              class="relative border border-border/50 rounded-sm bg-background hover:bg-muted/30 transition-colors"
            >
              <!-- Sessions in this slot -->
              <div
                v-for="session in getSessionsForSlot(day.key, slot.hour)"
                :key="session.id"
                class="absolute inset-1"
              >
                <Popover>
                  <PopoverTrigger as-child>
                    <Button
                      variant="ghost"
                      :class="[
                        'w-full h-full p-1 text-left justify-start border-l-2',
                        formatSession(session).color
                      ]"
                    >
                      <div class="min-w-0 flex-1">
                        <div class="font-medium text-xs truncate">
                          {{ formatSession(session).title }}
                        </div>
                        <div class="text-xs opacity-75 truncate">
                          {{ session.room.name }}
                        </div>
                      </div>
                    </Button>
                  </PopoverTrigger>
                  
                  <PopoverContent class="w-80" align="start">
                    <div class="space-y-3">
                      <div>
                        <h4 class="font-semibold">{{ formatSession(session).title }}</h4>
                        <p class="text-sm text-muted-foreground">
                          {{ formatSession(session).subtitle }}
                        </p>
                      </div>
                      
                      <div class="space-y-2 text-sm">
                        <div class="flex items-center gap-2">
                          <Clock class="h-4 w-4 text-muted-foreground" />
                          <span>{{ slot.label }} - {{ formatSession(session).duration }}</span>
                        </div>
                        
                        <div class="flex items-center gap-2">
                          <MapPin class="h-4 w-4 text-muted-foreground" />
                          <span>{{ formatSession(session).location }}</span>
                        </div>
                        
                        <div class="flex items-center gap-2">
                          <User class="h-4 w-4 text-muted-foreground" />
                          <span>{{ formatSession(session).instructor }}</span>
                        </div>
                        
                        <div class="flex items-center gap-2">
                          <BookOpen class="h-4 w-4 text-muted-foreground" />
                          <span>{{ session.course_offering.curriculum_unit.unit.credit_points }} Credit Points</span>
                        </div>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && sessions.length === 0" class="text-center py-12">
      <Calendar class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <p class="text-muted-foreground">No classes scheduled for this week</p>
    </div>
  </div>
</template>
