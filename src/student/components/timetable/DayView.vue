<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger,
} from '@/shared/components/ui/popover'
import { 
  Clock, 
  MapPin, 
  User, 
  BookOpen,
  Calendar,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next'
import type { ClassSession } from '../../types/models/course'

interface Props {
  sessions: ClassSession[]
  selectedDate: Date
  isLoading?: boolean
  onPreviousDay?: () => void
  onNextDay?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Computed properties
const dayTitle = computed(() => {
  const today = new Date()
  const selected = new Date(props.selectedDate)
  
  // Reset time for comparison
  today.setHours(0, 0, 0, 0)
  selected.setHours(0, 0, 0, 0)
  
  if (selected.getTime() === today.getTime()) {
    return 'Today'
  }
  
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)
  
  if (selected.getTime() === tomorrow.getTime()) {
    return 'Tomorrow'
  }
  
  const yesterday = new Date(today)
  yesterday.setDate(today.getDate() - 1)
  
  if (selected.getTime() === yesterday.getTime()) {
    return 'Yesterday'
  }
  
  return selected.toLocaleDateString('en-US', { 
    weekday: 'long',
    month: 'long',
    day: 'numeric'
  })
})

const formattedDate = computed(() => {
  return props.selectedDate.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  })
})

// Mock sessions for the day (in real app, this would filter actual sessions)
const daySessions = computed(() => {
  // For demo purposes, we'll show some sessions
  // In real implementation, this would filter sessions by the selected date
  return props.sessions.slice(0, 3).map((session, index) => ({
    ...session,
    startTime: `${9 + index * 2}:00`,
    endTime: `${10 + index * 2}:00`,
    duration: '1 hour'
  }))
})

const hasSessionsToday = computed(() => {
  return daySessions.value.length > 0
})

// Format session for display
const formatSession = (session: any) => {
  return {
    title: session.course_offering.curriculum_unit.unit.code,
    subtitle: session.course_offering.curriculum_unit.unit.name,
    instructor: session.instructor.email,
    location: `${session.room.name}, ${session.room.campus.name}`,
    time: `${session.startTime} - ${session.endTime}`,
    color: getSessionColor(session.course_offering.curriculum_unit.unit.code)
  }
}

// Get color for session based on course code
const getSessionColor = (courseCode: string) => {
  const colors = [
    'bg-blue-100 border-blue-300 text-blue-800',
    'bg-green-100 border-green-300 text-green-800',
    'bg-purple-100 border-purple-300 text-purple-800',
    'bg-orange-100 border-orange-300 text-orange-800',
    'bg-pink-100 border-pink-300 text-pink-800',
    'bg-indigo-100 border-indigo-300 text-indigo-800',
  ]
  
  let hash = 0
  for (let i = 0; i < courseCode.length; i++) {
    hash = courseCode.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  return colors[Math.abs(hash) % colors.length]
}

// Actions
const handlePreviousDay = () => {
  if (props.onPreviousDay) {
    props.onPreviousDay()
  }
}

const handleNextDay = () => {
  if (props.onNextDay) {
    props.onNextDay()
  }
}
</script>

<template>
  <div class="space-y-4">
    <!-- Day Navigation Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-xl font-semibold">{{ dayTitle }}</h2>
        <p class="text-sm text-muted-foreground">{{ formattedDate }}</p>
      </div>
      
      <!-- Navigation Controls -->
      <div class="flex items-center border rounded-md">
        <Button
          variant="ghost"
          size="sm"
          @click="handlePreviousDay"
          class="rounded-r-none border-r"
        >
          <ChevronLeft class="h-4 w-4" />
          <span class="sr-only">Previous day</span>
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          @click="handleNextDay"
          class="rounded-l-none"
        >
          <ChevronRight class="h-4 w-4" />
          <span class="sr-only">Next day</span>
        </Button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="space-y-3">
      <div v-for="i in 3" :key="i" class="h-24 bg-muted animate-pulse rounded-lg" />
    </div>

    <!-- Sessions List -->
    <div v-else-if="hasSessionsToday" class="space-y-3">
      <Card
        v-for="session in daySessions"
        :key="session.id"
        :class="[
          'transition-all duration-200 hover:shadow-md border-l-4',
          formatSession(session).color.split(' ')[1] // Extract border color
        ]"
      >
        <CardHeader class="pb-3">
          <div class="flex items-start justify-between">
            <div>
              <CardTitle class="text-lg">
                {{ formatSession(session).title }}
              </CardTitle>
              <p class="text-sm text-muted-foreground mt-1">
                {{ formatSession(session).subtitle }}
              </p>
            </div>
            <Badge :class="formatSession(session).color" class="shrink-0">
              {{ formatSession(session).time }}
            </Badge>
          </div>
        </CardHeader>

        <CardContent class="space-y-3">
          <!-- Session Details -->
          <div class="space-y-2 text-sm">
            <div class="flex items-center gap-2">
              <Clock class="h-4 w-4 text-muted-foreground" />
              <span>{{ formatSession(session).time }} ({{ session.duration }})</span>
            </div>
            
            <div class="flex items-center gap-2">
              <MapPin class="h-4 w-4 text-muted-foreground" />
              <span>{{ formatSession(session).location }}</span>
            </div>
            
            <div class="flex items-center gap-2">
              <User class="h-4 w-4 text-muted-foreground" />
              <span>{{ formatSession(session).instructor }}</span>
            </div>
            
            <div class="flex items-center gap-2">
              <BookOpen class="h-4 w-4 text-muted-foreground" />
              <span>{{ session.course_offering.curriculum_unit.unit.credit_points }} Credit Points</span>
            </div>
          </div>

          <!-- Action Button -->
          <div class="pt-2">
            <Popover>
              <PopoverTrigger as-child>
                <Button variant="outline" size="sm" class="w-full">
                  View Details
                </Button>
              </PopoverTrigger>
              
              <PopoverContent class="w-80">
                <div class="space-y-3">
                  <div>
                    <h4 class="font-semibold">{{ formatSession(session).title }}</h4>
                    <p class="text-sm text-muted-foreground">
                      {{ formatSession(session).subtitle }}
                    </p>
                  </div>
                  
                  <div class="space-y-2 text-sm">
                    <div class="flex items-center gap-2">
                      <Clock class="h-4 w-4 text-muted-foreground" />
                      <span>{{ formatSession(session).time }}</span>
                    </div>
                    
                    <div class="flex items-center gap-2">
                      <MapPin class="h-4 w-4 text-muted-foreground" />
                      <span>{{ formatSession(session).location }}</span>
                    </div>
                    
                    <div class="flex items-center gap-2">
                      <User class="h-4 w-4 text-muted-foreground" />
                      <span>{{ formatSession(session).instructor }}</span>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <Calendar class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <p class="text-muted-foreground">No classes scheduled for this day</p>
      <p class="text-sm text-muted-foreground mt-1">Enjoy your free time!</p>
    </div>
  </div>
</template>
