<script setup lang="ts">
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/shared/components/ui/collapsible'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from '@/shared/components/ui/sidebar'
import {
  ChevronRight,
  GalleryVerticalEnd,
  Home,
  User,
  Calendar,
  BookOpen,
  GraduationCap,
  ClipboardList,
  UserCheck,
  AlertTriangle,
  Map,
  Bell,
} from 'lucide-vue-next'
import { useRoute, useRouter } from 'vue-router'
import { computed, onMounted, onUnmounted } from 'vue'

const route = useRoute()

// Define navigation items according to student portal structure
const navMain = [
  {
    title: 'Dashboard',
    url: '/student/dashboard',
    icon: Home,
    shortcut: '1',
  },
  {
    title: 'Profile',
    url: '/student/profile',
    icon: User,
    shortcut: '2',
    items: [
      {
        title: 'Personal Info',
        url: '/student/profile',
      },
      {
        title: 'Study Plan',
        url: '/student/profile/study-plan',
      },
    ],
  },
  {
    title: 'Timetable',
    url: '/student/schedule',
    icon: Calendar,
    shortcut: '3',
  },
  {
    title: 'Course Registration',
    url: '/student/courses',
    icon: BookOpen,
    shortcut: '4',
    items: [
      {
        title: 'Open Courses',
        url: '/student/courses',
      },
      {
        title: 'My Registrations',
        url: '/student/courses/registrations',
      },
    ],
  },
  {
    title: 'Grades',
    url: '/student/grades',
    icon: GraduationCap,
    shortcut: '5',
    items: [
      {
        title: 'Transcript',
        url: '/student/grades',
      },
      {
        title: 'GPA Trend',
        url: '/student/grades/trend',
      },
    ],
  },
  {
    title: 'Assessment',
    url: '/student/assessment',
    icon: ClipboardList,
    shortcut: '6',
  },
  {
    title: 'Attendance',
    url: '/student/attendance',
    icon: UserCheck,
    shortcut: '7',
  },
  {
    title: 'Holds',
    url: '/student/holds',
    icon: AlertTriangle,
    shortcut: '8',
  },
  {
    title: 'Curriculum',
    url: '/student/curriculum',
    icon: Map,
    shortcut: '9',
  },
]

// Computed property to check if a route is active
const isRouteActive = computed(() => (url: string) => {
  return route.path === url || route.path.startsWith(url + '/')
})

// Computed property to check if a parent item should be open (has active children)
const shouldBeOpen = computed(() => (item: (typeof navMain)[0]) => {
  return (
    item.items?.some((subItem) => isRouteActive.value(subItem.url)) || isRouteActive.value(item.url)
  )
})

// Initialize router for navigation
const router = useRouter()

// Keyboard navigation support
const handleKeyboardNavigation = (event: KeyboardEvent) => {
  const key = event.key

  // Handle number keys 1-9 for menu navigation
  if (key >= '1' && key <= '9') {
    event.preventDefault()
    const index = parseInt(key) - 1
    const targetItem = navMain[index]

    if (targetItem) {
      // Navigate to the main URL of the item
      router.push(targetItem.url)

      // Announce to screen readers
      announceToScreenReader(`Navigating to ${targetItem.title}`)
    }
  }
}

// Screen reader announcements
const announceToScreenReader = (message: string) => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message

  document.body.appendChild(announcement)

  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

// Add keyboard event listeners
onMounted(() => {
  document.addEventListener('keydown', handleKeyboardNavigation)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardNavigation)
})

defineProps<{
  // Add any props that were passed to the React component
  [key: string]: unknown
}>()
</script>

<template>
  <Sidebar v-bind="$props" role="navigation" aria-label="Main navigation">
    <SidebarHeader>
      <!-- Logo and App Name -->
      <div class="flex items-center gap-2">
        <div
          class="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground"
        >
          <GalleryVerticalEnd class="size-4" />
        </div>
        <span class="font-semibold text-lg">Student Portal</span>
      </div>
    </SidebarHeader>
    <SidebarContent class="gap-0">
      <SidebarGroup>
        <!-- Main Navigation Menu -->
        <SidebarMenu role="menubar">
          <template v-for="(item, index) in navMain" :key="item.title">
            <!-- Items without sub-items -->
            <SidebarMenuItem v-if="!item.items" role="none">
              <SidebarMenuButton
                as-child
                :tooltip="`${item.title} (Press ${item.shortcut})`"
                :class="{
                  'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                    isRouteActive(item.url),
                  'hover:bg-accent hover:text-accent-foreground': !isRouteActive(item.url),
                }"
                role="menuitem"
                :aria-current="isRouteActive(item.url) ? 'page' : undefined"
                :tabindex="index === 0 ? 0 : -1"
              >
                <RouterLink :to="item.url" class="flex items-center gap-2 w-full">
                  <component :is="item.icon" class="h-4 w-4" />
                  <span>{{ item.title }}</span>
                  <span class="ml-auto text-xs text-muted-foreground">{{ item.shortcut }}</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- Items with sub-items (collapsible) -->
            <Collapsible
              v-else
              as-child
              :default-open="shouldBeOpen(item)"
              class="group/collapsible"
            >
              <SidebarMenuItem role="none">
                <CollapsibleTrigger as-child>
                  <SidebarMenuButton
                    :tooltip="`${item.title} (Press ${item.shortcut})`"
                    :class="{
                      'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                        isRouteActive(item.url),
                      'hover:bg-accent hover:text-accent-foreground': !isRouteActive(item.url),
                    }"
                    role="menuitem"
                    :aria-current="isRouteActive(item.url) ? 'page' : undefined"
                    :aria-expanded="shouldBeOpen(item)"
                    :tabindex="index === 0 ? 0 : -1"
                  >
                    <component :is="item.icon" class="h-4 w-4" />
                    <span>{{ item.title }}</span>
                    <span class="ml-auto text-xs text-muted-foreground mr-2">{{
                      item.shortcut
                    }}</span>
                    <ChevronRight
                      class="transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                    />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub role="menu">
                    <template v-for="subItem in item.items" :key="subItem.title">
                      <SidebarMenuSubItem role="none">
                        <SidebarMenuSubButton
                          as-child
                          :class="{
                            'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                              isRouteActive(subItem.url),
                            'hover:bg-accent hover:text-accent-foreground': !isRouteActive(
                              subItem.url,
                            ),
                          }"
                          role="menuitem"
                          :aria-current="isRouteActive(subItem.url) ? 'page' : undefined"
                        >
                          <RouterLink :to="subItem.url">
                            <span>{{ subItem.title }}</span>
                          </RouterLink>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    </template>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          </template>
        </SidebarMenu>
      </SidebarGroup>
    </SidebarContent>
    <SidebarRail />
  </Sidebar>
</template>
