<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Home, Calendar, BookOpen, Bell } from 'lucide-vue-next'
import { Button } from '@/shared/components/ui/button'

const route = useRoute()
const router = useRouter()

// Define bottom navigation items for mobile (key functions only)
const mobileNavItems = [
  {
    title: 'Dashboard',
    url: '/student/dashboard',
    icon: Home,
    shortcut: '1',
  },
  {
    title: 'Timetable',
    url: '/student/schedule',
    icon: Calendar,
    shortcut: '3',
  },
  {
    title: 'Courses',
    url: '/student/courses',
    icon: BookOpen,
    shortcut: '4',
  },
  {
    title: 'Notifications',
    url: '/student/notifications',
    icon: Bell,
    shortcut: 'N',
  },
]

// Computed property to check if a route is active
const isRouteActive = computed(() => (url: string) => {
  return route.path === url || route.path.startsWith(url + '/')
})

// Handle navigation
const navigateTo = (url: string, title: string) => {
  router.push(url)

  // Announce to screen readers
  announceToScreenReader(`Navigating to ${title}`)
}

// Screen reader announcements
const announceToScreenReader = (message: string) => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message

  document.body.appendChild(announcement)

  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}
</script>

<template>
  <!-- Mobile Bottom Navigation - Only visible on mobile -->
  <nav
    class="fixed bottom-0 left-0 right-0 z-50 bg-background border-t border-border md:hidden"
    role="navigation"
    aria-label="Mobile navigation"
  >
    <div class="flex items-center justify-around px-2 py-2">
      <template v-for="(item, index) in mobileNavItems" :key="item.title">
        <Button
          variant="ghost"
          size="sm"
          :class="[
            'flex flex-col items-center gap-1 px-3 py-2 h-auto min-w-0 flex-1',
            {
              'text-primary bg-primary/10': isRouteActive(item.url),
              'text-muted-foreground hover:text-foreground': !isRouteActive(item.url),
            },
          ]"
          @click="navigateTo(item.url, item.title)"
          role="menuitem"
          :aria-current="isRouteActive(item.url) ? 'page' : undefined"
          :aria-label="`${item.title} (Press ${item.shortcut})`"
          :tabindex="index === 0 ? 0 : -1"
        >
          <component :is="item.icon" class="h-5 w-5" />
          <span class="text-xs font-medium truncate">{{ item.title }}</span>
        </Button>
      </template>
    </div>
  </nav>
</template>

<style scoped>
/* Ensure content doesn't get hidden behind mobile nav */
@media (max-width: 768px) {
  :global(body) {
    padding-bottom: 4rem;
  }
}
</style>
