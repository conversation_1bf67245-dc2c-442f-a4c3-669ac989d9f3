<script setup lang="ts">
import StudentSidebar from './StudentSidebar.vue'
import StudentMobileNav from './StudentMobileNav.vue'
// import NotificationPopover from '@/shared/components/SharedNotificationPopover.vue'
import { Separator } from '@/shared/components/ui/separator'
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/shared/components/ui/sidebar'
import UserAvatar from '@/shared/components/SharedUserAvatar.vue'
import { RouterView } from 'vue-router'
</script>

<template>
  <SidebarProvider>
    <StudentSidebar />
    <SidebarInset>
      <header
        class="flex sticky top-0 bg-background h-16 shrink-0 items-center gap-2 border-b px-4 z-40"
      >
        <SidebarTrigger class="-ml-1" aria-label="Toggle sidebar" />
        <Separator orientation="vertical" class="mr-2 h-4" />

        <!-- Student Portal Title -->
        <div class="flex items-center gap-4">
          <h1 class="text-lg font-semibold text-muted-foreground">Student Portal</h1>
        </div>

        <!-- Spacer -->
        <div class="flex-1" />

        <!-- Right side - Notifications and Avatar -->
        <div class="flex items-center gap-3">
          <!-- <NotificationPopover /> -->
          <UserAvatar />
        </div>
      </header>

      <!-- Main content area with proper spacing for mobile nav -->
      <div class="flex flex-1 flex-col gap-4 p-4 pb-20 md:pb-4">
        <RouterView />
      </div>
    </SidebarInset>

    <!-- Mobile Navigation - Only visible on mobile -->
    <StudentMobileNav />
  </SidebarProvider>
</template>
