<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/shared/components/ui/popover'
import { 
  CheckCircle, 
  Lock, 
  BookOpen,
  Clock,
  Target,
  AlertCircle,
  Info,
  Award
} from 'lucide-vue-next'
import type { CurriculumUnit } from '../../types/models/student'

interface Props {
  unit: CurriculumUnit
  status: 'completed' | 'available' | 'locked'
  viewMode?: 'grid' | 'list'
  prerequisites?: string[] // Mock prerequisites
  grade?: string
}

const props = withDefaults(defineProps<Props>(), {
  viewMode: 'grid',
  prerequisites: () => [],
})

// Mock data for demonstration
const mockPrerequisites = computed(() => {
  // In real app, this would come from actual prerequisite data
  const unitCode = props.unit.unit.code
  const codeNumber = parseInt(unitCode.replace(/\D/g, '')) || 100
  
  if (codeNumber <= 100) return []
  if (codeNumber <= 200) return [`${unitCode.replace(/\d+/, '1')}01`]
  if (codeNumber <= 300) return [`${unitCode.replace(/\d+/, '2')}01`, `${unitCode.replace(/\d+/, '2')}02`]
  return [`${unitCode.replace(/\d+/, '3')}01`, `${unitCode.replace(/\d+/, '3')}02`, `${unitCode.replace(/\d+/, '3')}03`]
})

const mockGrade = computed(() => {
  if (props.status !== 'completed') return null
  const grades = ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C']
  return grades[Math.floor(Math.random() * grades.length)]
})

// Computed properties
const statusConfig = computed(() => {
  switch (props.status) {
    case 'completed':
      return {
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-50 border-green-200',
        badgeVariant: 'default' as const,
        badgeText: 'Completed'
      }
    case 'available':
      return {
        icon: BookOpen,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50 border-blue-200',
        badgeVariant: 'secondary' as const,
        badgeText: 'Available'
      }
    case 'locked':
      return {
        icon: Lock,
        color: 'text-muted-foreground',
        bgColor: 'bg-muted/30 border-muted',
        badgeVariant: 'outline' as const,
        badgeText: 'Locked'
      }
  }
})

const unitLevel = computed(() => {
  const code = props.unit.unit.code
  const level = parseInt(code.replace(/\D/g, '').charAt(0)) || 1
  return level
})

const unitType = computed(() => {
  // Mock unit type determination
  const code = props.unit.unit.code
  if (code.includes('CORE') || unitLevel.value === 1) return 'Core'
  if (code.includes('ELEC')) return 'Elective'
  return Math.random() > 0.5 ? 'Core' : 'Elective'
})

const difficultyLevel = computed(() => {
  const level = unitLevel.value
  if (level <= 1) return 'Beginner'
  if (level <= 2) return 'Intermediate'
  if (level <= 3) return 'Advanced'
  return 'Expert'
})

const estimatedHours = computed(() => {
  // Mock estimated study hours based on credit points
  return props.unit.unit.credit_points * 10
})
</script>

<template>
  <Popover>
    <PopoverTrigger as-child>
      <Card 
        :class="[
          'transition-all duration-200 hover:shadow-md cursor-pointer',
          statusConfig.bgColor,
          viewMode === 'list' ? 'w-full' : '',
          status === 'locked' ? 'opacity-75' : ''
        ]"
      >
        <CardHeader :class="viewMode === 'list' ? 'pb-2' : 'pb-3'">
          <div :class="[
            'flex items-start justify-between',
            viewMode === 'list' ? 'flex-row' : 'flex-col gap-2'
          ]">
            <div :class="viewMode === 'list' ? 'flex-1' : 'w-full'">
              <CardTitle :class="viewMode === 'list' ? 'text-base' : 'text-lg'">
                {{ unit.unit.code }}
              </CardTitle>
              <p :class="[
                'text-muted-foreground mt-1',
                viewMode === 'list' ? 'text-sm' : 'text-sm'
              ]">
                {{ unit.unit.name }}
              </p>
            </div>
            
            <div :class="[
              'flex items-center gap-2 shrink-0',
              viewMode === 'list' ? 'ml-4' : 'w-full justify-between'
            ]">
              <component 
                :is="statusConfig.icon" 
                :class="['h-5 w-5', statusConfig.color]"
              />
              <Badge :variant="statusConfig.badgeVariant" class="text-xs">
                {{ statusConfig.badgeText }}
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent v-if="viewMode === 'grid'" class="pt-0">
          <div class="space-y-2">
            <!-- Credit Points -->
            <div class="flex items-center justify-between text-sm">
              <span class="text-muted-foreground">Credit Points</span>
              <span class="font-medium">{{ unit.unit.credit_points }}</span>
            </div>
            
            <!-- Grade (if completed) -->
            <div v-if="status === 'completed' && mockGrade" class="flex items-center justify-between text-sm">
              <span class="text-muted-foreground">Grade</span>
              <Badge variant="outline" class="text-xs">{{ mockGrade }}</Badge>
            </div>
            
            <!-- Prerequisites count -->
            <div v-if="mockPrerequisites.length > 0" class="flex items-center justify-between text-sm">
              <span class="text-muted-foreground">Prerequisites</span>
              <span class="font-medium">{{ mockPrerequisites.length }}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </PopoverTrigger>
    
    <PopoverContent class="w-80" align="start">
      <div class="space-y-4">
        <!-- Header -->
        <div>
          <h4 class="font-semibold flex items-center gap-2">
            <component :is="statusConfig.icon" :class="['h-4 w-4', statusConfig.color]" />
            {{ unit.unit.code }} - {{ unit.unit.name }}
          </h4>
          <div class="flex items-center gap-2 mt-2">
            <Badge :variant="statusConfig.badgeVariant" class="text-xs">
              {{ statusConfig.badgeText }}
            </Badge>
            <Badge variant="outline" class="text-xs">
              {{ unitType }}
            </Badge>
            <Badge variant="outline" class="text-xs">
              Level {{ unitLevel }}
            </Badge>
          </div>
        </div>
        
        <!-- Details -->
        <div class="space-y-3 text-sm">
          <!-- Basic Info -->
          <div class="grid grid-cols-2 gap-3">
            <div>
              <div class="flex items-center gap-1 text-muted-foreground">
                <Target class="h-3 w-3" />
                <span>Credit Points</span>
              </div>
              <div class="font-medium">{{ unit.unit.credit_points }}</div>
            </div>
            <div>
              <div class="flex items-center gap-1 text-muted-foreground">
                <Clock class="h-3 w-3" />
                <span>Est. Hours</span>
              </div>
              <div class="font-medium">{{ estimatedHours }}h</div>
            </div>
          </div>
          
          <!-- Semester -->
          <div>
            <div class="flex items-center gap-1 text-muted-foreground">
              <BookOpen class="h-3 w-3" />
              <span>Semester</span>
            </div>
            <div class="font-medium">{{ unit.semester.name }}</div>
          </div>
          
          <!-- Grade (if completed) -->
          <div v-if="status === 'completed' && mockGrade">
            <div class="flex items-center gap-1 text-muted-foreground">
              <Award class="h-3 w-3" />
              <span>Grade Achieved</span>
            </div>
            <div class="font-medium">{{ mockGrade }}</div>
          </div>
          
          <!-- Difficulty -->
          <div>
            <div class="flex items-center gap-1 text-muted-foreground">
              <AlertCircle class="h-3 w-3" />
              <span>Difficulty</span>
            </div>
            <div class="font-medium">{{ difficultyLevel }}</div>
          </div>
          
          <!-- Prerequisites -->
          <div v-if="mockPrerequisites.length > 0">
            <div class="flex items-center gap-1 text-muted-foreground mb-2">
              <Info class="h-3 w-3" />
              <span>Prerequisites</span>
            </div>
            <div class="space-y-1">
              <Badge 
                v-for="prereq in mockPrerequisites" 
                :key="prereq"
                variant="outline" 
                class="text-xs mr-1"
              >
                {{ prereq }}
              </Badge>
            </div>
          </div>
          
          <!-- Status-specific info -->
          <div v-if="status === 'locked'" class="p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-800">
            <div class="flex items-start gap-2">
              <Lock class="h-4 w-4 mt-0.5" />
              <div>
                <div class="font-medium text-xs">Prerequisites Required</div>
                <div class="text-xs">Complete prerequisite units to unlock this unit</div>
              </div>
            </div>
          </div>
          
          <div v-if="status === 'available'" class="p-2 bg-blue-50 border border-blue-200 rounded text-blue-800">
            <div class="flex items-start gap-2">
              <BookOpen class="h-4 w-4 mt-0.5" />
              <div>
                <div class="font-medium text-xs">Ready to Enroll</div>
                <div class="text-xs">This unit is available for registration</div>
              </div>
            </div>
          </div>
          
          <div v-if="status === 'completed'" class="p-2 bg-green-50 border border-green-200 rounded text-green-800">
            <div class="flex items-start gap-2">
              <CheckCircle class="h-4 w-4 mt-0.5" />
              <div>
                <div class="font-medium text-xs">Successfully Completed</div>
                <div class="text-xs">You have completed this unit with grade {{ mockGrade }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PopoverContent>
  </Popover>
</template>
