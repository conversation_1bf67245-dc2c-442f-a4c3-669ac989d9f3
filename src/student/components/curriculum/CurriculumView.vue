<script setup lang="ts">
import { ref, computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import UnitNode from './UnitNode.vue'
import { 
  BookOpen, 
  Filter,
  Grid3X3,
  List,
  Target,
  Award
} from 'lucide-vue-next'
import type { CurriculumVersion, CurriculumUnit } from '../../types/models/student'

interface Props {
  curriculumVersion: CurriculumVersion | null
  curriculumUnits: CurriculumUnit[]
  completedUnits: string[] // Array of unit IDs that are completed
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
})

// Local state
const selectedFilter = ref<string>('all')
const viewMode = ref<'grid' | 'list'>('grid')

// Filter options
const filterOptions = [
  { value: 'all', label: 'All Units' },
  { value: 'core', label: 'Core Units' },
  { value: 'elective', label: 'Elective Units' },
  { value: 'completed', label: 'Completed' },
  { value: 'available', label: 'Available' },
  { value: 'locked', label: 'Prerequisites Required' }
]

// Computed properties
const filteredUnits = computed(() => {
  let units = props.curriculumUnits

  switch (selectedFilter.value) {
    case 'core':
      // Mock core unit filtering - in real app, this would be based on unit metadata
      units = units.filter((_, index) => index % 3 === 0)
      break
    case 'elective':
      // Mock elective unit filtering
      units = units.filter((_, index) => index % 3 !== 0)
      break
    case 'completed':
      units = units.filter(unit => props.completedUnits.includes(unit.unit.id))
      break
    case 'available':
      units = units.filter(unit => !props.completedUnits.includes(unit.unit.id) && isUnitAvailable(unit))
      break
    case 'locked':
      units = units.filter(unit => !isUnitAvailable(unit) && !props.completedUnits.includes(unit.unit.id))
      break
  }

  return units
})

const unitsBySemester = computed(() => {
  const grouped: Record<string, CurriculumUnit[]> = {}
  
  filteredUnits.value.forEach(unit => {
    const semesterName = unit.semester.name
    if (!grouped[semesterName]) {
      grouped[semesterName] = []
    }
    grouped[semesterName].push(unit)
  })
  
  return grouped
})

const progressStats = computed(() => {
  const total = props.curriculumUnits.length
  const completed = props.completedUnits.length
  const available = props.curriculumUnits.filter(unit => 
    !props.completedUnits.includes(unit.unit.id) && isUnitAvailable(unit)
  ).length
  
  return {
    total,
    completed,
    available,
    locked: total - completed - available,
    completionPercentage: total > 0 ? Math.round((completed / total) * 100) : 0
  }
})

// Helper functions
const isUnitAvailable = (unit: CurriculumUnit): boolean => {
  // Mock prerequisite checking - in real app, this would check actual prerequisites
  // For demo, we'll make units available based on their position
  const unitIndex = props.curriculumUnits.findIndex(u => u.id === unit.id)
  const completedCount = props.completedUnits.length
  return unitIndex <= completedCount + 2 // Allow 2 units ahead
}

const getUnitStatus = (unit: CurriculumUnit): 'completed' | 'available' | 'locked' => {
  if (props.completedUnits.includes(unit.unit.id)) return 'completed'
  if (isUnitAvailable(unit)) return 'available'
  return 'locked'
}

// Actions
const handleFilterChange = (value: string) => {
  selectedFilter.value = value
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid'
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
      <div>
        <h2 class="text-2xl font-bold">Curriculum Roadmap</h2>
        <p v-if="curriculumVersion" class="text-muted-foreground">
          {{ curriculumVersion.program.name }}
          <span v-if="curriculumVersion.specialization">
            - {{ curriculumVersion.specialization.name }}
          </span>
        </p>
      </div>
      
      <!-- Controls -->
      <div class="flex items-center gap-2">
        <!-- Filter -->
        <Select v-model="selectedFilter" @update:model-value="handleFilterChange">
          <SelectTrigger class="w-48">
            <Filter class="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter units" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="option in filterOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </SelectItem>
          </SelectContent>
        </Select>

        <!-- View Mode Toggle -->
        <Button variant="outline" size="sm" @click="toggleViewMode">
          <component :is="viewMode === 'grid' ? List : Grid3X3" class="h-4 w-4 mr-2" />
          {{ viewMode === 'grid' ? 'List' : 'Grid' }}
        </Button>
      </div>
    </div>

    <!-- Progress Overview -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Target class="h-5 w-5" />
          Progress Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="space-y-4">
          <div class="h-8 bg-muted animate-pulse rounded" />
          <div class="grid grid-cols-4 gap-4">
            <div v-for="i in 4" :key="i" class="h-16 bg-muted animate-pulse rounded" />
          </div>
        </div>
        
        <div v-else class="space-y-4">
          <!-- Overall Progress -->
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">
              {{ progressStats.completionPercentage }}%
            </div>
            <div class="text-sm text-muted-foreground">Curriculum Complete</div>
          </div>
          
          <!-- Detailed Stats -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div class="text-xl font-bold text-muted-foreground">{{ progressStats.total }}</div>
              <div class="text-sm text-muted-foreground">Total Units</div>
            </div>
            <div>
              <div class="text-xl font-bold text-green-600">{{ progressStats.completed }}</div>
              <div class="text-sm text-muted-foreground">Completed</div>
            </div>
            <div>
              <div class="text-xl font-bold text-blue-600">{{ progressStats.available }}</div>
              <div class="text-sm text-muted-foreground">Available</div>
            </div>
            <div>
              <div class="text-xl font-bold text-yellow-600">{{ progressStats.locked }}</div>
              <div class="text-sm text-muted-foreground">Locked</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Units Display -->
    <div v-if="isLoading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="space-y-3">
        <div class="h-6 bg-muted animate-pulse rounded w-32" />
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="j in 6" :key="j" class="h-32 bg-muted animate-pulse rounded" />
        </div>
      </div>
    </div>
    
    <div v-else-if="Object.keys(unitsBySemester).length > 0" class="space-y-6">
      <!-- Semester Groups -->
      <div
        v-for="(units, semesterName) in unitsBySemester"
        :key="semesterName"
        class="space-y-4"
      >
        <!-- Semester Header -->
        <div class="flex items-center gap-3">
          <h3 class="text-lg font-semibold">{{ semesterName }}</h3>
          <Badge variant="outline">
            {{ units.length }} {{ units.length === 1 ? 'Unit' : 'Units' }}
          </Badge>
        </div>

        <!-- Units Grid/List -->
        <div :class="[
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
            : 'space-y-3'
        ]">
          <UnitNode
            v-for="unit in units"
            :key="unit.id"
            :unit="unit"
            :status="getUnitStatus(unit)"
            :view-mode="viewMode"
          />
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <BookOpen class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <p class="text-muted-foreground">No units found</p>
      <p class="text-sm text-muted-foreground">
        Try adjusting your filter to see more units
      </p>
    </div>
  </div>
</template>
