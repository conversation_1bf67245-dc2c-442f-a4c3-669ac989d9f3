// Attendance-specific types for the lecturer portal

export type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused' | 'partial'
export type ParticipationLevel = 'excellent' | 'good' | 'average' | 'poor' | 'none'

// Bulk attendance operations
export interface BulkAttendanceUpdate {
  class_session_id: number
  updates: AttendanceUpdate[]
  notes?: string
}

export interface AttendanceUpdate {
  student_id: number
  status: AttendanceStatus
  check_in_time?: string
  check_out_time?: string
  minutes_late?: number
  participation_level?: ParticipationLevel
  participation_score?: number
  notes?: string
}

// Attendance analytics and reporting
export interface AttendanceAnalytics {
  course_offering_id: number
  period: {
    start_date: string
    end_date: string
    total_sessions: number
  }
  overall_statistics: {
    total_students: number
    average_attendance_rate: number
    trend: 'improving' | 'declining' | 'stable'
    best_attended_session: {
      session_id: number
      session_title: string
      attendance_rate: number
      date: string
    }
    worst_attended_session: {
      session_id: number
      session_title: string
      attendance_rate: number
      date: string
    }
  }
  student_breakdown: StudentAttendanceBreakdown[]
  session_breakdown: SessionAttendanceBreakdown[]
  patterns: AttendancePatterns
}

export interface StudentAttendanceBreakdown {
  student_id: number
  student_name: string
  student_email: string
  total_sessions: number
  attended_sessions: number
  late_sessions: number
  excused_absences: number
  unexcused_absences: number
  attendance_rate: number
  participation_average: number
  trend: 'improving' | 'declining' | 'stable'
  risk_level: 'low' | 'medium' | 'high' | 'critical'
  last_attendance: string
  consecutive_absences: number
}

export interface SessionAttendanceBreakdown {
  session_id: number
  session_title: string
  session_date: string
  session_type: string
  expected_attendees: number
  actual_attendees: number
  attendance_rate: number
  late_arrivals: number
  early_departures: number
  average_participation: number
  weather_factor?: string
  special_circumstances?: string
}

export interface AttendancePatterns {
  day_of_week_analysis: {
    [key: string]: {
      average_attendance: number
      session_count: number
    }
  }
  time_of_day_analysis: {
    morning: { average_attendance: number; session_count: number }
    afternoon: { average_attendance: number; session_count: number }
    evening: { average_attendance: number; session_count: number }
  }
  seasonal_trends: {
    [month: string]: {
      average_attendance: number
      session_count: number
    }
  }
  correlation_factors: {
    weather_impact: number
    exam_period_impact: number
    holiday_proximity_impact: number
  }
}

// Attendance alerts and notifications
export interface AttendanceAlert {
  id: number
  student_id: number
  student_name: string
  course_offering_id: number
  course_code: string
  alert_type: 'consecutive_absences' | 'low_attendance_rate' | 'sudden_drop' | 'perfect_attendance'
  severity: 'info' | 'warning' | 'critical'
  threshold_value: number
  current_value: number
  message: string
  action_suggestions: string[]
  created_at: string
  acknowledged_at?: string
  resolved_at?: string
}

// Attendance export and reporting
export interface AttendanceReport {
  report_id: string
  course_offering_id: number
  course_code: string
  course_name: string
  lecturer_name: string
  report_period: {
    start_date: string
    end_date: string
  }
  generated_at: string
  summary: {
    total_sessions: number
    total_students: number
    overall_attendance_rate: number
    students_at_risk: number
    perfect_attendance_students: number
  }
  detailed_data: StudentAttendanceBreakdown[]
  session_data: SessionAttendanceBreakdown[]
  recommendations: string[]
  export_format: 'pdf' | 'excel' | 'csv'
  file_url?: string
}

// Quick attendance marking interfaces
export interface QuickAttendanceSession {
  class_session_id: number
  course_code: string
  course_name: string
  session_title: string
  session_date: string
  start_time: string
  end_time: string
  location: string
  expected_students: EnrolledStudentQuick[]
  current_attendance: AttendanceRecord[]
  is_marked: boolean
  marked_at?: string
}

export interface EnrolledStudentQuick {
  student_id: number
  student_name: string
  student_email: string
  avatar_url?: string
  current_status?: AttendanceStatus
  attendance_rate: number
  recent_pattern: 'regular' | 'irregular' | 'declining' | 'improving'
}

export interface AttendanceRecord {
  id?: number
  student_id: number
  status: AttendanceStatus
  check_in_time?: string
  check_out_time?: string
  minutes_late: number
  participation_level?: ParticipationLevel
  participation_score?: number
  notes?: string
}

// Attendance validation and conflicts
export interface AttendanceValidation {
  is_valid: boolean
  errors: AttendanceError[]
  warnings: AttendanceWarning[]
  suggestions: string[]
}

export interface AttendanceError {
  student_id: number
  student_name: string
  error_type: 'duplicate_record' | 'invalid_time' | 'missing_required_field' | 'conflicting_status'
  message: string
  field?: string
}

export interface AttendanceWarning {
  student_id: number
  student_name: string
  warning_type: 'unusual_pattern' | 'late_submission' | 'inconsistent_data'
  message: string
  severity: 'low' | 'medium' | 'high'
}

// Attendance preferences and settings
export interface AttendanceSettings {
  lecturer_id: number
  auto_mark_late_threshold: number // minutes
  participation_scoring_enabled: boolean
  default_participation_score: number
  attendance_alert_thresholds: {
    consecutive_absences: number
    attendance_rate_warning: number
    attendance_rate_critical: number
  }
  notification_preferences: {
    email_alerts: boolean
    in_app_notifications: boolean
    daily_summary: boolean
    weekly_report: boolean
  }
  bulk_operations_enabled: boolean
  quick_mark_shortcuts: {
    [key: string]: AttendanceStatus
  }
}
