// Academic and Administrative Holds Types (aligned with database schema)
export type HoldSeverity = 'low' | 'medium' | 'high' | 'critical'
export type HoldType = 'financial' | 'academic' | 'administrative' | 'disciplinary' | 'health'

// Import types from other models
import type { Student, User, Program } from './student'

// Academic Support Models (aligned with database schema)
export interface AcademicHold {
  id: string
  student_id: string
  placed_by_user_id: string
  student: Student
  placed_by: User
}

export interface ProgramChangeRequest {
  id: string
  student_id: string
  from_program_id: string
  to_program_id: string
  student: Student
  from_program: Program
  to_program: Program
}

// Legacy Hold interface for backward compatibility
export interface Hold {
  id: string
  type: HoldType
  title: string
  description: string
  severity: HoldSeverity
  resolution_steps: string[]
  deadline?: string
  contact_info?: {
    name: string
    email: string
    phone?: string
    office?: string
  }
  created_at: string
  updated_at?: string
}

export interface HoldResolution {
  hold_id: string
  resolution_date: string
  resolved_by: string
  notes?: string
}
