// Student notification model types
export type NotificationType = 'academic' | 'finance' | 'system'
export type Priority = 'low' | 'medium' | 'high' | 'critical'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  read: boolean
  created_at: string
  action_url?: string
  priority: Priority
  source_avatar?: string
  metadata?: Record<string, any>
}

export interface NotificationFilters {
  category: 'all' | NotificationType
  read_status: 'all' | 'read' | 'unread'
}

export interface NotificationState {
  notifications: Notification[]
  filters: NotificationFilters
  isLoading: boolean
  error: string | null
  lastUpdated: string | null
  unreadCount: number
  isConnected: boolean
}

export interface NotificationPreferences {
  email_enabled: boolean
  push_enabled: boolean
  academic_alerts: boolean
  financial_alerts: boolean
  system_alerts: boolean
  digest_frequency: 'immediate' | 'daily' | 'weekly'
}