// Financial and Billing Types
export type PaymentStatus = 'pending' | 'paid' | 'overdue' | 'cancelled' | 'refunded' | 'partial'
export type PaymentMethod =
  | 'credit_card'
  | 'bank_transfer'
  | 'cash'
  | 'check'
  | 'financial_aid'
  | 'scholarship'
export type FeeType =
  | 'tuition'
  | 'lab_fee'
  | 'library_fee'
  | 'technology_fee'
  | 'parking'
  | 'health_insurance'
  | 'graduation_fee'
  | 'late_fee'
  | 'other'

export interface Fee {
  id: string
  name: string
  type: FeeType
  amount: number
  description?: string
  is_mandatory: boolean
  semester_id?: string
  course_id?: string
  due_date: string
}

export interface Bill {
  id: string
  student_id: string
  semester_id: string
  bill_date: string
  due_date: string
  total_amount: number
  paid_amount: number
  balance: number
  status: PaymentStatus
  fees: BillFee[]
  payments: Payment[]
  late_fees: number
  is_overdue: boolean
}

export interface BillFee {
  fee_id: string
  name: string
  type: FeeType
  amount: number
  description?: string
}

export interface Payment {
  id: string
  bill_id: string
  amount: number
  payment_date: string
  payment_method: PaymentMethod
  transaction_id?: string
  reference_number?: string
  notes?: string
}

export interface FinancialAid {
  id: string
  student_id: string
  type: 'scholarship' | 'grant' | 'loan' | 'work_study'
  name: string
  amount: number
  semester_id: string
  academic_year: string
  status: 'pending' | 'approved' | 'disbursed' | 'cancelled'
  disbursement_date?: string
  requirements?: string[]
  renewable: boolean
}

export interface FinancialSummary {
  student_id: string
  current_balance: number
  total_charges: number
  total_payments: number
  financial_aid_total: number
  outstanding_bills: Bill[]
  payment_history: Payment[]
  holds: FinancialHold[]
}

export interface FinancialHold {
  id: string
  student_id: string
  type: 'payment_overdue' | 'missing_documents' | 'other'
  description: string
  amount_owed?: number
  placed_date: string
  can_register: boolean
  can_graduate: boolean
  can_get_transcript: boolean
}
