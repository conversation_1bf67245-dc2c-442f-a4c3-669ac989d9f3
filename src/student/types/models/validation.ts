// Type validation utilities
import type { Student, Course, Grade, Bill } from './index'
import type { Notification } from './notification'

// Email validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Student ID validation (assuming format: YYYY-NNNNNN)
export const isValidStudentId = (studentId: string): boolean => {
  const studentIdRegex = /^\d{4}-\d{6}$/
  return studentIdRegex.test(studentId)
}

// Course code validation (assuming format: ABC123 or ABC1234)
export const isValidCourseCode = (courseCode: string): boolean => {
  const courseCodeRegex = /^[A-Z]{2,4}\d{3,4}$/
  return courseCodeRegex.test(courseCode)
}

// GPA validation (0.0 to 4.0 scale)
export const isValidGPA = (gpa: number): boolean => {
  return gpa >= 0.0 && gpa <= 4.0
}

// Credit hours validation
export const isValidCreditHours = (credits: number): boolean => {
  return credits > 0 && credits <= 6 && Number.isInteger(credits)
}

// Date validation helpers
export const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString)
  return !isNaN(date.getTime())
}

export const isFutureDate = (dateString: string): boolean => {
  const date = new Date(dateString)
  return date > new Date()
}

export const isPastDate = (dateString: string): boolean => {
  const date = new Date(dateString)
  return date < new Date()
}

// Time validation (HH:MM format)
export const isValidTime = (timeString: string): boolean => {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(timeString)
}

// Phone number validation (basic international format)
export const isValidPhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-()]{10,}$/
  return phoneRegex.test(phone)
}

// Student validation
export const validateStudent = (student: Partial<Student>): string[] => {
  const errors: string[] = []

  if (!student.email || !isValidEmail(student.email)) {
    errors.push('Valid email is required')
  }

  if (!student.student_id || !isValidStudentId(student.student_id)) {
    errors.push('Valid student ID is required (format: YYYY-NNNNNN)')
  }

  if (!student.name || student.name.trim().length < 2) {
    errors.push('Name must be at least 2 characters long')
  }

  if (student.phone && !isValidPhoneNumber(student.phone)) {
    errors.push('Invalid phone number format')
  }

  if (student.date_of_birth && !isValidDate(student.date_of_birth)) {
    errors.push('Invalid date of birth')
  }

  return errors
}

// Course validation
export const validateCourse = (course: Partial<Course>): string[] => {
  const errors: string[] = []

  if (!course.code || !isValidCourseCode(course.code)) {
    errors.push('Valid course code is required (format: ABC123)')
  }

  if (!course.name || course.name.trim().length < 3) {
    errors.push('Course name must be at least 3 characters long')
  }

  if (!course.credits || !isValidCreditHours(course.credits)) {
    errors.push('Valid credit hours required (1-6 integer)')
  }

  if (course.enrollment_capacity && course.enrollment_capacity < 1) {
    errors.push('Enrollment capacity must be at least 1')
  }

  return errors
}

// Grade validation
export const validateGrade = (grade: Partial<Grade>): string[] => {
  const errors: string[] = []

  if (!grade.student_id) {
    errors.push('Student ID is required')
  }

  if (!grade.course_id) {
    errors.push('Course ID is required')
  }

  if (grade.grade_points !== undefined && (grade.grade_points < 0 || grade.grade_points > 4)) {
    errors.push('Grade points must be between 0 and 4')
  }

  if (grade.credit_hours && !isValidCreditHours(grade.credit_hours)) {
    errors.push('Valid credit hours required (1-6 integer)')
  }

  return errors
}
