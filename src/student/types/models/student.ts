// Student Profile Types
export type EnrollmentStatus = 'active' | 'inactive' | 'graduated' | 'suspended' | 'withdrawn'
export type Gender = 'male' | 'female' | 'other' | 'prefer_not_to_say'

// Facilities Models (aligned with database schema)
export interface Campus {
  id: string
  name: string
  code: string
  address: string
  buildings: Building[]
}

export interface Building {
  id: string
  campus_id: string
  name: string
  code: string
  campus: Campus
}

export interface Room {
  id: string
  campus_id: string
  name: string
  code: string
  campus: Campus
}

// Academic Program Models (aligned with database schema)
export interface Program {
  id: string
  name: string
  code: string
  description: string
}

export interface Specialization {
  id: string
  program_id: string
  name: string
  code: string
  program: Program
}

// System Models
export interface Semester {
  id: string
  name: string
  code: string
}

export interface CurriculumVersion {
  id: string
  program_id: string
  specialization_id?: string
  semester_id: string
  program: Program
  specialization?: Specialization
  semester: Semester
}

// Student Profile (aligned with database schema)
export interface Student {
  id: string
  student_id: string
  full_name: string
  email: string
  program_id: string
  specialization_id?: string
  campus_id: string
  curriculum_version_id: string
  program: Program
  specialization?: Specialization
  campus: Campus
  curriculum_version: CurriculumVersion
  // Additional fields for UI
  phone?: string
  date_of_birth?: string
  gender?: Gender
  nationality?: string
  address?: string
  avatar_url?: string
  enrollment_status: EnrollmentStatus
  admission_date: string
  expected_graduation_date: string
  can_register: boolean
  has_active_holds: boolean
  created_at: string
  updated_at: string
}

// Course & Unit Models (aligned with database schema)
export interface Unit {
  id: string
  code: string
  name: string
  credit_points: number
}

export interface CurriculumUnit {
  id: string
  curriculum_version_id: string
  unit_id: string
  semester_id: string
  curriculum_version: CurriculumVersion
  unit: Unit
  semester: Semester
}

// Staff Models
export interface Lecturer {
  id: string
  employee_id: string
  email: string
  campus_id: string
  campus: Campus
}

export interface User {
  id: string
  name: string
  email: string
}

// Academic Progress Models (aligned with database schema)
export interface CreditProgress {
  earned_credits: number
  required_credits: number
  remaining_requirements: CurriculumUnit[]
  completion_percentage: number
}

export interface Enrollment {
  id: string
  student_id: string
  semester_id: string
  curriculum_version_id: string
  student: Student
  semester: Semester
  curriculum_version: CurriculumVersion
}

export interface AcademicStanding {
  id: string
  student_id: string
  semester_id: string
  student: Student
  semester: Semester
}

export interface GPACalculation {
  id: string
  student_id: string
  semester_id: string
  program_id: string
  student: Student
  semester: Semester
  program: Program
}

// Extended Semester interface with additional UI fields
export interface SemesterExtended extends Semester {
  start_date: string
  end_date: string
  academic_year: string
  is_current: boolean
  registration_start: string
  registration_end: string
  academic_calendar_url?: string
}

// Legacy GPAData interface for backward compatibility
export interface GPAData {
  current_gpa: number
  semester_gpa: number
  cumulative_gpa: number
  trend?: 'up' | 'down' | 'stable'
  academic_standing: string
  total_courses: number
  grade_distribution: Record<string, number>
}
