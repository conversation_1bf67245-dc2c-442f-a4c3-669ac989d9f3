// Student Profile Types
export type EnrollmentStatus = 'active' | 'inactive' | 'graduated' | 'suspended' | 'withdrawn'
export type Gender = 'male' | 'female' | 'other' | 'prefer_not_to_say'

export interface Campus {
  id: string
  name: string
  code: string
  address: string
  city: string
  country: string
}

export interface Program {
  id: string
  name: string
  code: string
  degree_type: 'bachelor' | 'master' | 'phd' | 'diploma' | 'certificate'
  duration_years: number
  total_credits: number
}

export interface Specialization {
  id: string
  name: string
  code: string
  program_id: string
  required_credits: number
}

export interface Student {
  id: string
  email: string
  name: string
  student_id: string
  full_name: string
  phone?: string
  date_of_birth?: string
  gender?: Gender
  nationality?: string
  address?: string
  avatar_url?: string
  enrollment_status: EnrollmentStatus
  admission_date: string
  expected_graduation_date: string
  campus: Campus
  program: Program
  specialization?: Specialization
  can_register: boolean
  has_active_holds: boolean
  created_at: string
  updated_at: string
}

export interface CreditProgress {
  earned_credits: number
  required_credits: number
  remaining_requirements: string[]
  completion_percentage: number
  credits_this_semester: number
  credits_in_progress: number
}

export interface Semester {
  id: string
  name: string
  code: string
  start_date: string
  end_date: string
  academic_year: string
  is_current: boolean
  registration_start: string
  registration_end: string
  academic_calendar_url?: string
}

export interface GPAData {
  current_gpa: number
  semester_gpa: number
  cumulative_gpa: number
  trend?: 'up' | 'down' | 'stable'
  academic_standing: string
  total_courses: number
  grade_distribution: Record<string, number>
}
