// Academic Records and Grades Types (aligned with database schema)
export type GradeScale =
  | 'A+'
  | 'A'
  | 'A-'
  | 'B+'
  | 'B'
  | 'B-'
  | 'C+'
  | 'C'
  | 'C-'
  | 'D+'
  | 'D'
  | 'F'
  | 'P'
  | 'NP'
  | 'W'
  | 'I'
  | 'AU'

// Import types from other models
import type { Student, Semester, Unit, CurriculumUnit } from './student'
import type { CourseOffering } from './course'

// Academic Records Models (aligned with database schema)
export interface AcademicRecord {
  id: string
  student_id: string
  course_offering_id: string
  semester_id: string
  unit_id: string
  student: Student
  course_offering: CourseOffering
  semester: Semester
  unit: Unit
}

// Assessment Models (aligned with database schema)
export interface Syllabus {
  id: string
  curriculum_unit_id: string
  curriculum_unit: CurriculumUnit
  assessment_components: AssessmentComponent[]
}

export interface AssessmentComponent {
  id: string
  syllabus_id: string
  syllabus: Syllabus
  assessment_component_details: AssessmentComponentDetail[]
}

export interface AssessmentComponentDetail {
  id: string
  component_id: string
  assessment_component: AssessmentComponent
  assessment_scores: AssessmentScore[]
}

export interface AssessmentScore {
  id: string
  assessment_component_detail_id: string
  student_id: string
  course_offering_id: string
  assessment_component_detail: AssessmentComponentDetail
  student: Student
  course_offering: CourseOffering
}

// Legacy Grade interface for backward compatibility
export interface Grade {
  id: string
  student_id: string
  course_id: string
  semester_id: string
  letter_grade: GradeScale
  grade_points: number
  credit_hours: number
  quality_points: number
  is_repeated: boolean
  is_transfer: boolean
  grade_date: string
  instructor_id: string
}

export interface GPA {
  semester_gpa: number
  cumulative_gpa: number
  total_credits_attempted: number
  total_credits_earned: number
  total_quality_points: number
  class_rank?: number
  class_size?: number
  academic_standing: 'good_standing' | 'probation' | 'suspension' | 'dismissal' | 'honors'
}

export interface Transcript {
  student_id: string
  generated_date: string
  semesters: TranscriptSemester[]
  overall_gpa: GPA
  degree_requirements: DegreeRequirement[]
  honors_awards: Honor[]
  transfer_credits: TransferCredit[]
}

export interface TranscriptSemester {
  semester_id: string
  semester_name: string
  year: string
  courses: TranscriptCourse[]
  semester_gpa: number
  credits_attempted: number
  credits_earned: number
}

export interface TranscriptCourse {
  course_code: string
  course_name: string
  credits: number
  grade: GradeScale
  grade_points: number
  quality_points: number
}

export interface DegreeRequirement {
  id: string
  category: string
  name: string
  required_credits: number
  earned_credits: number
  is_completed: boolean
  courses_applied: string[]
  remaining_courses?: string[]
}

export interface Honor {
  id: string
  name: string
  semester_id: string
  date_awarded: string
  description?: string
}

export interface TransferCredit {
  id: string
  institution_name: string
  course_code: string
  course_name: string
  credits: number
  grade?: string
  equivalent_course_id?: string
  semester_applied: string
}
