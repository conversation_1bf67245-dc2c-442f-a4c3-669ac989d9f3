// Course and Academic Types
export type CourseStatus = 'available' | 'full' | 'cancelled' | 'waitlist' | 'closed'
export type RegistrationStatus = 'idle' | 'registering' | 'success' | 'error' | 'conflict'
export type ClassType = 'lecture' | 'lab' | 'tutorial' | 'exam' | 'seminar'
export type DeliveryMode = 'in_person' | 'online' | 'hybrid'
export type SubmissionStatus = 'not_submitted' | 'submitted' | 'graded' | 'late'

export interface Instructor {
  id: string
  name: string
  email: string
  title: string
  department: string
  avatar_url?: string
  office_location?: string
  office_hours?: string
}

export interface Schedule {
  id: string
  day_of_week: number // 0 = Sunday, 1 = Monday, etc.
  start_time: string // HH:MM format
  end_time: string // HH:MM format
  location: string
  room?: string
  building?: string
  class_type: ClassType
  delivery_mode: DeliveryMode
  zoom_link?: string
  recurring: boolean
  start_date: string
  end_date: string
}

export interface Course {
  id: string
  code: string
  name: string
  credits: number
  description?: string
  prerequisites: string[]
  corequisites?: string[]
  semester: number
  year: number
  status: CourseStatus
  instructor: Instructor
  schedule: Schedule[]
  enrollment_capacity: number
  enrolled_count: number
  waitlist_count?: number
  department: string
  level: number // 100, 200, 300, 400 level courses
  is_core: boolean
  is_elective: boolean
  syllabus_url?: string
  created_at: string
  updated_at: string
}

export interface CourseRegistration {
  id: string
  student_id: string
  course_id: string
  registration_date: string
  status: 'registered' | 'dropped' | 'completed' | 'failed'
  grade?: string
  points?: number
  semester_id: string
  is_audit: boolean
  withdrawal_date?: string
}

export interface TimeConflict {
  course_id: string
  conflicting_course_id: string
  conflict_type: 'time_overlap' | 'prerequisite_missing' | 'corequisite_missing'
  message: string
  severity: 'error' | 'warning'
}

export interface RegistrationCart {
  courses: Course[]
  total_credits: number
  conflicts: TimeConflict[]
  estimated_fees: number
  is_overload: boolean
  overload_limit: number
}

export interface Assignment {
  id: string
  course_id: string
  title: string
  description: string
  due_date: string
  total_points: number
  submission_status: SubmissionStatus
  grade?: number
  feedback?: string
  attachments?: Array<{
    name: string
    url: string
    size: number
  }>
  created_at: string
  updated_at?: string
}
