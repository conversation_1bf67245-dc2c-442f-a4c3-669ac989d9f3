// API Response Types (aligned with database schema)

import type { 
  Student, 
  <PERSON><PERSON><PERSON>, 
  CreditProgress, 
  GPACalculation, 
  AcademicStanding, 
  Enrollment,
  SemesterExtended 
} from '../models/student'
import type { CourseOffering, CourseRegistration, ClassSession } from '../models/course'
import type { AcademicHold } from '../models/hold'
import type { AssessmentComponentDetail, AcademicRecord, AssessmentScore } from '../models/academic'
import type { Attendance } from '../models/attendance'

// Standardized API Response
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: Record<string, string[]>
  meta?: {
    pagination?: PaginationMeta
    filters?: FilterMeta
  }
}

export interface PaginationMeta {
  current_page: number
  per_page: number
  total: number
  last_page: number
  from: number
  to: number
}

export interface FilterMeta {
  applied_filters: Record<string, any>
  available_filters: Record<string, any[]>
}

// Dashboard API Response (aligned with database schema)
export interface DashboardResponse {
  current_semester: SemesterExtended & {
    academic_calendar_url: string
  }
  credit_progress: CreditProgress
  current_gpa: GPACalculation & {
    rank?: number
    trend: 'up' | 'down' | 'stable'
    semester_gpa: number
    cumulative_gpa: number
  }
  academic_holds: AcademicHold[]
  upcoming_assessments: AssessmentComponentDetail[]
  enrollment_status: Enrollment
  academic_standing: AcademicStanding
}

// Course Registration API Response
export interface CourseRegistrationResponse {
  available_offerings: CourseOffering[]
  registered_offerings: CourseRegistration[]
  registration_period: {
    start_date: string
    end_date: string
    is_open: boolean
  }
  credit_limits: {
    minimum: number
    maximum: number
    overload_threshold: number
  }
}

// Timetable API Response
export interface TimetableResponse {
  class_sessions: ClassSession[]
  semester: Semester
  week_range: {
    start_date: string
    end_date: string
  }
}

// Grades API Response
export interface GradesResponse {
  academic_records: AcademicRecord[]
  assessment_scores: AssessmentScore[]
  gpa_calculations: GPACalculation[]
  semester_summaries: {
    semester: Semester
    total_credits: number
    semester_gpa: number
    units_completed: number
  }[]
}

// Attendance API Response
export interface AttendanceResponse {
  attendance_records: Attendance[]
  attendance_summary: {
    course_offering: CourseOffering
    total_sessions: number
    attended_sessions: number
    attendance_percentage: number
    minimum_required: number
    status: 'good' | 'warning' | 'critical'
  }[]
}

// Profile API Response
export interface ProfileResponse {
  student: Student
  study_plan: {
    curriculum_version: any // Will be defined when curriculum components are implemented
    progress: CreditProgress
  }
}
