import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useBase<PERSON><PERSON> } from '@/shared/composables/useBaseApi'
import type { ClassSession } from '../types/models/course'
import type { Semester } from '../types/models/student'
import type { TimetableResponse } from '../types/api/responses'

export const useTimetableStore = defineStore('timetable', () => {
  // API client
  const api = useBaseApi()

  // State
  const classSessions = ref<ClassSession[]>([])
  const currentSemester = ref<Semester | null>(null)
  const weekRange = ref<{ start_date: string; end_date: string } | null>(null)
  const selectedWeek = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // Computed properties
  const sessionsGroupedByDay = computed(() => {
    const grouped: Record<string, ClassSession[]> = {}
    
    classSessions.value.forEach((session) => {
      // We'll need to add day_of_week to ClassSession or derive it from session timing
      // For now, we'll use a placeholder approach
      const dayKey = 'monday' // This should be derived from session data
      if (!grouped[dayKey]) {
        grouped[dayKey] = []
      }
      grouped[dayKey].push(session)
    })
    
    return grouped
  })

  const hasSessionsThisWeek = computed(() => classSessions.value.length > 0)

  const upcomingSessions = computed(() => {
    const now = new Date()
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // Filter sessions happening in the next 24 hours
    // Note: We'll need to add timing information to ClassSession
    return classSessions.value.filter(() => {
      // Placeholder logic - should check session timing
      return true
    })
  })

  // Actions
  const fetchTimetable = async (semesterId: string, weekStart?: string) => {
    isLoading.value = true
    error.value = null

    try {
      const params = new URLSearchParams({ semester_id: semesterId })
      if (weekStart) {
        params.append('week_start', weekStart)
        selectedWeek.value = weekStart
      }

      const response = await api.get<TimetableResponse>(`/api/student/timetable?${params}`)

      if (response.success && response.data) {
        classSessions.value = response.data.class_sessions
        currentSemester.value = response.data.semester
        weekRange.value = response.data.week_range
      }

      lastUpdated.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch timetable'
      console.error('Timetable fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const navigateToWeek = async (weekStart: string) => {
    if (currentSemester.value) {
      await fetchTimetable(currentSemester.value.id, weekStart)
    }
  }

  const navigateToToday = async () => {
    const today = new Date()
    const monday = new Date(today)
    monday.setDate(today.getDate() - today.getDay() + 1) // Get Monday of current week
    
    const weekStart = monday.toISOString().split('T')[0]
    await navigateToWeek(weekStart)
  }

  const refreshTimetable = async () => {
    if (currentSemester.value) {
      await fetchTimetable(currentSemester.value.id, selectedWeek.value || undefined)
    }
  }

  const getSessionsForDay = (dayOfWeek: number) => {
    // Filter sessions for a specific day
    // Note: We'll need to add day information to ClassSession
    return classSessions.value.filter(() => {
      // Placeholder logic - should check session day
      return true
    })
  }

  const resetStore = () => {
    classSessions.value = []
    currentSemester.value = null
    weekRange.value = null
    selectedWeek.value = null
    error.value = null
    lastUpdated.value = null
  }

  return {
    // State
    classSessions,
    currentSemester,
    weekRange,
    selectedWeek,
    isLoading,
    error,
    lastUpdated,

    // Computed
    sessionsGroupedByDay,
    hasSessionsThisWeek,
    upcomingSessions,

    // Actions
    fetchTimetable,
    navigateToWeek,
    navigateToToday,
    refreshTimetable,
    getSessionsForDay,
    resetStore,
  }
})
