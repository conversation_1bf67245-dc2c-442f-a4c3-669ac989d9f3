import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useApi } from '@/shared/composables/useBaseApi'
import type { Se<PERSON>ter, CreditProgress } from '../types/models/student'
import type { Hold } from '../types/models/hold'
import type { Assignment } from '../types/models/course'

export const useDashboardStore = defineStore('dashboard', () => {
  // API client
  const api = useBaseApi()

  // State
  const currentSemester = ref<Semester | null>(null)
  const creditProgress = ref<CreditProgress | null>(null)
  const currentGPA = ref<number | null>(null)
  const gpaTrend = ref<'up' | 'down' | 'stable' | null>(null)
  const academicHolds = ref<Hold[]>([])
  const upcomingDeadlines = ref<Assignment[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // Computed properties
  const hasHolds = computed(() => academicHolds.value.length > 0)

  const criticalHolds = computed(() =>
    academicHolds.value.filter((hold: Hold) => hold.severity === 'critical'),
  )

  const gpaStatus = computed(() => {
    if (currentGPA.value === null) return null
    if (currentGPA.value >= 3.2) return 'good'
    if (currentGPA.value >= 2.0) return 'warning'
    return 'danger'
  })

  const creditCompletionPercentage = computed(() => {
    if (!creditProgress.value) return 0
    return Math.round(
      (creditProgress.value.earned_credits / creditProgress.value.required_credits) * 100,
    )
  })

  const remainingCredits = computed(() => {
    if (!creditProgress.value) return 0
    return creditProgress.value.required_credits - creditProgress.value.earned_credits
  })

  const hasUpcomingDeadlines = computed(() => upcomingDeadlines.value.length > 0)

  const urgentDeadlines = computed(() => {
    const now = new Date()
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)

    return upcomingDeadlines.value.filter((deadline) => {
      const dueDate = new Date(deadline.due_date)
      return dueDate <= tomorrow
    })
  })

  // Actions
  const fetchDashboardData = async () => {
    isLoading.value = true
    error.value = null

    try {
      // Make parallel API calls for better performance
      const [semesterResponse, creditsResponse, gpaResponse, holdsResponse, deadlinesResponse] =
        await Promise.all([
          api.dashboard.getCurrentSemester(),
          api.dashboard.getCreditProgress(),
          api.dashboard.getGPA(),
          api.dashboard.getHolds(),
          api.dashboard.getUpcomingDeadlines(),
        ])

      // Update state with responses
      if (semesterResponse.success && semesterResponse.data) {
        currentSemester.value = semesterResponse.data as Semester
      }

      if (creditsResponse.success && creditsResponse.data) {
        creditProgress.value = creditsResponse.data as CreditProgress
      }

      if (gpaResponse.success && gpaResponse.data) {
        currentGPA.value = gpaResponse.data.gpa as number
        gpaTrend.value = gpaResponse.data.trend as 'up' | 'down' | 'stable'
      }

      if (holdsResponse.success && holdsResponse.data) {
        academicHolds.value = holdsResponse.data as Hold[]
      }

      if (deadlinesResponse.success && deadlinesResponse.data) {
        upcomingDeadlines.value = deadlinesResponse.data as Assignment[]
      }

      lastUpdated.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch dashboard data'
      console.error('Dashboard data fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    return fetchDashboardData()
  }

  const clearHold = async (holdId: string) => {
    try {
      // This would be a real API call in production
      // await api.post(`/dashboard/holds/${holdId}/clear`)

      // For now, just remove it from the local state
      academicHolds.value = academicHolds.value.filter((hold: Hold) => hold.id !== holdId)
      return { success: true }
    } catch (err) {
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to clear hold',
      }
    }
  }

  return {
    // State
    currentSemester,
    creditProgress,
    currentGPA,
    gpaTrend,
    academicHolds,
    upcomingDeadlines,
    isLoading,
    error,
    lastUpdated,

    // Computed
    hasHolds,
    criticalHolds,
    gpaStatus,
    creditCompletionPercentage,
    remainingCredits,
    hasUpcomingDeadlines,
    urgentDeadlines,

    // Actions
    fetchDashboardData,
    refreshData,
    clearHold,
  }
})
