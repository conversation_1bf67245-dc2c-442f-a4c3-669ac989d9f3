import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useBase<PERSON><PERSON> } from '@/shared/composables/useBaseApi'
import type {
  SemesterExtended,
  CreditProgress,
  GPACalculation,
  AcademicStanding,
  Enrollment,
} from '../types/models/student'
import type { AcademicHold, Hold } from '../types/models/hold'
import type { AssessmentComponentDetail } from '../types/models/academic'
import type { Assignment } from '../types/models/course'
import type { DashboardResponse } from '../types/api/responses'

export const useDashboardStore = defineStore('dashboard', () => {
  // API client
  const api = useBaseApi()

  // State (updated for database schema)
  const currentSemester = ref<SemesterExtended | null>(null)
  const creditProgress = ref<CreditProgress | null>(null)
  const currentGPA = ref<GPACalculation | null>(null)
  const academicHolds = ref<AcademicHold[]>([])
  const upcomingAssessments = ref<AssessmentComponentDetail[]>([])
  const enrollmentStatus = ref<Enrollment | null>(null)
  const academicStanding = ref<AcademicStanding | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // Legacy fields for backward compatibility
  const upcomingDeadlines = ref<Assignment[]>([])
  const legacyHolds = ref<Hold[]>([])

  // Computed properties (updated for database schema)
  const hasHolds = computed(() => academicHolds.value.length > 0)

  // For legacy compatibility, we'll need to extend AcademicHold with UI properties
  const criticalHolds = computed(() =>
    legacyHolds.value.filter((hold: Hold) => hold.severity === 'critical'),
  )

  const gpaStatus = computed(() => {
    // Note: GPACalculation doesn't have a direct GPA value, we'll need to add this to the API response
    // For now, we'll use a placeholder approach
    return null // Will be updated when API response includes actual GPA value
  })

  const creditCompletionPercentage = computed(() => {
    if (!creditProgress.value) return 0
    return Math.round(
      (creditProgress.value.earned_credits / creditProgress.value.required_credits) * 100,
    )
  })

  const remainingCredits = computed(() => {
    if (!creditProgress.value) return 0
    return creditProgress.value.required_credits - creditProgress.value.earned_credits
  })

  const hasUpcomingDeadlines = computed(() => upcomingDeadlines.value.length > 0)

  const urgentDeadlines = computed(() => {
    const now = new Date()
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)

    return upcomingDeadlines.value.filter((deadline) => {
      const dueDate = new Date(deadline.due_date)
      return dueDate <= tomorrow
    })
  })

  // Actions (updated for database schema)
  const fetchDashboardData = async () => {
    isLoading.value = true
    error.value = null

    try {
      // Use single API call for dashboard data (aligned with database schema)
      const response = await api.get<DashboardResponse>('/api/student/dashboard')

      if (response.success && response.data) {
        const data = response.data

        // Update state with new database-aligned data
        currentSemester.value = data.current_semester
        creditProgress.value = data.credit_progress
        currentGPA.value = data.current_gpa
        academicHolds.value = data.academic_holds
        upcomingAssessments.value = data.upcoming_assessments
        enrollmentStatus.value = data.enrollment_status
        academicStanding.value = data.academic_standing
      }

      lastUpdated.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch dashboard data'
      console.error('Dashboard data fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    return fetchDashboardData()
  }

  const clearHold = async (holdId: string) => {
    try {
      // This would be a real API call in production
      // await api.post(`/dashboard/holds/${holdId}/clear`)

      // For now, just remove it from the local state
      academicHolds.value = academicHolds.value.filter((hold) => hold.id !== holdId)
      return { success: true }
    } catch (err) {
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to clear hold',
      }
    }
  }

  return {
    // State (updated for database schema)
    currentSemester,
    creditProgress,
    currentGPA,
    academicHolds,
    upcomingAssessments,
    enrollmentStatus,
    academicStanding,
    isLoading,
    error,
    lastUpdated,

    // Legacy state for backward compatibility
    upcomingDeadlines,
    legacyHolds,

    // Computed
    hasHolds,
    criticalHolds,
    gpaStatus,
    creditCompletionPercentage,
    remainingCredits,
    hasUpcomingDeadlines,
    urgentDeadlines,

    // Actions
    fetchDashboardData,
    refreshData,
    clearHold,
  }
})
