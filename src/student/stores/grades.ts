import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useBase<PERSON><PERSON> } from '@/shared/composables/useBaseApi'
import type { AcademicRecord, AssessmentScore } from '../types/models/academic'
import type { GPACalculation, Semester } from '../types/models/student'
import type { GradesResponse } from '../types/api/responses'

export const useGradesStore = defineStore('grades', () => {
  // API client
  const api = useBaseApi()

  // State
  const academicRecords = ref<AcademicRecord[]>([])
  const assessmentScores = ref<AssessmentScore[]>([])
  const gpaCalculations = ref<GPACalculation[]>([])
  const semesterSummaries = ref<{
    semester: Semester
    total_credits: number
    semester_gpa: number
    units_completed: number
  }[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // Computed properties
  const recordsBySemester = computed(() => {
    const grouped: Record<string, AcademicRecord[]> = {}
    
    academicRecords.value.forEach((record) => {
      const semesterId = record.semester_id
      if (!grouped[semesterId]) {
        grouped[semesterId] = []
      }
      grouped[semesterId].push(record)
    })
    
    return grouped
  })

  const currentGPA = computed(() => {
    if (gpaCalculations.value.length === 0) return null
    
    // Get the most recent GPA calculation
    const sortedGPAs = [...gpaCalculations.value].sort((a, b) => 
      new Date(b.semester.code).getTime() - new Date(a.semester.code).getTime()
    )
    
    return sortedGPAs[0]
  })

  const gpaHistory = computed(() => {
    return gpaCalculations.value
      .sort((a, b) => new Date(a.semester.code).getTime() - new Date(b.semester.code).getTime())
      .map((gpa) => ({
        semester: gpa.semester.name,
        gpa: 0, // Will need to add actual GPA value to GPACalculation interface
        credits: 0, // Will need to add credits to interface
      }))
  })

  const assessmentsByOffering = computed(() => {
    const grouped: Record<string, AssessmentScore[]> = {}
    
    assessmentScores.value.forEach((score) => {
      const offeringId = score.course_offering_id
      if (!grouped[offeringId]) {
        grouped[offeringId] = []
      }
      grouped[offeringId].push(score)
    })
    
    return grouped
  })

  const hasGrades = computed(() => academicRecords.value.length > 0)

  const completedUnits = computed(() => {
    return academicRecords.value.filter((record) => {
      // Filter for completed units - will need grade information in AcademicRecord
      return true // Placeholder
    }).length
  })

  // Actions
  const fetchGrades = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.get<GradesResponse>('/api/student/grades')

      if (response.success && response.data) {
        academicRecords.value = response.data.academic_records
        assessmentScores.value = response.data.assessment_scores
        gpaCalculations.value = response.data.gpa_calculations
        semesterSummaries.value = response.data.semester_summaries
      }

      lastUpdated.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch grades'
      console.error('Grades fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchGradesBySemester = async (semesterId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.get<GradesResponse>(`/api/student/grades/semester/${semesterId}`)

      if (response.success && response.data) {
        // Update only the records for this semester
        const newRecords = response.data.academic_records
        academicRecords.value = [
          ...academicRecords.value.filter(r => r.semester_id !== semesterId),
          ...newRecords
        ]
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch semester grades'
      console.error('Semester grades fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchAssessmentScores = async (courseOfferingId: string) => {
    try {
      const response = await api.get<{ assessment_scores: AssessmentScore[] }>(
        `/api/student/assessments/course-offering/${courseOfferingId}`
      )

      if (response.success && response.data) {
        // Update assessment scores for this course offering
        const newScores = response.data.assessment_scores
        assessmentScores.value = [
          ...assessmentScores.value.filter(s => s.course_offering_id !== courseOfferingId),
          ...newScores
        ]
      }
    } catch (err) {
      console.error('Assessment scores fetch error:', err)
    }
  }

  const refreshGrades = async () => {
    await fetchGrades()
  }

  const getGradesForSemester = (semesterId: string) => {
    return academicRecords.value.filter(record => record.semester_id === semesterId)
  }

  const getAssessmentsForOffering = (courseOfferingId: string) => {
    return assessmentScores.value.filter(score => score.course_offering_id === courseOfferingId)
  }

  const resetStore = () => {
    academicRecords.value = []
    assessmentScores.value = []
    gpaCalculations.value = []
    semesterSummaries.value = []
    error.value = null
    lastUpdated.value = null
  }

  return {
    // State
    academicRecords,
    assessmentScores,
    gpaCalculations,
    semesterSummaries,
    isLoading,
    error,
    lastUpdated,

    // Computed
    recordsBySemester,
    currentGPA,
    gpaHistory,
    assessmentsByOffering,
    hasGrades,
    completedUnits,

    // Actions
    fetchGrades,
    fetchGradesBySemester,
    fetchAssessmentScores,
    refreshGrades,
    getGradesForSemester,
    getAssessmentsForOffering,
    resetStore,
  }
})
