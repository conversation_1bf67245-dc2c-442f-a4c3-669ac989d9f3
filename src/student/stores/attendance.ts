import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useBaseApi } from '@/shared/composables/useBaseApi'
import type { Attendance } from '../types/models/attendance'
import type { CourseOffering } from '../types/models/course'
import type { AttendanceResponse } from '../types/api/responses'

export const useAttendanceStore = defineStore('attendance', () => {
  // API client
  const api = useBaseApi()

  // State
  const attendanceRecords = ref<Attendance[]>([])
  const attendanceSummary = ref<{
    course_offering: CourseOffering
    total_sessions: number
    attended_sessions: number
    attendance_percentage: number
    minimum_required: number
    status: 'good' | 'warning' | 'critical'
  }[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // Computed properties
  const attendanceByOffering = computed(() => {
    const grouped: Record<string, Attendance[]> = {}
    
    attendanceRecords.value.forEach((record) => {
      const offeringId = record.class_session.course_offering_id
      if (!grouped[offeringId]) {
        grouped[offeringId] = []
      }
      grouped[offeringId].push(record)
    })
    
    return grouped
  })

  const overallAttendanceRate = computed(() => {
    if (attendanceSummary.value.length === 0) return 0
    
    const totalSessions = attendanceSummary.value.reduce((sum, summary) => sum + summary.total_sessions, 0)
    const attendedSessions = attendanceSummary.value.reduce((sum, summary) => sum + summary.attended_sessions, 0)
    
    return totalSessions > 0 ? Math.round((attendedSessions / totalSessions) * 100) : 0
  })

  const criticalAttendanceOfferings = computed(() => {
    return attendanceSummary.value.filter(summary => summary.status === 'critical')
  })

  const warningAttendanceOfferings = computed(() => {
    return attendanceSummary.value.filter(summary => summary.status === 'warning')
  })

  const goodAttendanceOfferings = computed(() => {
    return attendanceSummary.value.filter(summary => summary.status === 'good')
  })

  const hasAttendanceIssues = computed(() => {
    return criticalAttendanceOfferings.value.length > 0 || warningAttendanceOfferings.value.length > 0
  })

  const attendanceAlerts = computed(() => {
    const alerts: Array<{
      type: 'critical' | 'warning'
      message: string
      courseOffering: CourseOffering
      percentage: number
    }> = []

    criticalAttendanceOfferings.value.forEach(summary => {
      alerts.push({
        type: 'critical',
        message: `Attendance below 75% - Risk of exam restriction`,
        courseOffering: summary.course_offering,
        percentage: summary.attendance_percentage
      })
    })

    warningAttendanceOfferings.value.forEach(summary => {
      alerts.push({
        type: 'warning',
        message: `Attendance below 80% - Monitor closely`,
        courseOffering: summary.course_offering,
        percentage: summary.attendance_percentage
      })
    })

    return alerts
  })

  // Actions
  const fetchAttendance = async (semesterId?: string) => {
    isLoading.value = true
    error.value = null

    try {
      const params = semesterId ? `?semester_id=${semesterId}` : ''
      const response = await api.get<AttendanceResponse>(`/api/student/attendance${params}`)

      if (response.success && response.data) {
        attendanceRecords.value = response.data.attendance_records
        attendanceSummary.value = response.data.attendance_summary
      }

      lastUpdated.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch attendance'
      console.error('Attendance fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchAttendanceForOffering = async (courseOfferingId: string) => {
    try {
      const response = await api.get<{ attendance_records: Attendance[] }>(
        `/api/student/attendance/course-offering/${courseOfferingId}`
      )

      if (response.success && response.data) {
        // Update attendance records for this course offering
        const newRecords = response.data.attendance_records
        attendanceRecords.value = [
          ...attendanceRecords.value.filter(r => r.class_session.course_offering_id !== courseOfferingId),
          ...newRecords
        ]
      }
    } catch (err) {
      console.error('Course offering attendance fetch error:', err)
    }
  }

  const refreshAttendance = async () => {
    await fetchAttendance()
  }

  const getAttendanceForOffering = (courseOfferingId: string) => {
    return attendanceRecords.value.filter(record => 
      record.class_session.course_offering_id === courseOfferingId
    )
  }

  const getAttendanceSummaryForOffering = (courseOfferingId: string) => {
    return attendanceSummary.value.find(summary => 
      summary.course_offering.id === courseOfferingId
    )
  }

  const calculateAttendanceRate = (courseOfferingId: string) => {
    const records = getAttendanceForOffering(courseOfferingId)
    if (records.length === 0) return 0

    // This is a simplified calculation - in reality, we'd need to know total sessions
    // and count present vs absent records
    const presentCount = records.length // Placeholder - should count actual present records
    const totalSessions = records.length // Placeholder - should get total sessions from API
    
    return totalSessions > 0 ? Math.round((presentCount / totalSessions) * 100) : 0
  }

  const resetStore = () => {
    attendanceRecords.value = []
    attendanceSummary.value = []
    error.value = null
    lastUpdated.value = null
  }

  return {
    // State
    attendanceRecords,
    attendanceSummary,
    isLoading,
    error,
    lastUpdated,

    // Computed
    attendanceByOffering,
    overallAttendanceRate,
    criticalAttendanceOfferings,
    warningAttendanceOfferings,
    goodAttendanceOfferings,
    hasAttendanceIssues,
    attendanceAlerts,

    // Actions
    fetchAttendance,
    fetchAttendanceForOffering,
    refreshAttendance,
    getAttendanceForOffering,
    getAttendanceSummaryForOffering,
    calculateAttendanceRate,
    resetStore,
  }
})
