import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useBase<PERSON>pi } from '@/shared/composables/useBaseApi'
import type {
  CourseOffering,
  CourseRegistration,
  Course,
  TimeConflict,
  RegistrationStatus,
  Schedule,
} from '../types/models/course'
import type { CourseRegistrationResponse } from '../types/api/responses'
import type { ApiResponse } from '@/shared/types/api/api'

export const useCourseStore = defineStore('course', () => {
  // API client
  const api = useBaseApi()

  // State (updated for database schema)
  const availableOfferings = ref<CourseOffering[]>([])
  const registeredOfferings = ref<CourseRegistration[]>([])
  const selectedOfferings = ref<CourseOffering[]>([])
  const registrationStatus = ref<RegistrationStatus>('idle')
  const conflicts = ref<TimeConflict[]>([])
  const registrationPeriod = ref<{
    start_date: string
    end_date: string
    is_open: boolean
  } | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const currentSemesterId = ref<string | null>(null)
  const totalCredits = ref(0)
  const maxCredits = ref(18) // Default max credits per semester

  // Legacy state for backward compatibility
  const availableCourses = ref<Course[]>([])
  const registeredCourses = ref<Course[]>([])
  const selectedCourses = ref<Course[]>([])

  // Computed properties (updated for database schema)
  const hasConflicts = computed(() => conflicts.value.length > 0)

  const isOverloaded = computed(() => totalCredits.value > maxCredits.value)

  const availableOfferingsFiltered = computed(() => {
    return availableOfferings.value.filter(
      (offering) =>
        !selectedOfferings.value.some((selected) => selected.id === offering.id) &&
        !registeredOfferings.value.some(
          (registered) => registered.course_offering_id === offering.id,
        ),
    )
  })

  const selectedOfferingsWithStatus = computed(() => {
    return selectedOfferings.value.map((offering) => {
      const offeringConflicts = conflicts.value.filter(
        (conflict) =>
          conflict.course_id === offering.id || conflict.conflicting_course_id === offering.id,
      )

      return {
        ...offering,
        hasConflicts: offeringConflicts.length > 0,
        conflicts: offeringConflicts,
      }
    })
  })

  // Legacy computed properties for backward compatibility
  const availableCoursesFiltered = computed(() => {
    return availableCourses.value.filter(
      (course) =>
        !selectedCourses.value.some((selected) => selected.id === course.id) &&
        !registeredCourses.value.some((registered) => registered.id === course.id),
    )
  })

  const selectedCoursesWithStatus = computed(() => {
    return selectedCourses.value.map((course) => {
      const courseConflicts = conflicts.value.filter(
        (conflict) =>
          conflict.course_id === course.id || conflict.conflicting_course_id === course.id,
      )

      return {
        ...course,
        hasConflicts: courseConflicts.length > 0,
        conflicts: courseConflicts,
      }
    })
  })

  // Calculate total credits whenever selected courses change
  const updateTotalCredits = () => {
    totalCredits.value = [...selectedCourses.value, ...registeredCourses.value].reduce(
      (sum, course) => sum + course.credits,
      0,
    )
  }

  // Actions
  const fetchAvailableCourses = async (semesterId?: string) => {
    isLoading.value = true
    error.value = null

    try {
      if (semesterId) {
        currentSemesterId.value = semesterId
      }

      const response = await api.courses.getAvailable(currentSemesterId.value || undefined)

      if (response.success && response.data) {
        availableCourses.value = response.data
      } else {
        throw new Error(response.message || 'Failed to fetch available courses')
      }
    } catch (err) {
      error.value = 'Failed to fetch available courses'
      console.error('Available courses fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchRegisteredCourses = async (semesterId?: string) => {
    isLoading.value = true
    error.value = null

    try {
      if (semesterId) {
        currentSemesterId.value = semesterId
      }

      const response = await api.courses.getRegistered(currentSemesterId.value || undefined)

      if (response.success && response.data) {
        registeredCourses.value = response.data
        updateTotalCredits()
      } else {
        throw new Error(response.message || 'Failed to fetch registered courses')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch registered courses'
      console.error('Registered courses fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const checkConflicts = async () => {
    if (selectedCourses.value.length === 0) {
      conflicts.value = []
      return { success: true, conflicts: [] }
    }

    try {
      const allCourseIds = [
        ...selectedCourses.value.map((c) => c.id),
        ...registeredCourses.value.map((c) => c.id),
      ]

      const response = await api.courses.checkConflicts(allCourseIds)

      if (response.success && response.data) {
        conflicts.value = response.data.conflicts || []
        return { success: true, conflicts: conflicts.value }
      } else {
        throw new Error(response.message || 'Failed to check for conflicts')
      }
    } catch (err) {
      console.error('Conflict check error:', err)
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to check for conflicts',
      }
    }
  }

  // Detect time conflicts locally without API call
  const detectTimeConflicts = (courses: Course[]): TimeConflict[] => {
    const conflicts: TimeConflict[] = []
    const allCourses = [...courses, ...registeredCourses.value]

    // Check each course against all others for time overlaps
    for (let i = 0; i < allCourses.length; i++) {
      for (let j = i + 1; j < allCourses.length; j++) {
        const course1 = allCourses[i]
        const course2 = allCourses[j]

        // Check for schedule overlaps
        const hasOverlap = detectScheduleOverlap(course1.schedule, course2.schedule)

        if (hasOverlap) {
          conflicts.push({
            course_id: course1.id,
            conflicting_course_id: course2.id,
            conflict_type: 'time_overlap',
            message: `Time conflict between ${course1.code} and ${course2.code}`,
            severity: 'error',
          })
        }

        // Check for missing prerequisites
        if (course2.prerequisites.includes(course1.code)) {
          conflicts.push({
            course_id: course2.id,
            conflicting_course_id: course1.id,
            conflict_type: 'prerequisite_missing',
            message: `${course1.code} is a prerequisite for ${course2.code}`,
            severity: 'error',
          })
        }
      }
    }

    return conflicts
  }

  // Helper function to detect schedule overlaps
  const detectScheduleOverlap = (schedule1: Schedule[], schedule2: Schedule[]): boolean => {
    for (const slot1 of schedule1) {
      for (const slot2 of schedule2) {
        // Check if same day
        if (slot1.day_of_week === slot2.day_of_week) {
          // Convert times to minutes for easier comparison
          const start1 = timeToMinutes(slot1.start_time)
          const end1 = timeToMinutes(slot1.end_time)
          const start2 = timeToMinutes(slot2.start_time)
          const end2 = timeToMinutes(slot2.end_time)

          // Check for overlap
          if (start1 < end2 && start2 < end1) {
            return true
          }
        }
      }
    }
    return false
  }

  // Helper to convert HH:MM to minutes
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number)
    return hours * 60 + minutes
  }

  const addCourse = async (course: Course) => {
    // Check if course is already selected or registered
    if (
      selectedCourses.value.some((c) => c.id === course.id) ||
      registeredCourses.value.some((c) => c.id === course.id)
    ) {
      return { success: false, error: 'Course is already in your selection' }
    }

    // Check if course is full
    if (course.status === 'full') {
      return { success: false, error: 'Course is full' }
    }

    // Optimistic UI update
    selectedCourses.value.push(course)
    updateTotalCredits()

    // Check for conflicts locally first for immediate feedback
    const localConflicts = detectTimeConflicts(selectedCourses.value)
    conflicts.value = localConflicts

    // If there are conflicts, we still keep the course in the selection
    // but we'll show warnings to the user
    if (localConflicts.length > 0) {
      return {
        success: true,
        warning: 'Course added with potential conflicts. Please review before submitting.',
      }
    }

    return { success: true }
  }

  const removeCourse = (courseId: string) => {
    const courseIndex = selectedCourses.value.findIndex((c) => c.id === courseId)

    if (courseIndex !== -1) {
      selectedCourses.value.splice(courseIndex, 1)
      updateTotalCredits()

      // Re-check conflicts after removal
      const localConflicts = detectTimeConflicts(selectedCourses.value)
      conflicts.value = localConflicts

      return { success: true }
    }

    return { success: false, error: 'Course not found in selection' }
  }

  const submitRegistration = async () => {
    if (!currentSemesterId.value) {
      return { success: false, error: 'No semester selected' }
    }

    if (selectedCourses.value.length === 0) {
      return { success: false, error: 'No courses selected' }
    }

    // Final conflict check before submission
    const conflictCheck = await checkConflicts()
    if (!conflictCheck.success) {
      return conflictCheck
    }

    if (conflicts.value.length > 0) {
      return {
        success: false,
        error: 'Please resolve conflicts before submitting registration',
      }
    }

    registrationStatus.value = 'registering'

    try {
      const courseIds = selectedCourses.value.map((course) => course.id)

      const response = await api.courses.register(courseIds, currentSemesterId.value)

      if (response.success) {
        // Move successfully registered courses to registeredCourses
        const successfulIds = response.data?.successful_registrations || []
        const successfulCourses = selectedCourses.value.filter((c) => successfulIds.includes(c.id))

        registeredCourses.value = [...registeredCourses.value, ...successfulCourses]

        // Remove successful courses from selectedCourses
        selectedCourses.value = selectedCourses.value.filter((c) => !successfulIds.includes(c.id))

        // Handle partial failures
        if (response.data?.failed_registrations?.length) {
          const failedRegistrations = response.data.failed_registrations

          return {
            success: true,
            message: 'Registration partially successful',
            failedRegistrations,
          }
        }

        registrationStatus.value = 'success'
        updateTotalCredits()
        return { success: true, message: 'Registration successful' }
      } else {
        throw new Error(response.message || 'Registration failed')
      }
    } catch (err) {
      registrationStatus.value = 'error'
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Registration failed',
      }
    } finally {
      registrationStatus.value = 'idle'
    }
  }

  const clearSelection = () => {
    selectedCourses.value = []
    conflicts.value = []
    updateTotalCredits()
  }

  const resetStore = () => {
    availableCourses.value = []
    registeredCourses.value = []
    selectedCourses.value = []
    availableOfferings.value = []
    registeredOfferings.value = []
    selectedOfferings.value = []
    conflicts.value = []
    registrationStatus.value = 'idle'
    registrationPeriod.value = null
    error.value = null
    currentSemesterId.value = null
    totalCredits.value = 0
  }

  // New methods for course offerings (database schema aligned)
  const fetchAvailableOfferings = async (semesterId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await api.get<CourseRegistrationResponse>(
        `/api/student/course-offerings/available/${semesterId}`,
      )

      if (response.success && response.data) {
        availableOfferings.value = response.data.available_offerings
        registeredOfferings.value = response.data.registered_offerings
        registrationPeriod.value = response.data.registration_period
        currentSemesterId.value = semesterId
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch course offerings'
    } finally {
      isLoading.value = false
    }
  }

  const addCourseOffering = async (offering: CourseOffering) => {
    // Check if offering is already selected or registered
    if (
      selectedOfferings.value.some((o) => o.id === offering.id) ||
      registeredOfferings.value.some((r) => r.course_offering_id === offering.id)
    ) {
      return { success: false, error: 'Course offering is already in your selection' }
    }

    // Optimistic UI update
    selectedOfferings.value.push(offering)

    try {
      const response = await api.post('/api/student/course-registrations', {
        course_offering_id: offering.id,
        semester_id: offering.semester_id,
      })

      if (response.success) {
        return { success: true }
      } else {
        // Rollback on error
        selectedOfferings.value = selectedOfferings.value.filter((o) => o.id !== offering.id)
        throw new Error(response.message || 'Failed to add course offering')
      }
    } catch (err) {
      // Rollback on error
      selectedOfferings.value = selectedOfferings.value.filter((o) => o.id !== offering.id)
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to add course offering',
      }
    }
  }

  const removeCourseOffering = (offeringId: string) => {
    selectedOfferings.value = selectedOfferings.value.filter(
      (offering) => offering.id !== offeringId,
    )
    return { success: true }
  }

  return {
    // State (database schema aligned)
    availableOfferings,
    registeredOfferings,
    selectedOfferings,
    registrationPeriod,
    registrationStatus,
    conflicts,
    isLoading,
    error,
    currentSemesterId,
    totalCredits,
    maxCredits,

    // Legacy state for backward compatibility
    availableCourses,
    registeredCourses,
    selectedCourses,

    // Computed (database schema aligned)
    hasConflicts,
    isOverloaded,
    availableOfferingsFiltered,
    selectedOfferingsWithStatus,

    // Legacy computed for backward compatibility
    availableCoursesFiltered,
    selectedCoursesWithStatus,

    // Actions (database schema aligned)
    fetchAvailableOfferings,
    addCourseOffering,
    removeCourseOffering,

    // Legacy actions for backward compatibility
    fetchAvailableCourses,
    fetchRegisteredCourses,
    checkConflicts,
    addCourse,
    removeCourse,
    submitRegistration,
    clearSelection,
    resetStore,

    // Helper methods exposed for testing
    detectTimeConflicts,
    detectScheduleOverlap,
    timeToMinutes,
  }
})
