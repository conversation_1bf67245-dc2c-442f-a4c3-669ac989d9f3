<script setup lang="ts">
import { useAuthStore } from '@/shared/stores/auth'
import { Button } from '@/shared/components/ui/button'

const authStore = useAuthStore()

const handleLogout = async () => {
  try {
    await authStore.logout()
  } catch (error) {
    console.error('Logout error:', error)
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
    <div class="max-w-md w-full space-y-8 text-center">
      <div>
        <img alt="Vue logo" src="@/assets/logo.svg" width="125" height="125" class="mx-auto" />
        <h1 class="mt-6 text-3xl font-extrabold text-gray-900">
          Vue Auth Portal
        </h1>
        <p class="mt-2 text-sm text-gray-600">
          A secure Vue.js application with authentication
        </p>
      </div>

      <div v-if="authStore.isAuthenticated" class="space-y-4">
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <p>Welcome back, {{ authStore.user?.name }}!</p>
        </div>
        <div class="space-y-2">
          <router-link to="/dashboard"
            class="w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Go to Dashboard
          </router-link>
          <Button @click="handleLogout" class="w-full bg-gray-600 hover:bg-gray-700 text-white">
            Logout
          </Button>
        </div>
      </div>

      <div v-else class="space-y-4">
        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
          <p>You are not authenticated</p>
        </div>
        <router-link to="/login"
          class="w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          Sign In
        </router-link>
      </div>

      <div class="mt-8">
        <nav class="flex justify-center space-x-4">
          <router-link to="/about" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
            About
          </router-link>
        </nav>
      </div>
    </div>
  </div>
</template>
