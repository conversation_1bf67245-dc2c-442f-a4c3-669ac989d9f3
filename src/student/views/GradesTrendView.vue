<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useGradesStore } from '../stores/grades'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { GradesList, GPAHistory, AssessmentDetails } from '../components/grades'
import { BookOpen, TrendingUp, FileText, Download, Filter } from 'lucide-vue-next'

const gradesStore = useGradesStore()

// Local state
const selectedSemester = ref<string>('all')
const activeTab = ref('grades')

// Mock semester options (in real app, this would come from API)
const semesterOptions = ref([
  { id: 'all', name: 'All Semesters' },
  { id: 'current', name: 'Current Semester' },
  { id: 'spring2024', name: 'Spring 2024' },
  { id: 'fall2023', name: 'Fall 2023' },
  { id: 'summer2023', name: 'Summer 2023' },
])

// Computed properties
const filteredRecords = computed(() => {
  if (selectedSemester.value === 'all') {
    return gradesStore.academicRecords
  }
  return gradesStore.academicRecords.filter(
    (record) => record.semester_id === selectedSemester.value,
  )
})

const hasGrades = computed(() => {
  return gradesStore.academicRecords.length > 0
})

// Actions
const loadGrades = async () => {
  await gradesStore.fetchGrades()
}

const handleSemesterChange = (semesterId: string) => {
  selectedSemester.value = semesterId
  if (semesterId !== 'all') {
    gradesStore.fetchGradesBySemester(semesterId)
  }
}

const handleExport = () => {
  // Mock export functionality
  console.log('Exporting grades...')
}

// Lifecycle
onMounted(() => {
  loadGrades()
})
</script>

<template>
  <div class="container mx-auto py-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
      <div>
        <h1 class="text-3xl font-bold">Grades & Academic Progress</h1>
        <p class="text-muted-foreground">Track your academic performance and grade trends</p>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center gap-2">
        <!-- Semester Filter -->
        <Select v-model="selectedSemester" @update:model-value="handleSemesterChange">
          <SelectTrigger class="w-48">
            <Filter class="h-4 w-4 mr-2" />
            <SelectValue placeholder="Select semester" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="semester in semesterOptions" :key="semester.id" :value="semester.id">
              {{ semester.name }}
            </SelectItem>
          </SelectContent>
        </Select>

        <!-- Export Button -->
        <Button variant="outline" size="sm" @click="handleExport">
          <Download class="h-4 w-4 mr-2" />
          Export
        </Button>
      </div>
    </div>

    <!-- Main Content -->
    <Tabs v-model="activeTab" class="space-y-6">
      <TabsList class="grid w-full grid-cols-3">
        <TabsTrigger value="grades" class="flex items-center gap-2">
          <BookOpen class="h-4 w-4" />
          Grades
        </TabsTrigger>
        <TabsTrigger value="gpa" class="flex items-center gap-2">
          <TrendingUp class="h-4 w-4" />
          GPA History
        </TabsTrigger>
        <TabsTrigger value="assessments" class="flex items-center gap-2">
          <FileText class="h-4 w-4" />
          Assessments
        </TabsTrigger>
      </TabsList>

      <!-- Grades Tab -->
      <TabsContent value="grades" class="space-y-6">
        <GradesList
          :records="filteredRecords"
          :assessment-scores="gradesStore.assessmentScores"
          :semester-summaries="gradesStore.semesterSummaries"
          :selected-semester="selectedSemester"
          :is-loading="gradesStore.isLoading"
        />
      </TabsContent>

      <!-- GPA History Tab -->
      <TabsContent value="gpa" class="space-y-6">
        <GPAHistory
          :gpa-calculations="gradesStore.gpaCalculations"
          :semester-summaries="gradesStore.semesterSummaries"
          :is-loading="gradesStore.isLoading"
        />
      </TabsContent>

      <!-- Assessments Tab -->
      <TabsContent value="assessments" class="space-y-6">
        <AssessmentDetails
          :assessment-scores="gradesStore.assessmentScores"
          :upcoming-assessments="[]"
          :is-loading="gradesStore.isLoading"
        />
      </TabsContent>
    </Tabs>

    <!-- Error State -->
    <div v-if="gradesStore.error" class="text-center py-12">
      <div class="text-destructive mb-4">
        <BookOpen class="h-12 w-12 mx-auto mb-4" />
        <p>Failed to load grades</p>
        <p class="text-sm">{{ gradesStore.error }}</p>
      </div>
      <Button @click="loadGrades" variant="outline"> Try Again </Button>
    </div>
  </div>
</template>
