<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/shared/stores/auth'
import {
  SemesterCard,
  CreditProgress,
  GPADisplay,
  HoldsAlert,
  AlertNotifications,
} from '@/student/components/dashboard'
import type { <PERSON><PERSON><PERSON>, CreditProgress as CreditProgressType, GPAData } from '@/shared/types/models/student'
import type { Hold } from '@/shared/types/models/hold'
import type { Notification } from '@/shared/types/models/notification'

const authStore = useAuthStore()

// Reactive data
const isLoading = ref(true)
const currentSemester = ref<Semester | null>(null)
const creditProgress = ref<CreditProgressType | null>(null)
const gpaData = ref<GPAData | null>(null)
const holds = ref<Hold[]>([])
const notifications = ref<Notification[]>([])

// Mock data for demonstration - in a real app, this would come from API calls
const loadDashboardData = async () => {
  isLoading.value = true

  try {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Mock current semester data
    currentSemester.value = {
      id: '1',
      name: 'Spring 2024',
      code: 'SP24',
      start_date: '2024-02-01',
      end_date: '2024-05-31',
      academic_year: '2023-2024',
      is_current: true,
      registration_start: '2024-01-15',
      registration_end: '2024-02-15',
      academic_calendar_url: 'https://example.com/calendar',
    }

    // Mock credit progress data
    creditProgress.value = {
      earned_credits: 85,
      required_credits: 120,
      remaining_requirements: ['Capstone Project', 'Ethics Course', 'Elective'],
      completion_percentage: 71,
      credits_this_semester: 15,
      credits_in_progress: 15,
    }

    // Mock GPA data
    gpaData.value = {
      current_gpa: 3.45,
      semester_gpa: 3.6,
      cumulative_gpa: 3.42,
      trend: 'up',
      academic_standing: 'Good Standing',
      total_courses: 28,
      grade_distribution: {
        A: 8,
        B: 12,
        C: 6,
        D: 2,
        F: 0,
      },
    }

    // Mock holds data
    holds.value = [
      {
        id: '1',
        title: 'Outstanding Library Fees',
        description: 'You have overdue library materials that need to be returned or renewed.',
        type: 'financial',
        severity: 'warning',
        status: 'active',
        department: 'Library Services',
        due_date: '2024-03-15',
        amount_owed: 25.5,
        resolution_steps: ['Return overdue books', 'Pay outstanding fees online'],
        created_at: '2024-02-01T10:00:00Z',
        updated_at: '2024-02-01T10:00:00Z',
      },
    ]

    // Mock notifications data
    notifications.value = [
      {
        id: '1',
        title: 'Course Registration Opens Soon',
        message:
          'Registration for Fall 2024 semester opens on March 20th. Review your study plan and prepare your course selections.',
        type: 'academic_alert',
        category: 'registration',
        priority: 'high',
        action_text: 'View Course Catalog',
        action_url: '/courses',
        created_at: '2024-03-01T09:00:00Z',
        read: false,
      },
      {
        id: '2',
        title: 'Assignment Due Tomorrow',
        message:
          'Your Computer Science project is due tomorrow at 11:59 PM. Make sure to submit it on time.',
        type: 'academic_alert',
        category: 'deadline',
        priority: 'medium',
        action_text: 'Submit Assignment',
        action_url: '/assignments',
        created_at: '2024-03-10T14:30:00Z',
        read: false,
      },
    ]
  } catch (error) {
    console.error('Error loading dashboard data:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<template>
  <div class="container mx-auto py-6 space-y-6">
    <!-- Welcome Header -->
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">Welcome back, {{ authStore.user?.name || 'Student' }}!</h1>
      <p class="text-muted-foreground">
        Here's an overview of your academic progress and important updates.
      </p>
    </div>

    <!-- Alerts Section -->
    <div class="space-y-4">
      <HoldsAlert :holds="holds" :is-loading="isLoading" />
      <AlertNotifications :notifications="notifications" :is-loading="isLoading" />
    </div>

    <!-- Main Dashboard Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <!-- Semester Information -->
      <div class="lg:col-span-1">
        <SemesterCard :semester="currentSemester" :is-loading="isLoading" />
      </div>

      <!-- Credit Progress -->
      <div class="lg:col-span-1">
        <CreditProgress :credit-progress="creditProgress" :is-loading="isLoading" />
      </div>

      <!-- GPA Display -->
      <div class="lg:col-span-1 xl:col-span-1">
        <GPADisplay :gpa-data="gpaData" :is-loading="isLoading" />
      </div>
    </div>
  </div>
</template>
