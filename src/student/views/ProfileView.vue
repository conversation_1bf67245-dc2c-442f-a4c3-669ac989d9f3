<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { But<PERSON> } from '@/shared/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/shared/components/ui/tabs'
import { ProfileView, StudyPlan } from '../components/profile'
import { User, BookOpen, Settings } from 'lucide-vue-next'
import type {
  Student,
  CurriculumVersion,
  CurriculumUnit,
  AcademicRecord,
} from '../types/models/student'

// Local state
const student = ref<Student | null>(null)
const curriculumVersion = ref<CurriculumVersion | null>(null)
const curriculumUnits = ref<CurriculumUnit[]>([])
const academicRecords = ref<AcademicRecord[]>([])
const isLoading = ref(true)
const activeTab = ref('profile')

// Mock data loading
const loadProfileData = async () => {
  isLoading.value = true

  try {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Mock student data
    student.value = {
      id: 'student-1',
      student_id: 'S12345678',
      email: '<EMAIL>',
      phone: '+61 400 123 456',
      address: '123 Student Street, Melbourne VIC 3000',
      date_of_birth: '1995-06-15',
      enrollment_date: '2022-02-28',
      program_id: 'prog-1',
      specialization_id: 'spec-1',
      curriculum_version_id: 'cv-1',
      program: {
        id: 'prog-1',
        name: 'Bachelor of Computer Science',
        code: 'BCS',
        description: 'Comprehensive computer science program',
      },
      specialization: {
        id: 'spec-1',
        program_id: 'prog-1',
        name: 'Software Engineering',
        code: 'SE',
        program: {
          id: 'prog-1',
          name: 'Bachelor of Computer Science',
          code: 'BCS',
          description: 'Comprehensive computer science program',
        },
      },
      curriculum_version: {
        id: 'cv-1',
        program_id: 'prog-1',
        specialization_id: 'spec-1',
        semester_id: 'sem-1',
        program: {
          id: 'prog-1',
          name: 'Bachelor of Computer Science',
          code: 'BCS',
          description: 'Comprehensive computer science program',
        },
        specialization: {
          id: 'spec-1',
          program_id: 'prog-1',
          name: 'Software Engineering',
          code: 'SE',
          program: {
            id: 'prog-1',
            name: 'Bachelor of Computer Science',
            code: 'BCS',
            description: 'Comprehensive computer science program',
          },
        },
        semester: {
          id: 'sem-1',
          name: 'Spring 2024',
          code: 'SP24',
        },
      },
    }

    // Mock curriculum data (reuse from CurriculumView)
    const mockUnits = [
      { code: 'CS101', name: 'Introduction to Programming', credits: 3, semester: 'Semester 1' },
      { code: 'MATH101', name: 'Mathematics for Computing', credits: 3, semester: 'Semester 1' },
      { code: 'ENG101', name: 'Technical Communication', credits: 3, semester: 'Semester 1' },
      { code: 'CS102', name: 'Data Structures', credits: 3, semester: 'Semester 2' },
      { code: 'CS103', name: 'Computer Systems', credits: 3, semester: 'Semester 2' },
      { code: 'MATH201', name: 'Discrete Mathematics', credits: 3, semester: 'Semester 2' },
      { code: 'CS201', name: 'Algorithms', credits: 3, semester: 'Semester 3' },
      { code: 'CS202', name: 'Database Systems', credits: 3, semester: 'Semester 3' },
      { code: 'CS203', name: 'Software Engineering', credits: 3, semester: 'Semester 3' },
    ]

    curriculumUnits.value = mockUnits.map((unit, index) => ({
      id: `cu-${index + 1}`,
      curriculum_version_id: 'cv-1',
      unit_id: `unit-${index + 1}`,
      semester_id: `sem-${Math.floor(index / 3) + 1}`,
      curriculum_version: student.value!.curriculum_version,
      unit: {
        id: `unit-${index + 1}`,
        code: unit.code,
        name: unit.name,
        credit_points: unit.credits,
      },
      semester: {
        id: `sem-${Math.floor(index / 3) + 1}`,
        name: unit.semester,
        code: `S${Math.floor(index / 3) + 1}`,
      },
    }))

    // Mock academic records (completed units)
    academicRecords.value = curriculumUnits.value.slice(0, 6).map((unit) => ({
      id: `record-${unit.id}`,
      student_id: 'student-1',
      unit_id: unit.unit.id,
      semester_id: unit.semester_id,
      course_offering_id: `offering-${unit.id}`,
      final_grade: 'A',
      grade_points: 4.0,
      status: 'completed' as const,
      student: student.value!,
      unit: unit.unit,
      semester: unit.semester,
      course_offering: {
        id: `offering-${unit.id}`,
        curriculum_unit_id: unit.id,
        semester_id: unit.semester_id,
        lecture_id: 'lecturer-1',
        curriculum_unit: unit,
        semester: unit.semester,
        lecturer: {
          id: 'lecturer-1',
          employee_id: 'EMP001',
          email: '<EMAIL>',
          campus_id: 'campus-1',
          campus: {
            id: 'campus-1',
            name: 'Main Campus',
            code: 'MAIN',
            address: '123 University Ave',
            buildings: [],
          },
        },
        class_sessions: [],
      },
    }))
  } catch (error) {
    console.error('Failed to load profile data:', error)
  } finally {
    isLoading.value = false
  }
}

// Actions
const handleSaveProfile = async (data: Partial<Student>) => {
  console.log('Saving profile data:', data)
  // Mock save - in real app, this would call an API
  await new Promise((resolve) => setTimeout(resolve, 500))

  // Update local data
  if (student.value) {
    Object.assign(student.value, data)
  }
}

// Lifecycle
onMounted(() => {
  loadProfileData()
})
</script>

<template>
  <div class="container mx-auto py-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
      <div>
        <h1 class="text-3xl font-bold">My Profile</h1>
        <p class="text-muted-foreground">Manage your personal information and study plan</p>
      </div>
    </div>

    <!-- Main Content -->
    <Tabs v-model="activeTab" class="space-y-6">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="profile" class="flex items-center gap-2">
          <User class="h-4 w-4" />
          Personal Info
        </TabsTrigger>
        <TabsTrigger value="study-plan" class="flex items-center gap-2">
          <BookOpen class="h-4 w-4" />
          Study Plan
        </TabsTrigger>
      </TabsList>

      <!-- Profile Tab -->
      <TabsContent value="profile" class="space-y-6">
        <ProfileView :student="student" :is-loading="isLoading" :on-save="handleSaveProfile" />
      </TabsContent>

      <!-- Study Plan Tab -->
      <TabsContent value="study-plan" class="space-y-6">
        <StudyPlan
          :curriculum-version="student?.curriculum_version || null"
          :curriculum-units="curriculumUnits"
          :academic-records="academicRecords"
          :is-loading="isLoading"
        />
      </TabsContent>
    </Tabs>

    <!-- Error State -->
    <div v-if="!isLoading && !student" class="text-center py-12">
      <div class="text-destructive mb-4">
        <User class="h-12 w-12 mx-auto mb-4" />
        <p>Failed to load profile data</p>
        <p class="text-sm">Please try again later</p>
      </div>
      <Button @click="loadProfileData" variant="outline"> Try Again </Button>
    </div>
  </div>
</template>
