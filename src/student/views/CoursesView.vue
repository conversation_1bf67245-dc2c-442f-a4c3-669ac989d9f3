<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCourseStore } from '../stores/course'
import { Input } from '@/shared/components/ui/input'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import { CourseOfferingCard, RegistrationCart, ConflictDetection } from '../components/registration'
import { Search, Filter, Calendar } from 'lucide-vue-next'
import type { CourseOffering } from '../types/models/course'

const courseStore = useCourseStore()

// Local state
const searchQuery = ref('')
const selectedSemester = ref<string>('')
const selectedFilter = ref<string>('all')

// Mock semester options - in real app, this would come from API
const semesterOptions = ref([
  { id: 'current', name: 'Current Semester' },
  { id: 'next', name: 'Next Semester' },
  { id: 'summer', name: 'Summer Session' },
])

// Mock credit limits - in real app, this would come from API
const creditLimits = ref({
  minimum: 12,
  maximum: 18,
  overload_threshold: 15,
})

// Computed properties
const filteredOfferings = computed(() => {
  let offerings = courseStore.availableOfferingsFiltered

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    offerings = offerings.filter(
      (offering) =>
        offering.curriculum_unit.unit.code.toLowerCase().includes(query) ||
        offering.curriculum_unit.unit.name.toLowerCase().includes(query),
    )
  }

  // Apply category filter
  if (selectedFilter.value !== 'all') {
    // This would be implemented based on actual filtering requirements
    // For now, we'll just return all offerings
  }

  return offerings
})

const conflicts = computed(() => {
  // Mock conflict detection - in real app, this would use the store's conflict detection
  return []
})

// Actions
const handleAddOffering = (offering: CourseOffering) => {
  courseStore.addCourseOffering(offering)
}

const handleRemoveOffering = (offering: CourseOffering) => {
  courseStore.removeCourseOffering(offering.id)
}

const handleSubmitRegistration = async () => {
  // This would call the store's submit registration method
  console.log('Submitting registration...')
}

const handleClearCart = () => {
  courseStore.selectedOfferings = []
}

const loadCourseOfferings = async () => {
  const semesterId = selectedSemester.value || 'current'
  await courseStore.fetchAvailableOfferings(semesterId)
}

// Lifecycle
onMounted(() => {
  selectedSemester.value = 'current'
  loadCourseOfferings()
})
</script>

<template>
  <div class="container mx-auto py-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold">Course Registration</h1>
        <p class="text-muted-foreground">Browse and register for available course offerings</p>
      </div>
      <Badge v-if="courseStore.registrationPeriod?.is_open" variant="default">
        Registration Open
      </Badge>
      <Badge v-else variant="secondary"> Registration Closed </Badge>
    </div>

    <!-- Filters -->
    <div class="flex flex-col sm:flex-row gap-4 mb-6">
      <div class="relative flex-1">
        <Search
          class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
        />
        <Input
          v-model="searchQuery"
          placeholder="Search courses by code or name..."
          class="pl-10"
        />
      </div>

      <Select v-model="selectedSemester" @update:model-value="loadCourseOfferings">
        <SelectTrigger class="w-48">
          <Calendar class="h-4 w-4 mr-2" />
          <SelectValue placeholder="Select semester" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem v-for="semester in semesterOptions" :key="semester.id" :value="semester.id">
            {{ semester.name }}
          </SelectItem>
        </SelectContent>
      </Select>

      <Select v-model="selectedFilter">
        <SelectTrigger class="w-40">
          <Filter class="h-4 w-4 mr-2" />
          <SelectValue placeholder="Filter" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Courses</SelectItem>
          <SelectItem value="core">Core Courses</SelectItem>
          <SelectItem value="elective">Electives</SelectItem>
          <SelectItem value="available">Available</SelectItem>
        </SelectContent>
      </Select>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Course Offerings List -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Conflict Detection -->
        <ConflictDetection :conflicts="conflicts" />

        <!-- Loading State -->
        <div v-if="courseStore.isLoading" class="space-y-4">
          <div v-for="i in 3" :key="i" class="h-48 bg-muted animate-pulse rounded-lg" />
        </div>

        <!-- Course Offerings -->
        <div v-else-if="filteredOfferings.length > 0" class="space-y-4">
          <CourseOfferingCard
            v-for="offering in filteredOfferings"
            :key="offering.id"
            :offering="offering"
            :is-selected="courseStore.selectedOfferings.some((o) => o.id === offering.id)"
            :is-registered="
              courseStore.registeredOfferings.some((r) => r.course_offering_id === offering.id)
            "
            :is-registration-open="courseStore.registrationPeriod?.is_open ?? false"
            :on-add="handleAddOffering"
            :on-remove="handleRemoveOffering"
          />
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-12">
          <div class="text-muted-foreground mb-4">
            <Search class="h-12 w-12 mx-auto mb-4" />
            <p>No course offerings found</p>
            <p class="text-sm">Try adjusting your search or filters</p>
          </div>
        </div>
      </div>

      <!-- Registration Cart -->
      <div class="lg:col-span-1">
        <RegistrationCart
          :selected-offerings="courseStore.selectedOfferings"
          :conflicts="conflicts"
          :credit-limits="creditLimits"
          :is-submitting="courseStore.registrationStatus === 'registering'"
          :on-remove="handleRemoveOffering"
          :on-submit="handleSubmitRegistration"
          :on-clear="handleClearCart"
        />
      </div>
    </div>
  </div>
</template>
