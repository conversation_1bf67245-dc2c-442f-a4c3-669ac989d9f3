import type { RouteRecordRaw } from 'vue-router'

export const lecturerRoutes: RouteRecordRaw = {
  path: '/lecturer',
  component: () => import('@/lecturer/components/layout/LecturerLayout.vue'),
  meta: { requiresAuth: true, role: 'lecturer' },
  children: [
    {
      path: 'dashboard',
      name: 'lecturer-dashboard',
      component: () => import('@/lecturer/views/DashboardView.vue'),
    },
    {
      path: 'teaching',
      name: 'lecturer-teaching',
      children: [
        {
          path: 'courses',
          name: 'lecturer-courses',
          component: () => import('@/lecturer/views/CoursesView.vue'),
        },
        {
          path: 'timetable',
          name: 'lecturer-timetable',
          component: () => import('@/lecturer/views/TimetableView.vue'),
        },
      ],
    },
    {
      path: 'attendance',
      name: 'lecturer-attendance',
      component: () => import('@/lecturer/views/AttendanceView.vue'),
    },
    // Commented routes for future implementation
    // {
    //   path: 'students',
    //   name: 'lecturer-students',
    //   component: () => import('@/lecturer/views/StudentsView.vue'),
    // },
    // {
    //   path: 'feedback',
    //   name: 'lecturer-feedback',
    //   component: () => import('@/lecturer/views/FeedbackView.vue'),
    // },
    // {
    //   path: 'notifications',
    //   name: 'lecturer-notifications',
    //   component: () => import('@/lecturer/views/NotificationsView.vue'),
    // },
    // {
    //   path: 'profile',
    //   name: 'lecturer-profile',
    //   component: () => import('@/lecturer/views/ProfileView.vue'),
    // },
    // {
    //   path: 'settings',
    //   name: 'lecturer-settings',
    //   component: () => import('@/lecturer/views/SettingsView.vue'),
    // },
  ],
}