import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/shared/stores/auth'
import { studentRoutes } from './student.routes'
import { lecturerRoutes } from './lecturer.routes'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // Shared Routes
    {
      path: '/login',
      name: 'login',
      component: () => import('@/shared/views/LoginView.vue'),
      meta: { requiresGuest: true },
    },

    // Role-based Routes
    studentRoutes,
    lecturerRoutes,

    // Root redirect based on role
    {
      path: '/',
      redirect: () => {
        const authStore = useAuthStore()
        if (authStore.isStudent) return '/student/dashboard'
        if (authStore.isLecturer) return '/lecturer/dashboard'
        return '/login'
      },
    },

    // Legacy route redirects for backward compatibility
    {
      path: '/dashboard',
      redirect: () => {
        const authStore = useAuthStore()
        return authStore.isStudent ? '/student/dashboard' : '/lecturer/dashboard'
      },
    },

    // Catch-all 404 route
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/shared/views/NotFoundView.vue'),
    },
  ],
})

// Enhanced navigation guard with role validation
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // Redirect to login with the intended route as query parameter
    next({
      name: 'login',
      query: { redirect: to.fullPath },
    })
    return
  }

  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // Redirect authenticated users to appropriate dashboard based on role
    const redirectPath = authStore.isStudent ? '/student/dashboard' : '/lecturer/dashboard'
    next(redirectPath)
    return
  }

  // Check role authorization
  if (to.meta.role && authStore.role !== to.meta.role) {
    // Redirect to appropriate dashboard based on user role
    const redirectPath = authStore.isStudent ? '/student/dashboard' : '/lecturer/dashboard'
    next(redirectPath)
    return
  }

  next()
})

export default router
