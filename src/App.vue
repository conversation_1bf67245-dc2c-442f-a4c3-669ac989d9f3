<script setup lang="ts">
import { RouterView } from 'vue-router';
import { onMounted } from 'vue';
import { useAuthStore } from '@/shared/stores/auth';

const authStore = useAuthStore();

// Initialize auth state
onMounted(async () => {
  authStore.initializeAuth();

  // If user is authenticated, fetch fresh user data
  if (authStore.isAuthenticated) {
    try {
      await authStore.getMe();
    } catch (error) {
      console.error('Failed to fetch user data on app start:', error);
    }
  }
});
</script>

<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
}
</style>
