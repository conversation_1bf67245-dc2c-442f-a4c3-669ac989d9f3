<script setup lang="ts">
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/shared/components/ui/collapsible'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from '@/shared/components/ui/sidebar'
import {
  ChevronRight,
  GraduationCap,
  LayoutDashboard,
  BookOpen,
  Calendar,
  Users,
  UserCheck,
  MessageSquare,
  Bell,
  User,
  Settings,
} from 'lucide-vue-next'
import { useRoute, useRouter } from 'vue-router'
import { computed, onMounted, onUnmounted } from 'vue'

const route = useRoute()

// Define navigation items for lecturer portal
const navMain = [
  {
    title: 'Dashboard',
    url: '/lecturer/dashboard',
    icon: LayoutDashboard,
    shortcut: '1',
  },
  {
    title: 'Teaching',
    url: '/lecturer/teaching',
    icon: BookOpen,
    shortcut: '2',
    items: [
      {
        title: 'My Courses',
        url: '/lecturer/teaching/courses',
      },
      {
        title: 'Timetable',
        url: '/lecturer/teaching/timetable',
      },
    ],
  },
  {
    title: 'Attendance',
    url: '/lecturer/attendance',
    icon: UserCheck,
    shortcut: '3',
  },
  {
    title: 'Student Management',
    url: '/lecturer/students',
    icon: Users,
    shortcut: '4',
  },
  {
    title: 'Feedback',
    url: '/lecturer/feedback',
    icon: MessageSquare,
    shortcut: '5',
  },
  {
    title: 'Notifications',
    url: '/lecturer/notifications',
    icon: Bell,
    shortcut: '6',
  },
  {
    title: 'Profile',
    url: '/lecturer/profile',
    icon: User,
    shortcut: '7',
  },
  {
    title: 'Settings',
    url: '/lecturer/settings',
    icon: Settings,
    shortcut: '8',
  },
]

// Computed property to check if a route is active
const isRouteActive = computed(() => (url: string) => {
  return route.path === url || route.path.startsWith(url + '/')
})

// Computed property to check if a parent item should be open (has active children)
const shouldBeOpen = computed(() => (item: (typeof navMain)[0]) => {
  return (
    item.items?.some((subItem) => isRouteActive.value(subItem.url)) || isRouteActive.value(item.url)
  )
})

// Initialize router for navigation
const router = useRouter()

// Keyboard navigation support
const handleKeyboardNavigation = (event: KeyboardEvent) => {
  const key = event.key

  // Handle number keys 1-8 for menu navigation
  if (key >= '1' && key <= '8') {
    event.preventDefault()
    const index = parseInt(key) - 1
    const targetItem = navMain[index]

    if (targetItem) {
      // Navigate to the main URL of the item
      router.push(targetItem.url)

      // Announce to screen readers
      announceToScreenReader(`Navigating to ${targetItem.title}`)
    }
  }
}

// Screen reader announcements
const announceToScreenReader = (message: string) => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message

  document.body.appendChild(announcement)

  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

// Add keyboard event listeners
onMounted(() => {
  document.addEventListener('keydown', handleKeyboardNavigation)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardNavigation)
})

defineProps<{
  [key: string]: unknown
}>()
</script>

<template>
  <Sidebar v-bind="$props" role="navigation" aria-label="Lecturer navigation">
    <SidebarHeader>
      <!-- Logo and App Name -->
      <div class="flex items-center gap-2">
        <div
          class="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground"
        >
          <GraduationCap class="size-4" />
        </div>
        <span class="font-semibold text-lg">Lecturer Portal</span>
      </div>
    </SidebarHeader>
    <SidebarContent class="gap-0">
      <SidebarGroup>
        <SidebarGroupLabel>Overview</SidebarGroupLabel>
        <!-- Main Navigation Menu -->
        <SidebarMenu role="menubar">
          <template v-for="(item, index) in navMain.slice(0, 1)" :key="item.title">
            <SidebarMenuItem role="none">
              <SidebarMenuButton
                as-child
                :tooltip="`${item.title} (Press ${item.shortcut})`"
                :class="{
                  'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                    isRouteActive(item.url),
                  'hover:bg-accent hover:text-accent-foreground': !isRouteActive(item.url),
                }"
                role="menuitem"
                :aria-current="isRouteActive(item.url) ? 'page' : undefined"
                :tabindex="index === 0 ? 0 : -1"
              >
                <RouterLink :to="item.url" class="flex items-center gap-2 w-full">
                  <component :is="item.icon" class="h-4 w-4" />
                  <span>{{ item.title }}</span>
                  <span class="ml-auto text-xs text-muted-foreground">{{ item.shortcut }}</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </template>
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>Teaching</SidebarGroupLabel>
        <SidebarMenu role="menubar">
          <template v-for="(item, index) in navMain.slice(1, 3)" :key="item.title">
            <!-- Items without sub-items -->
            <SidebarMenuItem v-if="!item.items" role="none">
              <SidebarMenuButton
                as-child
                :tooltip="`${item.title} (Press ${item.shortcut})`"
                :class="{
                  'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                    isRouteActive(item.url),
                  'hover:bg-accent hover:text-accent-foreground': !isRouteActive(item.url),
                }"
                role="menuitem"
                :aria-current="isRouteActive(item.url) ? 'page' : undefined"
              >
                <RouterLink :to="item.url" class="flex items-center gap-2 w-full">
                  <component :is="item.icon" class="h-4 w-4" />
                  <span>{{ item.title }}</span>
                  <span class="ml-auto text-xs text-muted-foreground">{{ item.shortcut }}</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- Items with sub-items (collapsible) -->
            <Collapsible
              v-else
              as-child
              :default-open="shouldBeOpen(item)"
              class="group/collapsible"
            >
              <SidebarMenuItem role="none">
                <CollapsibleTrigger as-child>
                  <SidebarMenuButton
                    :tooltip="`${item.title} (Press ${item.shortcut})`"
                    :class="{
                      'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                        isRouteActive(item.url),
                      'hover:bg-accent hover:text-accent-foreground': !isRouteActive(item.url),
                    }"
                    role="menuitem"
                    :aria-current="isRouteActive(item.url) ? 'page' : undefined"
                    :aria-expanded="shouldBeOpen(item)"
                  >
                    <component :is="item.icon" class="h-4 w-4" />
                    <span>{{ item.title }}</span>
                    <span class="ml-auto text-xs text-muted-foreground mr-2">{{
                      item.shortcut
                    }}</span>
                    <ChevronRight
                      class="transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                    />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub role="menu">
                    <template v-for="subItem in item.items" :key="subItem.title">
                      <SidebarMenuSubItem role="none">
                        <SidebarMenuSubButton
                          as-child
                          :class="{
                            'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                              isRouteActive(subItem.url),
                            'hover:bg-accent hover:text-accent-foreground': !isRouteActive(
                              subItem.url,
                            ),
                          }"
                          role="menuitem"
                          :aria-current="isRouteActive(subItem.url) ? 'page' : undefined"
                        >
                          <RouterLink :to="subItem.url">
                            <span>{{ subItem.title }}</span>
                          </RouterLink>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    </template>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          </template>
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>Student Management</SidebarGroupLabel>
        <SidebarMenu role="menubar">
          <template v-for="(item, index) in navMain.slice(3, 6)" :key="item.title">
            <SidebarMenuItem role="none">
              <SidebarMenuButton
                as-child
                :tooltip="`${item.title} (Press ${item.shortcut})`"
                :class="{
                  'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                    isRouteActive(item.url),
                  'hover:bg-accent hover:text-accent-foreground': !isRouteActive(item.url),
                }"
                role="menuitem"
                :aria-current="isRouteActive(item.url) ? 'page' : undefined"
              >
                <RouterLink :to="item.url" class="flex items-center gap-2 w-full">
                  <component :is="item.icon" class="h-4 w-4" />
                  <span>{{ item.title }}</span>
                  <span class="ml-auto text-xs text-muted-foreground">{{ item.shortcut }}</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </template>
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>Account</SidebarGroupLabel>
        <SidebarMenu role="menubar">
          <template v-for="(item, index) in navMain.slice(6)" :key="item.title">
            <SidebarMenuItem role="none">
              <SidebarMenuButton
                as-child
                :tooltip="`${item.title} (Press ${item.shortcut})`"
                :class="{
                  'bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white':
                    isRouteActive(item.url),
                  'hover:bg-accent hover:text-accent-foreground': !isRouteActive(item.url),
                }"
                role="menuitem"
                :aria-current="isRouteActive(item.url) ? 'page' : undefined"
              >
                <RouterLink :to="item.url" class="flex items-center gap-2 w-full">
                  <component :is="item.icon" class="h-4 w-4" />
                  <span>{{ item.title }}</span>
                  <span class="ml-auto text-xs text-muted-foreground">{{ item.shortcut }}</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </template>
        </SidebarMenu>
      </SidebarGroup>
    </SidebarContent>
    <SidebarRail />
  </Sidebar>
</template>
