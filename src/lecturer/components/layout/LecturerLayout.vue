<script setup lang="ts">
import LecturerSidebar from '@/lecturer/components/layout/LecturerSidebar.vue'
import Lecturer<PERSON><PERSON><PERSON><PERSON><PERSON> from '@/lecturer/components/layout/LecturerMobileNav.vue'
import NotificationPopover from '@/shared/components/SharedNotificationPopover.vue'
import { Separator } from '@/shared/components/ui/separator'
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/shared/components/ui/sidebar'
import { Button } from '@/shared/components/ui/button'
import UserAvatar from '@/shared/components/SharedUserAvatar.vue'
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import { 
  Users, 
  ClipboardCheck, 
  MessageSquare, 
  Calendar,
  BookOpen
} from 'lucide-vue-next'

const route = useRoute()

// Compute page title based on current route
const pageTitle = computed(() => {
  const routeName = route.name as string
  if (routeName?.includes('dashboard')) return 'Dashboard'
  if (routeName?.includes('courses')) return 'My Courses'
  if (routeName?.includes('timetable')) return 'Timetable'
  if (routeName?.includes('attendance')) return 'Attendance'
  if (routeName?.includes('students')) return 'Student Management'
  if (routeName?.includes('feedback')) return 'Feedback'
  if (routeName?.includes('notifications')) return 'Notifications'
  if (routeName?.includes('profile')) return 'Profile'
  if (routeName?.includes('settings')) return 'Settings'
  return 'Lecturer Portal'
})

// Quick access actions for lecturers
const quickActions = [
  {
    label: 'Mark Attendance',
    icon: ClipboardCheck,
    action: () => navigateToAttendance(),
    variant: 'default' as const
  },
  {
    label: 'View Students',
    icon: Users,
    action: () => navigateToStudents(),
    variant: 'outline' as const
  },
  {
    label: 'Send Message',
    icon: MessageSquare,
    action: () => navigateToNotifications(),
    variant: 'outline' as const
  }
]

// Navigation functions
const navigateToAttendance = () => {
  // Navigate to attendance marking
}

const navigateToStudents = () => {
  // Navigate to student management
}

const navigateToNotifications = () => {
  // Navigate to notifications/messaging
}
</script>

<template>
  <SidebarProvider>
    <LecturerSidebar />
    <SidebarInset>
      <header
        class="flex sticky top-0 bg-background h-16 shrink-0 items-center gap-2 border-b px-4 z-40"
      >
        <SidebarTrigger class="-ml-1" aria-label="Toggle sidebar" />
        <Separator orientation="vertical" class="mr-2 h-4" />

        <!-- Lecturer Portal Title and Page Context -->
        <div class="flex items-center gap-4">
          <h1 class="text-lg font-semibold">{{ pageTitle }}</h1>
          <div class="hidden md:flex items-center gap-2 text-sm text-muted-foreground">
            <span>•</span>
            <span>Lecturer Portal</span>
          </div>
        </div>

        <!-- Spacer -->
        <div class="flex-1" />

        <!-- Quick Access Tools - Hidden on mobile -->
        <div class="hidden lg:flex items-center gap-2">
          <Button
            v-for="action in quickActions"
            :key="action.label"
            :variant="action.variant"
            size="sm"
            @click="action.action"
            class="gap-2"
          >
            <component :is="action.icon" class="h-4 w-4" />
            {{ action.label }}
          </Button>
        </div>

        <!-- Right side - Notifications and Avatar -->
        <div class="flex items-center gap-3">
          <NotificationPopover />
          <UserAvatar />
        </div>
      </header>

      <!-- Main content area with proper spacing for mobile nav -->
      <div class="flex flex-1 flex-col gap-4 p-4 pb-20 md:pb-4">
        <RouterView />
      </div>
    </SidebarInset>

    <!-- Mobile Navigation - Only visible on mobile -->
    <LecturerMobileNav />
  </SidebarProvider>
</template>
