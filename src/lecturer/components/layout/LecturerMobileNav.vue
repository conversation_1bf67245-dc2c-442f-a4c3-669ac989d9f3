<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  LayoutDashboard, 
  BookOpen, 
  UserCheck, 
  Users, 
  Bell 
} from 'lucide-vue-next'
import { Button } from '@/shared/components/ui/button'

const route = useRoute()
const router = useRouter()

// Define bottom navigation items for mobile (key lecturer functions only)
const mobileNavItems = [
  {
    title: 'Dashboard',
    url: '/lecturer/dashboard',
    icon: LayoutDashboard,
    shortcut: '1',
  },
  {
    title: 'Courses',
    url: '/lecturer/teaching/courses',
    icon: BookOpen,
    shortcut: '2',
  },
  {
    title: 'Attendance',
    url: '/lecturer/attendance',
    icon: UserCheck,
    shortcut: '3',
  },
  {
    title: 'Students',
    url: '/lecturer/students',
    icon: Users,
    shortcut: '4',
  },
  {
    title: 'Notifications',
    url: '/lecturer/notifications',
    icon: Bell,
    shortcut: 'N',
  },
]

// Computed property to check if a route is active
const isRouteActive = computed(() => (url: string) => {
  return route.path === url || route.path.startsWith(url + '/')
})

// Handle navigation
const navigateTo = (url: string, title: string) => {
  router.push(url)
  
  // Announce to screen readers
  announceToScreenReader(`Navigating to ${title}`)
}

// Screen reader announcements
const announceToScreenReader = (message: string) => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message

  document.body.appendChild(announcement)

  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}
</script>

<template>
  <!-- Mobile Bottom Navigation - Only visible on mobile -->
  <nav
    class="fixed bottom-0 left-0 right-0 z-50 bg-background border-t border-border md:hidden"
    role="navigation"
    aria-label="Mobile lecturer navigation"
  >
    <div class="flex items-center justify-around px-2 py-2">
      <Button
        v-for="item in mobileNavItems"
        :key="item.title"
        variant="ghost"
        size="sm"
        class="flex flex-col items-center gap-1 h-auto py-2 px-3 min-w-0 flex-1"
        :class="{
          'text-primary bg-primary/10': isRouteActive(item.url),
          'text-muted-foreground hover:text-foreground': !isRouteActive(item.url),
        }"
        @click="navigateTo(item.url, item.title)"
        :aria-current="isRouteActive(item.url) ? 'page' : undefined"
        :aria-label="`${item.title} (Press ${item.shortcut})`"
      >
        <component 
          :is="item.icon" 
          class="h-5 w-5 shrink-0"
          :class="{
            'text-primary': isRouteActive(item.url),
            'text-muted-foreground': !isRouteActive(item.url),
          }"
        />
        <span 
          class="text-xs font-medium truncate max-w-full"
          :class="{
            'text-primary': isRouteActive(item.url),
            'text-muted-foreground': !isRouteActive(item.url),
          }"
        >
          {{ item.title }}
        </span>
      </Button>
    </div>
  </nav>
</template>

<style scoped>
/* Ensure mobile nav doesn't interfere with desktop layout */
@media (min-width: 768px) {
  nav {
    display: none;
  }
}

/* Add safe area padding for devices with home indicators */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  nav {
    padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
  }
}
</style>
