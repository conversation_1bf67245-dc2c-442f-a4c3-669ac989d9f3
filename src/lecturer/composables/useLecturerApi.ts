import { useBase<PERSON><PERSON> } from '@/shared/composables/useBaseApi'
import { useAuthStore } from '@/shared/stores/auth'

export function useLecturerApi() {
  const authStore = useAuthStore()
  const baseApi = useBaseApi()

  // Create lecturer-specific API instance with base URL prefix
  const lecturerApi = {
    ...baseApi,

    // Lecturer Dashboard APIs
    dashboard: {
      getData: () => baseApi.get(`/api/lecturer/dashboard`),
      getTeachingSummary: () => baseApi.get(`/api/lecturer/dashboard/teaching-summary`),
      getStudentAlerts: () => baseApi.get(`/api/lecturer/dashboard/student-alerts`),
      getRecentActivities: () => baseApi.get(`/api/lecturer/dashboard/activities`),
    },

    // Lecturer Course Management APIs
    courses: {
      getOfferings: () => baseApi.get(`/api/lecturer/courses`),
      getById: (courseId: string) => baseApi.get(`/api/lecturer/courses/${courseId}`),
      updateCourse: (courseId: string, data: any) => baseApi.put(`/api/lecturer/courses/${courseId}`, data),
      uploadMaterial: (courseId: string, file: FormData) => baseApi.post(`/api/lecturer/courses/${courseId}/materials`, file),
    },

    // Lecturer Attendance Management APIs
    attendance: {
      getSessions: () => baseApi.get(`/api/lecturer/attendance/sessions`),
      getSessionById: (sessionId: string) => baseApi.get(`/api/lecturer/attendance/sessions/${sessionId}`),
      markAttendance: (sessionId: string, attendanceData: any) => baseApi.post(`/api/lecturer/attendance/sessions/${sessionId}/mark`, attendanceData),
      updateAttendance: (sessionId: string, studentId: string, status: string) => baseApi.put(`/api/lecturer/attendance/sessions/${sessionId}/students/${studentId}`, { status }),
    },

    // Lecturer Student Management APIs
    students: {
      getByCourse: (courseId: string) => baseApi.get(`/api/lecturer/students/course/${courseId}`),
      getAlerts: () => baseApi.get(`/api/lecturer/students/alerts`),
      dismissAlert: (alertId: string) => baseApi.delete(`/api/lecturer/students/alerts/${alertId}`),
      addNote: (studentId: string, note: string) => baseApi.post(`/api/lecturer/students/${studentId}/notes`, { note }),
    },

    // Lecturer Timetable APIs
    timetable: {
      get: () => baseApi.get(`/api/lecturer/timetable`),
      getByWeek: (weekStart: string) => baseApi.get(`/api/lecturer/timetable/week/${weekStart}`),
      createSession: (sessionData: any) => baseApi.post(`/api/lecturer/timetable/sessions`, sessionData),
      updateSession: (sessionId: string, sessionData: any) => baseApi.put(`/api/lecturer/timetable/sessions/${sessionId}`, sessionData),
    },
  }

  return lecturerApi
}