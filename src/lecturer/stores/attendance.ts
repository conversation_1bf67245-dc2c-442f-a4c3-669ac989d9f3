import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useApi } from '@/shared/composables/useBaseApi'
import type { 
  AttendanceSession,
  AttendanceRecord,
  AttendanceAnalytics,
  BulkAttendanceUpdate,
  QuickAttendanceSession,
  AttendanceStatus
} from '@/shared/types/models/attendance'
import type { ClassSession } from '@/shared/types/models/lecturer'

export const useLecturerAttendanceStore = defineStore('lecturerAttendance', () => {
  const api = useBaseApi()

  // State
  const attendanceSessions = ref<AttendanceSession[]>([])
  const selectedSession = ref<QuickAttendanceSession | null>(null)
  const attendanceRecords = ref<AttendanceRecord[]>([])
  const attendanceAnalytics = ref<AttendanceAnalytics | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const bulkUpdateMode = ref(false)
  const selectedStudents = ref<Set<number>>(new Set())

  // Computed properties
  const sessionsRequiringAttendance = computed(() => 
    attendanceSessions.value.filter(session => 
      !session.marked_at && session.class_session.attendance_required
    )
  )

  const completedSessions = computed(() => 
    attendanceSessions.value.filter(session => session.marked_at)
  )

  const todaysSessions = computed(() => {
    const today = new Date().toISOString().split('T')[0]
    return attendanceSessions.value.filter(session => 
      session.session_date === today
    )
  })

  const averageAttendanceRate = computed(() => {
    if (completedSessions.value.length === 0) return 0
    const totalRate = completedSessions.value.reduce((sum, session) => 
      sum + session.attendance_percentage, 0
    )
    return totalRate / completedSessions.value.length
  })

  const studentsWithPoorAttendance = computed(() => {
    if (!attendanceAnalytics.value) return []
    return attendanceAnalytics.value.student_breakdown.filter(student => 
      student.attendance_rate < 75 // Less than 75% attendance
    )
  })

  // Actions
  const fetchAttendanceSessions = async (courseId?: string) => {
    loading.value = true
    error.value = null

    try {
      const response = await api.lecturer.attendance.getSessions(courseId)
      
      if (response.success) {
        attendanceSessions.value = response.data
      } else {
        throw new Error(response.message || 'Failed to load attendance sessions')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load attendance sessions'
      console.error('Attendance sessions fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchSessionAttendance = async (sessionId: string) => {
    loading.value = true
    error.value = null

    try {
      const response = await api.lecturer.attendance.getSessionAttendance(sessionId)
      
      if (response.success) {
        selectedSession.value = response.data
        attendanceRecords.value = response.data.current_attendance || []
      } else {
        throw new Error(response.message || 'Failed to load session attendance')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load session attendance'
      console.error('Session attendance fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const markAttendance = async (sessionId: string, attendanceData: AttendanceRecord[]) => {
    try {
      const response = await api.lecturer.attendance.markAttendance(sessionId, {
        attendance_records: attendanceData
      })
      
      if (response.success) {
        // Update local state
        const sessionIndex = attendanceSessions.value.findIndex(
          session => session.id.toString() === sessionId
        )
        
        if (sessionIndex !== -1) {
          attendanceSessions.value[sessionIndex].marked_at = new Date().toISOString()
          attendanceSessions.value[sessionIndex].attendance_records = attendanceData
          attendanceSessions.value[sessionIndex].present_count = 
            attendanceData.filter(record => record.status === 'present').length
          attendanceSessions.value[sessionIndex].absent_count = 
            attendanceData.filter(record => record.status === 'absent').length
          attendanceSessions.value[sessionIndex].late_count = 
            attendanceData.filter(record => record.status === 'late').length
          attendanceSessions.value[sessionIndex].attendance_percentage = 
            (attendanceSessions.value[sessionIndex].present_count / attendanceData.length) * 100
        }
        
        if (selectedSession.value) {
          selectedSession.value.is_marked = true
          selectedSession.value.marked_at = new Date().toISOString()
          selectedSession.value.current_attendance = attendanceData
        }
        
        return response.data
      } else {
        throw new Error(response.message || 'Failed to mark attendance')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to mark attendance'
      console.error('Mark attendance error:', err)
      throw err
    }
  }

  const bulkUpdateAttendance = async (sessionId: string, updates: BulkAttendanceUpdate) => {
    try {
      const response = await api.lecturer.attendance.bulkUpdateAttendance(sessionId, updates)
      
      if (response.success) {
        // Update local state
        if (selectedSession.value) {
          updates.updates.forEach(update => {
            const recordIndex = selectedSession.value!.current_attendance.findIndex(
              record => record.student_id === update.student_id
            )
            
            if (recordIndex !== -1) {
              selectedSession.value!.current_attendance[recordIndex] = {
                ...selectedSession.value!.current_attendance[recordIndex],
                ...update
              }
            }
          })
        }
        
        return response.data
      } else {
        throw new Error(response.message || 'Failed to update attendance')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update attendance'
      console.error('Bulk update attendance error:', err)
      throw err
    }
  }

  const fetchAttendanceAnalytics = async (courseId: string, period?: string) => {
    try {
      const response = await api.lecturer.attendance.getAnalytics(courseId, period)
      
      if (response.success) {
        attendanceAnalytics.value = response.data
        return response.data
      } else {
        throw new Error(response.message || 'Failed to load attendance analytics')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load attendance analytics'
      console.error('Attendance analytics fetch error:', err)
      throw err
    }
  }

  const exportAttendanceReport = async (courseId: string, format: string = 'pdf') => {
    try {
      const response = await api.lecturer.attendance.exportReport(courseId, format)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || 'Failed to export attendance report')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to export attendance report'
      console.error('Export attendance report error:', err)
      throw err
    }
  }

  // Bulk operations
  const toggleBulkMode = () => {
    bulkUpdateMode.value = !bulkUpdateMode.value
    if (!bulkUpdateMode.value) {
      selectedStudents.value.clear()
    }
  }

  const selectStudent = (studentId: number) => {
    if (selectedStudents.value.has(studentId)) {
      selectedStudents.value.delete(studentId)
    } else {
      selectedStudents.value.add(studentId)
    }
  }

  const selectAllStudents = () => {
    if (!selectedSession.value) return
    
    selectedSession.value.expected_students.forEach(student => {
      selectedStudents.value.add(student.student_id)
    })
  }

  const clearSelectedStudents = () => {
    selectedStudents.value.clear()
  }

  const bulkMarkStatus = async (status: AttendanceStatus) => {
    if (!selectedSession.value || selectedStudents.value.size === 0) return

    const updates = Array.from(selectedStudents.value).map(studentId => ({
      student_id: studentId,
      status,
      check_in_time: status === 'present' ? new Date().toISOString() : undefined,
      minutes_late: status === 'late' ? 5 : 0 // Default 5 minutes late
    }))

    await bulkUpdateAttendance(selectedSession.value.class_session_id.toString(), {
      class_session_id: selectedSession.value.class_session_id,
      updates
    })

    clearSelectedStudents()
  }

  // Utility functions
  const getSessionsByDate = (date: string) => {
    return attendanceSessions.value.filter(session => session.session_date === date)
  }

  const getSessionsByCourse = (courseCode: string) => {
    return attendanceSessions.value.filter(session => session.course_code === courseCode)
  }

  const calculateAttendanceRate = (presentCount: number, totalCount: number) => {
    if (totalCount === 0) return 0
    return Math.round((presentCount / totalCount) * 100)
  }

  const getAttendanceStatusColor = (status: AttendanceStatus) => {
    const colors = {
      present: 'text-green-600',
      absent: 'text-red-600',
      late: 'text-yellow-600',
      excused: 'text-blue-600',
      partial: 'text-orange-600'
    }
    return colors[status] || 'text-gray-600'
  }

  return {
    // State
    attendanceSessions,
    selectedSession,
    attendanceRecords,
    attendanceAnalytics,
    loading,
    error,
    bulkUpdateMode,
    selectedStudents,

    // Computed properties
    sessionsRequiringAttendance,
    completedSessions,
    todaysSessions,
    averageAttendanceRate,
    studentsWithPoorAttendance,

    // Actions
    fetchAttendanceSessions,
    fetchSessionAttendance,
    markAttendance,
    bulkUpdateAttendance,
    fetchAttendanceAnalytics,
    exportAttendanceReport,

    // Bulk operations
    toggleBulkMode,
    selectStudent,
    selectAllStudents,
    clearSelectedStudents,
    bulkMarkStatus,

    // Utility functions
    getSessionsByDate,
    getSessionsByCourse,
    calculateAttendanceRate,
    getAttendanceStatusColor,
  }
})
