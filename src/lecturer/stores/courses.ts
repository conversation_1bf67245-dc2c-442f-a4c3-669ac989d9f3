import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useApi } from '@/shared/composables/useBaseApi'
import type { 
  CourseOffering, 
  CourseMaterial, 
  CourseStatistics,
  EnrolledStudent 
} from '@/shared/types/models/lecturer'

export const useLecturerCoursesStore = defineStore('lecturerCourses', () => {
  const api = useBaseApi()

  // State
  const courses = ref<CourseOffering[]>([])
  const selectedCourse = ref<CourseOffering | null>(null)
  const courseStatistics = ref<CourseStatistics | null>(null)
  const enrolledStudents = ref<EnrolledStudent[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // Computed properties
  const activeCourses = computed(() => 
    courses.value.filter(course => course.semester.is_current)
  )

  const coursesByDeliveryMode = computed(() => {
    const grouped: Record<string, CourseOffering[]> = {}
    courses.value.forEach(course => {
      if (!grouped[course.delivery_mode]) {
        grouped[course.delivery_mode] = []
      }
      grouped[course.delivery_mode].push(course)
    })
    return grouped
  })

  const totalEnrollment = computed(() => 
    courses.value.reduce((total, course) => total + course.current_enrollment, 0)
  )

  const averageEnrollmentRate = computed(() => {
    if (courses.value.length === 0) return 0
    const totalRate = courses.value.reduce((sum, course) => 
      sum + (course.current_enrollment / course.max_capacity), 0
    )
    return (totalRate / courses.value.length) * 100
  })

  const upcomingSessions = computed(() => {
    const now = new Date()
    const sessions = courses.value.flatMap(course => 
      course.class_sessions.filter(session => {
        const sessionDate = new Date(session.session_date)
        return sessionDate > now && session.status === 'scheduled'
      })
    )
    return sessions.sort((a, b) => 
      new Date(a.session_date).getTime() - new Date(b.session_date).getTime()
    )
  })

  // Actions
  const fetchCourses = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await api.lecturer.courses.getAll()
      
      if (response.success) {
        courses.value = response.data
        lastUpdated.value = new Date()
      } else {
        throw new Error(response.message || 'Failed to load courses')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load courses'
      console.error('Courses fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchCourseById = async (courseId: string) => {
    try {
      const response = await api.lecturer.courses.getById(courseId)
      
      if (response.success) {
        const course = response.data
        
        // Update the course in the courses array
        const index = courses.value.findIndex(c => c.id.toString() === courseId)
        if (index !== -1) {
          courses.value[index] = course
        } else {
          courses.value.push(course)
        }
        
        selectedCourse.value = course
        return course
      } else {
        throw new Error(response.message || 'Failed to load course')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load course'
      console.error('Course fetch error:', err)
      throw err
    }
  }

  const fetchCourseStudents = async (courseId: string) => {
    try {
      const response = await api.lecturer.courses.getStudents(courseId)
      
      if (response.success) {
        enrolledStudents.value = response.data
        return response.data
      } else {
        throw new Error(response.message || 'Failed to load course students')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load course students'
      console.error('Course students fetch error:', err)
      throw err
    }
  }

  const fetchCourseStatistics = async (courseId: string) => {
    try {
      const response = await api.lecturer.courses.getStatistics(courseId)
      
      if (response.success) {
        courseStatistics.value = response.data
        return response.data
      } else {
        throw new Error(response.message || 'Failed to load course statistics')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load course statistics'
      console.error('Course statistics fetch error:', err)
      throw err
    }
  }

  const uploadMaterial = async (courseId: string, material: FormData) => {
    try {
      const response = await api.lecturer.courses.uploadMaterial(courseId, material)
      
      if (response.success) {
        const newMaterial = response.data
        
        // Update local state
        const course = courses.value.find(c => c.id.toString() === courseId)
        if (course) {
          course.course_materials.push(newMaterial)
        }
        
        if (selectedCourse.value && selectedCourse.value.id.toString() === courseId) {
          selectedCourse.value.course_materials.push(newMaterial)
        }
        
        return newMaterial
      } else {
        throw new Error(response.message || 'Failed to upload material')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to upload material'
      console.error('Material upload error:', err)
      throw err
    }
  }

  const updateSyllabus = async (courseId: string, syllabusData: any) => {
    try {
      const response = await api.lecturer.courses.updateSyllabus(courseId, syllabusData)
      
      if (response.success) {
        // Update local state
        const course = courses.value.find(c => c.id.toString() === courseId)
        if (course) {
          course.syllabus_url = response.data.syllabus_url
        }
        
        if (selectedCourse.value && selectedCourse.value.id.toString() === courseId) {
          selectedCourse.value.syllabus_url = response.data.syllabus_url
        }
        
        return response.data
      } else {
        throw new Error(response.message || 'Failed to update syllabus')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update syllabus'
      console.error('Syllabus update error:', err)
      throw err
    }
  }

  const selectCourse = (course: CourseOffering) => {
    selectedCourse.value = course
  }

  const clearSelectedCourse = () => {
    selectedCourse.value = null
    enrolledStudents.value = []
    courseStatistics.value = null
  }

  // Utility functions
  const getCourseByCode = (courseCode: string) => {
    return courses.value.find(course => 
      course.curriculum_unit.code === courseCode
    )
  }

  const getCoursesByDeliveryMode = (mode: string) => {
    return courses.value.filter(course => course.delivery_mode === mode)
  }

  const getCoursesWithLowEnrollment = (threshold: number = 0.5) => {
    return courses.value.filter(course => 
      (course.current_enrollment / course.max_capacity) < threshold
    )
  }

  const getCoursesWithHighEnrollment = (threshold: number = 0.9) => {
    return courses.value.filter(course => 
      (course.current_enrollment / course.max_capacity) > threshold
    )
  }

  const getMaterialsByWeek = (courseId: string, weekNumber: number) => {
    const course = courses.value.find(c => c.id.toString() === courseId)
    if (!course) return []
    
    return course.course_materials.filter(material => 
      material.week_number === weekNumber
    )
  }

  const refreshCourseData = async (courseId: string) => {
    await Promise.all([
      fetchCourseById(courseId),
      fetchCourseStudents(courseId),
      fetchCourseStatistics(courseId)
    ])
  }

  return {
    // State
    courses,
    selectedCourse,
    courseStatistics,
    enrolledStudents,
    loading,
    error,
    lastUpdated,

    // Computed properties
    activeCourses,
    coursesByDeliveryMode,
    totalEnrollment,
    averageEnrollmentRate,
    upcomingSessions,

    // Actions
    fetchCourses,
    fetchCourseById,
    fetchCourseStudents,
    fetchCourseStatistics,
    uploadMaterial,
    updateSyllabus,
    selectCourse,
    clearSelectedCourse,
    refreshCourseData,

    // Utility functions
    getCourseByCode,
    getCoursesByDeliveryMode,
    getCoursesWithLowEnrollment,
    getCoursesWithHighEnrollment,
    getMaterialsByWeek,
  }
})
