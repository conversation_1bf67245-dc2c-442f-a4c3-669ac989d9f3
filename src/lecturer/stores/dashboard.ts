import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useApi } from '@/shared/composables/useBaseApi'
import type { 
  LecturerDashboard, 
  ClassSession, 
  StudentAlert, 
  Activity 
} from '@/shared/types/models/lecturer'

export const useLecturerDashboardStore = defineStore('lecturerDashboard', () => {
  const api = useBaseApi()

  // State
  const dashboard = ref<LecturerDashboard | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // Computed properties
  const teachingSummary = computed(() => dashboard.value?.teaching_summary || null)
  const attendanceSummary = computed(() => dashboard.value?.attendance_summary || null)
  const studentAlerts = computed(() => dashboard.value?.student_alerts || null)
  const recentActivities = computed(() => dashboard.value?.recent_activities || [])
  const upcomingDeadlines = computed(() => dashboard.value?.upcoming_deadlines || null)

  const totalCourses = computed(() => teachingSummary.value?.total_courses || 0)
  const totalStudents = computed(() => teachingSummary.value?.total_students || 0)
  const todaySessions = computed(() => teachingSummary.value?.active_sessions_today || [])
  const upcomingSessions = computed(() => teachingSummary.value?.upcoming_sessions || [])

  const criticalAlerts = computed(() => 
    studentAlerts.value?.students_needing_attention?.filter(alert => alert.severity === 'critical') || []
  )
  const lowAttendanceStudents = computed(() => 
    studentAlerts.value?.low_attendance_students || []
  )

  const averageAttendance = computed(() => 
    attendanceSummary.value?.average_class_attendance || 0
  )
  const attendanceTrend = computed(() => 
    attendanceSummary.value?.attendance_trend || 'stable'
  )

  // Actions
  const fetchDashboard = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await api.lecturer.dashboard.getData()
      
      if (response.success) {
        dashboard.value = response.data
        lastUpdated.value = new Date()
      } else {
        throw new Error(response.message || 'Failed to load dashboard data')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load dashboard'
      console.error('Dashboard fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const refreshDashboard = async () => {
    await fetchDashboard()
  }

  const fetchTeachingSummary = async () => {
    try {
      const response = await api.lecturer.dashboard.getTeachingSummary()
      
      if (response.success && dashboard.value) {
        dashboard.value.teaching_summary = response.data
        lastUpdated.value = new Date()
      }
    } catch (err) {
      console.error('Teaching summary fetch error:', err)
    }
  }

  const fetchStudentAlerts = async () => {
    try {
      const response = await api.lecturer.dashboard.getStudentAlerts()
      
      if (response.success && dashboard.value) {
        dashboard.value.student_alerts = response.data
        lastUpdated.value = new Date()
      }
    } catch (err) {
      console.error('Student alerts fetch error:', err)
    }
  }

  const fetchRecentActivities = async () => {
    try {
      const response = await api.lecturer.dashboard.getRecentActivities()
      
      if (response.success && dashboard.value) {
        dashboard.value.recent_activities = response.data
        lastUpdated.value = new Date()
      }
    } catch (err) {
      console.error('Recent activities fetch error:', err)
    }
  }

  const dismissAlert = async (alertId: number) => {
    try {
      // Optimistically remove the alert from the UI
      if (dashboard.value?.student_alerts) {
        dashboard.value.student_alerts.students_needing_attention = 
          dashboard.value.student_alerts.students_needing_attention.filter(
            alert => alert.id !== alertId
          )
        dashboard.value.student_alerts.low_attendance_students = 
          dashboard.value.student_alerts.low_attendance_students.filter(
            alert => alert.id !== alertId
          )
      }

      // Make API call to dismiss the alert
      // await api.lecturer.students.dismissAlert(alertId)
    } catch (err) {
      console.error('Failed to dismiss alert:', err)
      // Refresh dashboard to restore state on error
      await fetchDashboard()
    }
  }

  const markActivityAsRead = async (activityId: number) => {
    try {
      // Optimistically mark as read
      if (dashboard.value?.recent_activities) {
        const activity = dashboard.value.recent_activities.find(a => a.id === activityId)
        if (activity && activity.metadata) {
          activity.metadata.read = true
        }
      }

      // Make API call to mark as read
      // await api.lecturer.activities.markAsRead(activityId)
    } catch (err) {
      console.error('Failed to mark activity as read:', err)
      // Refresh dashboard to restore state on error
      await fetchDashboard()
    }
  }

  // Utility functions
  const getSessionsByType = (type: string) => {
    return todaySessions.value.filter(session => session.session_type === type)
  }

  const getAlertsBySeverity = (severity: 'low' | 'medium' | 'high' | 'critical') => {
    const allAlerts = [
      ...(studentAlerts.value?.students_needing_attention || []),
      ...(studentAlerts.value?.low_attendance_students || [])
    ]
    return allAlerts.filter(alert => alert.severity === severity)
  }

  const isDataStale = computed(() => {
    if (!lastUpdated.value) return true
    const now = new Date()
    const diffMinutes = (now.getTime() - lastUpdated.value.getTime()) / (1000 * 60)
    return diffMinutes > 15 // Consider data stale after 15 minutes
  })

  // Auto-refresh functionality
  const setupAutoRefresh = (intervalMinutes: number = 15) => {
    const interval = setInterval(() => {
      if (!loading.value) {
        refreshDashboard()
      }
    }, intervalMinutes * 60 * 1000)

    return () => clearInterval(interval)
  }

  return {
    // State
    dashboard,
    loading,
    error,
    lastUpdated,

    // Computed properties
    teachingSummary,
    attendanceSummary,
    studentAlerts,
    recentActivities,
    upcomingDeadlines,
    totalCourses,
    totalStudents,
    todaySessions,
    upcomingSessions,
    criticalAlerts,
    lowAttendanceStudents,
    averageAttendance,
    attendanceTrend,
    isDataStale,

    // Actions
    fetchDashboard,
    refreshDashboard,
    fetchTeachingSummary,
    fetchStudentAlerts,
    fetchRecentActivities,
    dismissAlert,
    markActivityAsRead,

    // Utility functions
    getSessionsByType,
    getAlertsBySeverity,
    setupAutoRefresh,
  }
})
