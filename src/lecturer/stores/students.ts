import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useApi } from '@/shared/composables/useBaseApi'
import type { 
  EnrolledStudent, 
  StudentAlert, 
  CourseStatistics 
} from '@/shared/types/models/lecturer'

export const useLecturerStudentsStore = defineStore('lecturerStudents', () => {
  const api = useBaseApi()

  // State
  const enrolledStudents = ref<EnrolledStudent[]>([])
  const selectedStudent = ref<EnrolledStudent | null>(null)
  const studentAlerts = ref<StudentAlert[]>([])
  const courseStatistics = ref<CourseStatistics | null>(null)
  const selectedCourseId = ref<number | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')
  const filterOptions = ref({
    attendanceThreshold: 75,
    academicStanding: 'all',
    riskLevel: 'all'
  })

  // Computed properties
  const filteredStudents = computed(() => {
    let filtered = enrolledStudents.value

    // Search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(student => 
        student.student_name.toLowerCase().includes(query) ||
        student.student_email.toLowerCase().includes(query) ||
        student.student_id.toString().includes(query)
      )
    }

    // Attendance filter
    if (filterOptions.value.attendanceThreshold > 0) {
      filtered = filtered.filter(student => 
        student.attendance_percentage >= filterOptions.value.attendanceThreshold
      )
    }

    // Academic standing filter
    if (filterOptions.value.academicStanding !== 'all') {
      filtered = filtered.filter(student => 
        student.academic_standing === filterOptions.value.academicStanding
      )
    }

    return filtered
  })

  const studentsAtRisk = computed(() => 
    enrolledStudents.value.filter(student => 
      student.attendance_percentage < 75 || 
      student.academic_standing === 'probation' ||
      (student.current_gpa && student.current_gpa < 2.0)
    )
  )

  const excellentStudents = computed(() => 
    enrolledStudents.value.filter(student => 
      student.attendance_percentage >= 95 && 
      (student.current_gpa && student.current_gpa >= 3.5)
    )
  )

  const averageAttendance = computed(() => {
    if (enrolledStudents.value.length === 0) return 0
    const total = enrolledStudents.value.reduce((sum, student) => 
      sum + student.attendance_percentage, 0
    )
    return Math.round(total / enrolledStudents.value.length)
  })

  const averageGPA = computed(() => {
    const studentsWithGPA = enrolledStudents.value.filter(student => student.current_gpa)
    if (studentsWithGPA.length === 0) return 0
    const total = studentsWithGPA.reduce((sum, student) => 
      sum + (student.current_gpa || 0), 0
    )
    return Math.round((total / studentsWithGPA.length) * 100) / 100
  })

  const criticalAlerts = computed(() => 
    studentAlerts.value.filter(alert => alert.severity === 'critical')
  )

  const unreadAlerts = computed(() => 
    studentAlerts.value.filter(alert => !alert.resolved_at)
  )

  // Actions
  const fetchEnrolledStudents = async (courseId?: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await api.lecturer.students.getAll(courseId?.toString())
      
      if (response.success) {
        enrolledStudents.value = response.data
        selectedCourseId.value = courseId || null
      } else {
        throw new Error(response.message || 'Failed to load enrolled students')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load enrolled students'
      console.error('Enrolled students fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchStudentById = async (studentId: string, courseId?: string) => {
    try {
      const response = await api.lecturer.students.getById(studentId, courseId)
      
      if (response.success) {
        selectedStudent.value = response.data
        
        // Update the student in the enrolled students array if it exists
        const index = enrolledStudents.value.findIndex(s => 
          s.student_id.toString() === studentId
        )
        if (index !== -1) {
          enrolledStudents.value[index] = response.data
        }
        
        return response.data
      } else {
        throw new Error(response.message || 'Failed to load student details')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load student details'
      console.error('Student fetch error:', err)
      throw err
    }
  }

  const updateStudentNotes = async (studentId: number, courseId: number, notes: string) => {
    try {
      const response = await api.lecturer.students.updateNotes(
        studentId.toString(), 
        courseId.toString(), 
        notes
      )
      
      if (response.success) {
        // Update local state
        const student = enrolledStudents.value.find(s => s.student_id === studentId)
        if (student) {
          student.notes = notes
        }
        
        if (selectedStudent.value && selectedStudent.value.student_id === studentId) {
          selectedStudent.value.notes = notes
        }
        
        return response.data
      } else {
        throw new Error(response.message || 'Failed to update student notes')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update student notes'
      console.error('Update student notes error:', err)
      throw err
    }
  }

  const fetchStudentAttendanceDetails = async (studentId: number, courseId: number) => {
    try {
      const response = await api.lecturer.students.getAttendanceDetails(
        studentId.toString(), 
        courseId.toString()
      )
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || 'Failed to load student attendance details')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load student attendance details'
      console.error('Student attendance details fetch error:', err)
      throw err
    }
  }

  const fetchStudentAlerts = async (studentId?: number) => {
    try {
      const response = await api.lecturer.students.getAlerts(studentId?.toString())
      
      if (response.success) {
        studentAlerts.value = response.data
        return response.data
      } else {
        throw new Error(response.message || 'Failed to load student alerts')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load student alerts'
      console.error('Student alerts fetch error:', err)
      throw err
    }
  }

  const resolveAlert = async (alertId: number) => {
    try {
      // Optimistically update the UI
      const alert = studentAlerts.value.find(a => a.id === alertId)
      if (alert) {
        alert.resolved_at = new Date().toISOString()
      }

      // Make API call to resolve the alert
      // await api.lecturer.students.resolveAlert(alertId)
    } catch (err) {
      console.error('Failed to resolve alert:', err)
      // Refresh alerts to restore state on error
      await fetchStudentAlerts()
    }
  }

  const selectStudent = (student: EnrolledStudent) => {
    selectedStudent.value = student
  }

  const clearSelectedStudent = () => {
    selectedStudent.value = null
  }

  // Search and filter functions
  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const updateFilterOptions = (options: Partial<typeof filterOptions.value>) => {
    filterOptions.value = { ...filterOptions.value, ...options }
  }

  const clearFilters = () => {
    searchQuery.value = ''
    filterOptions.value = {
      attendanceThreshold: 75,
      academicStanding: 'all',
      riskLevel: 'all'
    }
  }

  // Utility functions
  const getStudentsByAttendanceRange = (min: number, max: number) => {
    return enrolledStudents.value.filter(student => 
      student.attendance_percentage >= min && student.attendance_percentage <= max
    )
  }

  const getStudentsByAcademicStanding = (standing: string) => {
    return enrolledStudents.value.filter(student => 
      student.academic_standing === standing
    )
  }

  const getStudentRiskLevel = (student: EnrolledStudent): 'low' | 'medium' | 'high' | 'critical' => {
    const attendanceRisk = student.attendance_percentage < 60 ? 2 : 
                          student.attendance_percentage < 75 ? 1 : 0
    const gpaRisk = (student.current_gpa && student.current_gpa < 2.0) ? 2 : 
                   (student.current_gpa && student.current_gpa < 2.5) ? 1 : 0
    const standingRisk = student.academic_standing === 'probation' ? 2 : 0

    const totalRisk = attendanceRisk + gpaRisk + standingRisk

    if (totalRisk >= 4) return 'critical'
    if (totalRisk >= 3) return 'high'
    if (totalRisk >= 2) return 'medium'
    return 'low'
  }

  const sortStudents = (sortBy: 'name' | 'attendance' | 'gpa' | 'risk', order: 'asc' | 'desc' = 'asc') => {
    const sorted = [...enrolledStudents.value].sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'name':
          comparison = a.student_name.localeCompare(b.student_name)
          break
        case 'attendance':
          comparison = a.attendance_percentage - b.attendance_percentage
          break
        case 'gpa':
          comparison = (a.current_gpa || 0) - (b.current_gpa || 0)
          break
        case 'risk':
          const riskOrder = { low: 0, medium: 1, high: 2, critical: 3 }
          comparison = riskOrder[getStudentRiskLevel(a)] - riskOrder[getStudentRiskLevel(b)]
          break
      }

      return order === 'desc' ? -comparison : comparison
    })

    enrolledStudents.value = sorted
  }

  const exportStudentList = (format: 'csv' | 'excel' = 'csv') => {
    // Implementation would depend on the specific export library used
    console.log(`Exporting student list in ${format} format`)
  }

  return {
    // State
    enrolledStudents,
    selectedStudent,
    studentAlerts,
    courseStatistics,
    selectedCourseId,
    loading,
    error,
    searchQuery,
    filterOptions,

    // Computed properties
    filteredStudents,
    studentsAtRisk,
    excellentStudents,
    averageAttendance,
    averageGPA,
    criticalAlerts,
    unreadAlerts,

    // Actions
    fetchEnrolledStudents,
    fetchStudentById,
    updateStudentNotes,
    fetchStudentAttendanceDetails,
    fetchStudentAlerts,
    resolveAlert,
    selectStudent,
    clearSelectedStudent,

    // Search and filter functions
    setSearchQuery,
    updateFilterOptions,
    clearFilters,

    // Utility functions
    getStudentsByAttendanceRange,
    getStudentsByAcademicStanding,
    getStudentRiskLevel,
    sortStudents,
    exportStudentList,
  }
})
