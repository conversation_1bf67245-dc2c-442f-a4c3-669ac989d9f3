<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { But<PERSON> } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  UserCheck, 
  Video, 
  Edit, 
  X,
  ChevronLeft,
  ChevronRight,
  Plus
} from 'lucide-vue-next'

// Mock data for demonstration - in real app this would come from a store
const sessions = ref([
  {
    id: 1,
    course_code: 'CS101',
    course_name: 'Introduction to Computer Science',
    session_title: 'Variables and Data Types',
    session_type: 'lecture',
    date: '2024-01-15',
    start_time: '09:00',
    end_time: '10:30',
    location: 'Room A101',
    delivery_mode: 'in_person',
    status: 'scheduled',
    expected_attendees: 45,
    attendance_marked: false,
    online_meeting_url: null
  },
  {
    id: 2,
    course_code: 'CS101',
    course_name: 'Introduction to Computer Science',
    session_title: 'Lab Session - Basic Programming',
    session_type: 'practical',
    date: '2024-01-15',
    start_time: '14:00',
    end_time: '16:00',
    location: 'Computer Lab 1',
    delivery_mode: 'in_person',
    status: 'scheduled',
    expected_attendees: 22,
    attendance_marked: false,
    online_meeting_url: null
  },
  {
    id: 3,
    course_code: 'CS201',
    course_name: 'Data Structures',
    session_title: 'Binary Trees',
    session_type: 'lecture',
    date: '2024-01-16',
    start_time: '11:00',
    end_time: '12:30',
    location: 'Online',
    delivery_mode: 'online',
    status: 'scheduled',
    expected_attendees: 38,
    attendance_marked: false,
    online_meeting_url: 'https://meet.example.com/cs201-trees'
  }
])

const loading = ref(false)
const error = ref<string | null>(null)
const selectedWeek = ref(new Date())
const selectedSession = ref(null)

// Computed properties
const currentWeekStart = computed(() => {
  const date = new Date(selectedWeek.value)
  const day = date.getDay()
  const diff = date.getDate() - day
  return new Date(date.setDate(diff))
})

const weekDays = computed(() => {
  const days = []
  const start = new Date(currentWeekStart.value)
  
  for (let i = 0; i < 7; i++) {
    const day = new Date(start)
    day.setDate(start.getDate() + i)
    days.push(day)
  }
  
  return days
})

const sessionsByDay = computed(() => {
  const grouped: Record<string, any[]> = {}
  
  weekDays.value.forEach(day => {
    const dateStr = day.toISOString().split('T')[0]
    grouped[dateStr] = sessions.value.filter(session => session.date === dateStr)
      .sort((a, b) => a.start_time.localeCompare(b.start_time))
  })
  
  return grouped
})

const todaysSessions = computed(() => {
  const today = new Date().toISOString().split('T')[0]
  return sessions.value.filter(session => session.date === today)
})

// Helper functions
const formatTime = (timeString: string) => {
  return new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const getSessionTypeColor = (type: string) => {
  switch (type) {
    case 'lecture': return 'default'
    case 'tutorial': return 'secondary'
    case 'practical': return 'outline'
    case 'laboratory': return 'destructive'
    case 'seminar': return 'default'
    default: return 'secondary'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'scheduled': return 'default'
    case 'in_progress': return 'destructive'
    case 'completed': return 'secondary'
    case 'cancelled': return 'outline'
    default: return 'secondary'
  }
}

const isToday = (date: Date) => {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

const navigateWeek = (direction: 'prev' | 'next') => {
  const newDate = new Date(selectedWeek.value)
  newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))
  selectedWeek.value = newDate
}

const goToToday = () => {
  selectedWeek.value = new Date()
}

const markAttendance = (session: any) => {
  // Navigate to attendance marking for this session
  console.log('Mark attendance for session:', session.id)
}

const joinOnlineMeeting = (session: any) => {
  if (session.online_meeting_url) {
    window.open(session.online_meeting_url, '_blank')
  }
}

const editSession = (session: any) => {
  selectedSession.value = session
  console.log('Edit session:', session.id)
}

const cancelSession = (session: any) => {
  console.log('Cancel session:', session.id)
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">Timetable</h1>
        <p class="text-muted-foreground">
          Manage your teaching schedule and sessions
        </p>
      </div>
      <Button variant="default">
        <Plus class="mr-2 h-4 w-4" />
        Add Session
      </Button>
    </div>

    <!-- Error State -->
    <Alert v-if="error" variant="destructive">
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Today's Sessions Summary -->
    <Card v-if="todaysSessions.length > 0">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Clock class="h-5 w-5" />
          Today's Sessions
        </CardTitle>
        <CardDescription>
          {{ todaysSessions.length }} session{{ todaysSessions.length !== 1 ? 's' : '' }} scheduled for today
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
          <div 
            v-for="session in todaysSessions" 
            :key="session.id"
            class="flex items-center justify-between p-3 border rounded-lg"
          >
            <div class="flex-1">
              <p class="font-medium">{{ session.course_code }}</p>
              <p class="text-sm text-muted-foreground">{{ session.session_title }}</p>
              <p class="text-xs text-muted-foreground">
                {{ formatTime(session.start_time) }} - {{ formatTime(session.end_time) }}
              </p>
            </div>
            <div class="flex items-center gap-2">
              <Badge :variant="getSessionTypeColor(session.session_type)">
                {{ session.session_type }}
              </Badge>
              <Button size="sm" variant="outline" @click="markAttendance(session)">
                <UserCheck class="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Week Navigation -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="navigateWeek('prev')">
          <ChevronLeft class="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" @click="goToToday">
          Today
        </Button>
        <Button variant="outline" size="sm" @click="navigateWeek('next')">
          <ChevronRight class="h-4 w-4" />
        </Button>
      </div>
      <h2 class="text-lg font-semibold">
        Week of {{ currentWeekStart.toLocaleDateString() }}
      </h2>
    </div>

    <!-- Weekly Timetable -->
    <div class="grid grid-cols-7 gap-4">
      <!-- Day Headers -->
      <div 
        v-for="day in weekDays" 
        :key="day.toISOString()"
        class="text-center p-2 border-b"
      >
        <div class="font-medium" :class="{ 'text-primary': isToday(day) }">
          {{ day.toLocaleDateString([], { weekday: 'short' }) }}
        </div>
        <div class="text-sm text-muted-foreground" :class="{ 'text-primary font-medium': isToday(day) }">
          {{ day.getDate() }}
        </div>
      </div>

      <!-- Session Slots -->
      <div 
        v-for="day in weekDays" 
        :key="`sessions-${day.toISOString()}`"
        class="min-h-[400px] border rounded-lg p-2 space-y-2"
        :class="{ 'bg-primary/5 border-primary': isToday(day) }"
      >
        <div 
          v-for="session in sessionsByDay[day.toISOString().split('T')[0]] || []" 
          :key="session.id"
          class="p-3 border rounded-lg bg-card hover:shadow-sm transition-shadow cursor-pointer"
          @click="editSession(session)"
        >
          <!-- Session Header -->
          <div class="flex items-start justify-between mb-2">
            <div class="flex-1">
              <p class="font-medium text-sm">{{ session.course_code }}</p>
              <p class="text-xs text-muted-foreground">{{ session.session_title }}</p>
            </div>
            <Badge :variant="getStatusColor(session.status)" class="text-xs">
              {{ session.status }}
            </Badge>
          </div>

          <!-- Time and Location -->
          <div class="space-y-1 mb-2">
            <div class="flex items-center gap-1 text-xs text-muted-foreground">
              <Clock class="h-3 w-3" />
              <span>{{ formatTime(session.start_time) }} - {{ formatTime(session.end_time) }}</span>
            </div>
            <div class="flex items-center gap-1 text-xs text-muted-foreground">
              <MapPin class="h-3 w-3" />
              <span>{{ session.location }}</span>
            </div>
            <div class="flex items-center gap-1 text-xs text-muted-foreground">
              <Users class="h-3 w-3" />
              <span>{{ session.expected_attendees }} students</span>
            </div>
          </div>

          <!-- Session Type -->
          <div class="flex items-center justify-between">
            <Badge :variant="getSessionTypeColor(session.session_type)" class="text-xs">
              {{ session.session_type }}
            </Badge>
            
            <!-- Action Buttons -->
            <div class="flex items-center gap-1">
              <Button 
                v-if="session.online_meeting_url" 
                size="sm" 
                variant="ghost" 
                class="h-6 w-6 p-0"
                @click.stop="joinOnlineMeeting(session)"
              >
                <Video class="h-3 w-3" />
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                class="h-6 w-6 p-0"
                @click.stop="markAttendance(session)"
              >
                <UserCheck class="h-3 w-3" />
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                class="h-6 w-6 p-0"
                @click.stop="editSession(session)"
              >
                <Edit class="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        <!-- Empty Day Message -->
        <div 
          v-if="!sessionsByDay[day.toISOString().split('T')[0]]?.length"
          class="text-center py-8 text-muted-foreground text-sm"
        >
          No sessions scheduled
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="flex items-center gap-4 pt-4 border-t">
      <Button variant="outline" size="sm">
        <Calendar class="mr-2 h-4 w-4" />
        Export Schedule
      </Button>
      <Button variant="outline" size="sm">
        <UserCheck class="mr-2 h-4 w-4" />
        Bulk Attendance
      </Button>
      <Button variant="outline" size="sm">
        <Video class="mr-2 h-4 w-4" />
        Meeting Links
      </Button>
    </div>
  </div>
</template>
