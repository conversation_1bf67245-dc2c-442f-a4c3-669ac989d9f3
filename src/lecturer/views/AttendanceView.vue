<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { useLecturerAttendanceStore } from '@/stores/lecturerAttendance'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Checkbox } from '@/shared/components/ui/checkbox'
import { Input } from '@/shared/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Avatar, AvatarFallback, AvatarImage } from '@/shared/components/ui/avatar'
import { 
  UserCheck, 
  Users, 
  Calendar, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Search,
  Filter,
  Download,
  BarChart3,
  Save
} from 'lucide-vue-next'

const attendanceStore = useLecturerAttendanceStore()
const searchQuery = ref('')
const selectedCourse = ref('all')

// Computed properties for easy access
const {
  attendanceSessions,
  selectedSession,
  loading,
  error,
  sessionsRequiringAttendance,
  completedSessions,
  todaysSessions,
  averageAttendanceRate,
  bulkUpdateMode,
  selectedStudents
} = attendanceStore

// Mock data for demonstration
const mockSession = ref({
  class_session_id: 1,
  course_code: 'CS101',
  course_name: 'Introduction to Computer Science',
  session_title: 'Variables and Data Types',
  session_date: '2024-01-15',
  start_time: '09:00',
  end_time: '10:30',
  location: 'Room A101',
  expected_students: [
    {
      student_id: 1,
      student_name: 'Alice Johnson',
      student_email: '<EMAIL>',
      avatar_url: null,
      attendance_rate: 95,
      recent_pattern: 'regular'
    },
    {
      student_id: 2,
      student_name: 'Bob Smith',
      student_email: '<EMAIL>',
      avatar_url: null,
      attendance_rate: 78,
      recent_pattern: 'irregular'
    },
    {
      student_id: 3,
      student_name: 'Carol Davis',
      student_email: '<EMAIL>',
      avatar_url: null,
      attendance_rate: 88,
      recent_pattern: 'regular'
    }
  ],
  current_attendance: [
    {
      student_id: 1,
      status: 'present',
      check_in_time: '09:05',
      minutes_late: 5,
      participation_level: 'excellent',
      participation_score: 95
    },
    {
      student_id: 2,
      status: 'absent',
      minutes_late: 0
    },
    {
      student_id: 3,
      status: 'late',
      check_in_time: '09:15',
      minutes_late: 15,
      participation_level: 'good',
      participation_score: 80
    }
  ],
  is_marked: false
})

// Load attendance data on component mount
onMounted(async () => {
  await attendanceStore.fetchAttendanceSessions()
  // For demo, set the mock session as selected
  attendanceStore.selectedSession = mockSession.value
})

// Helper functions
const getStatusColor = (status: string) => {
  switch (status) {
    case 'present': return 'text-green-600'
    case 'absent': return 'text-red-600'
    case 'late': return 'text-yellow-600'
    case 'excused': return 'text-blue-600'
    default: return 'text-gray-600'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'present': return CheckCircle
    case 'absent': return XCircle
    case 'late': return AlertTriangle
    case 'excused': return CheckCircle
    default: return XCircle
  }
}

const getAttendanceRateColor = (rate: number) => {
  if (rate >= 90) return 'text-green-600'
  if (rate >= 75) return 'text-yellow-600'
  return 'text-red-600'
}

const updateAttendanceStatus = (studentId: number, status: string) => {
  if (!selectedSession.value) return
  
  const attendanceIndex = selectedSession.value.current_attendance.findIndex(
    record => record.student_id === studentId
  )
  
  if (attendanceIndex !== -1) {
    selectedSession.value.current_attendance[attendanceIndex].status = status
    if (status === 'present') {
      selectedSession.value.current_attendance[attendanceIndex].check_in_time = new Date().toTimeString().slice(0, 5)
      selectedSession.value.current_attendance[attendanceIndex].minutes_late = 0
    }
  } else {
    selectedSession.value.current_attendance.push({
      student_id: studentId,
      status,
      check_in_time: status === 'present' ? new Date().toTimeString().slice(0, 5) : undefined,
      minutes_late: 0
    })
  }
}

const getStudentAttendanceStatus = (studentId: number) => {
  if (!selectedSession.value) return 'absent'
  const record = selectedSession.value.current_attendance.find(r => r.student_id === studentId)
  return record?.status || 'absent'
}

const saveAttendance = async () => {
  if (!selectedSession.value) return
  
  try {
    await attendanceStore.markAttendance(
      selectedSession.value.class_session_id.toString(),
      selectedSession.value.current_attendance
    )
    // Show success message
  } catch (error) {
    console.error('Failed to save attendance:', error)
  }
}

const exportAttendance = () => {
  console.log('Export attendance data')
}

const toggleBulkMode = () => {
  attendanceStore.toggleBulkMode()
}

const bulkMarkPresent = () => {
  attendanceStore.bulkMarkStatus('present')
}

const bulkMarkAbsent = () => {
  attendanceStore.bulkMarkStatus('absent')
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">Attendance Management</h1>
        <p class="text-muted-foreground">
          Mark and manage student attendance for your sessions
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="exportAttendance" variant="outline">
          <Download class="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button @click="toggleBulkMode" :variant="bulkUpdateMode ? 'default' : 'outline'">
          <Users class="mr-2 h-4 w-4" />
          Bulk Mode
        </Button>
      </div>
    </div>

    <!-- Error State -->
    <Alert v-if="error" variant="destructive">
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Attendance Overview -->
    <div class="grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Sessions Today</CardTitle>
          <Calendar class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ todaysSessions.length }}</div>
          <p class="text-xs text-muted-foreground">
            Scheduled for today
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Pending</CardTitle>
          <Clock class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ sessionsRequiringAttendance.length }}</div>
          <p class="text-xs text-muted-foreground">
            Attendance to mark
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Completed</CardTitle>
          <CheckCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ completedSessions.length }}</div>
          <p class="text-xs text-muted-foreground">
            This week
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Avg. Attendance</CardTitle>
          <BarChart3 class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ Math.round(averageAttendanceRate) }}%</div>
          <p class="text-xs text-muted-foreground">
            Across all sessions
          </p>
        </CardContent>
      </Card>
    </div>

    <!-- Main Content -->
    <Tabs default-value="mark-attendance" class="space-y-4">
      <TabsList>
        <TabsTrigger value="mark-attendance">Mark Attendance</TabsTrigger>
        <TabsTrigger value="sessions">Sessions</TabsTrigger>
        <TabsTrigger value="analytics">Analytics</TabsTrigger>
      </TabsList>

      <!-- Mark Attendance Tab -->
      <TabsContent value="mark-attendance" class="space-y-4">
        <!-- Session Selection -->
        <Card v-if="selectedSession">
          <CardHeader>
            <div class="flex items-center justify-between">
              <div>
                <CardTitle>{{ selectedSession.course_code }} - {{ selectedSession.session_title }}</CardTitle>
                <CardDescription>
                  {{ new Date(selectedSession.session_date).toLocaleDateString() }} • 
                  {{ selectedSession.start_time }} - {{ selectedSession.end_time }} • 
                  {{ selectedSession.location }}
                </CardDescription>
              </div>
              <div class="flex items-center gap-2">
                <Badge v-if="selectedSession.is_marked" variant="secondary">
                  Marked
                </Badge>
                <Button @click="saveAttendance" :disabled="loading">
                  <Save class="mr-2 h-4 w-4" />
                  Save Attendance
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        <!-- Bulk Actions -->
        <Card v-if="bulkUpdateMode && selectedStudents.size > 0">
          <CardContent class="pt-6">
            <div class="flex items-center gap-4">
              <span class="text-sm font-medium">{{ selectedStudents.size }} students selected</span>
              <div class="flex items-center gap-2">
                <Button size="sm" @click="bulkMarkPresent" variant="outline">
                  <CheckCircle class="mr-2 h-4 w-4" />
                  Mark Present
                </Button>
                <Button size="sm" @click="bulkMarkAbsent" variant="outline">
                  <XCircle class="mr-2 h-4 w-4" />
                  Mark Absent
                </Button>
                <Button size="sm" @click="attendanceStore.clearSelectedStudents()" variant="ghost">
                  Clear Selection
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Student List -->
        <Card v-if="selectedSession">
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>Students ({{ selectedSession.expected_students.length }})</CardTitle>
              <div class="flex items-center gap-2">
                <div class="relative">
                  <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    v-model="searchQuery"
                    placeholder="Search students..."
                    class="pl-8 w-64"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <div 
                v-for="student in selectedSession.expected_students" 
                :key="student.student_id"
                class="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
              >
                <div class="flex items-center gap-4">
                  <!-- Bulk Selection Checkbox -->
                  <Checkbox 
                    v-if="bulkUpdateMode"
                    :checked="selectedStudents.has(student.student_id)"
                    @update:checked="attendanceStore.selectStudent(student.student_id)"
                  />
                  
                  <!-- Student Info -->
                  <Avatar class="h-10 w-10">
                    <AvatarImage :src="student.avatar_url" />
                    <AvatarFallback>{{ student.student_name.split(' ').map(n => n[0]).join('') }}</AvatarFallback>
                  </Avatar>
                  
                  <div>
                    <p class="font-medium">{{ student.student_name }}</p>
                    <p class="text-sm text-muted-foreground">{{ student.student_email }}</p>
                    <div class="flex items-center gap-2 mt-1">
                      <span class="text-xs text-muted-foreground">Attendance Rate:</span>
                      <span :class="['text-xs font-medium', getAttendanceRateColor(student.attendance_rate)]">
                        {{ student.attendance_rate }}%
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Attendance Status and Controls -->
                <div class="flex items-center gap-2">
                  <div class="flex items-center gap-1">
                    <component 
                      :is="getStatusIcon(getStudentAttendanceStatus(student.student_id))" 
                      :class="['h-4 w-4', getStatusColor(getStudentAttendanceStatus(student.student_id))]"
                    />
                    <span :class="['text-sm font-medium', getStatusColor(getStudentAttendanceStatus(student.student_id))]">
                      {{ getStudentAttendanceStatus(student.student_id) }}
                    </span>
                  </div>
                  
                  <div class="flex items-center gap-1">
                    <Button 
                      size="sm" 
                      variant="outline"
                      :class="getStudentAttendanceStatus(student.student_id) === 'present' ? 'bg-green-50 border-green-200' : ''"
                      @click="updateAttendanceStatus(student.student_id, 'present')"
                    >
                      Present
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      :class="getStudentAttendanceStatus(student.student_id) === 'late' ? 'bg-yellow-50 border-yellow-200' : ''"
                      @click="updateAttendanceStatus(student.student_id, 'late')"
                    >
                      Late
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      :class="getStudentAttendanceStatus(student.student_id) === 'absent' ? 'bg-red-50 border-red-200' : ''"
                      @click="updateAttendanceStatus(student.student_id, 'absent')"
                    >
                      Absent
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- No Session Selected -->
        <Card v-else>
          <CardContent class="pt-6">
            <div class="text-center py-8">
              <UserCheck class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 class="text-lg font-semibold mb-2">No Session Selected</h3>
              <p class="text-muted-foreground">
                Select a session from the Sessions tab to mark attendance.
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Sessions Tab -->
      <TabsContent value="sessions">
        <Card>
          <CardContent class="pt-6">
            <div class="text-center py-8">
              <Calendar class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 class="text-lg font-semibold mb-2">Sessions List</h3>
              <p class="text-muted-foreground">
                Session management functionality would be implemented here.
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Analytics Tab -->
      <TabsContent value="analytics">
        <Card>
          <CardContent class="pt-6">
            <div class="text-center py-8">
              <BarChart3 class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 class="text-lg font-semibold mb-2">Attendance Analytics</h3>
              <p class="text-muted-foreground">
                Analytics and reporting functionality would be implemented here.
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
