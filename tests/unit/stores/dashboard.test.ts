import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useDashboardStore } from '@/student/stores/dashboard'

// Mock API
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn()
}

vi.mock('@/shared/services/api', () => ({
  api: mockApi
}))

describe('Dashboard Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const store = useDashboardStore()
      
      expect(store.currentSemester).toBeNull()
      expect(store.gpaCalculations).toEqual([])
      expect(store.academicHolds).toEqual([])
      expect(store.upcomingDeadlines).toEqual([])
      expect(store.recentAnnouncements).toEqual([])
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
    })
  })

  describe('Getters', () => {
    it('should calculate current GPA correctly', () => {
      const store = useDashboardStore()
      
      store.gpaCalculations = [
        {
          id: '1',
          student_id: 'student-1',
          semester_id: 'sem-1',
          cumulative_gpa: 3.5,
          semester_gpa: 3.7,
          total_credit_points: 120,
          total_grade_points: 420,
          calculation_date: '2024-01-15',
          student: {} as any,
          semester: {} as any
        }
      ]
      
      expect(store.currentGPA).toBe(3.5)
    })

    it('should return 0 for current GPA when no calculations exist', () => {
      const store = useDashboardStore()
      expect(store.currentGPA).toBe(0)
    })

    it('should calculate total credits correctly', () => {
      const store = useDashboardStore()
      
      store.gpaCalculations = [
        {
          id: '1',
          student_id: 'student-1',
          semester_id: 'sem-1',
          cumulative_gpa: 3.5,
          semester_gpa: 3.7,
          total_credit_points: 120,
          total_grade_points: 420,
          calculation_date: '2024-01-15',
          student: {} as any,
          semester: {} as any
        }
      ]
      
      expect(store.totalCredits).toBe(120)
    })

    it('should identify critical holds', () => {
      const store = useDashboardStore()
      
      store.academicHolds = [
        {
          id: '1',
          student_id: 'student-1',
          hold_type: 'financial',
          description: 'Outstanding fees',
          severity: 'high',
          placed_date: '2024-01-01',
          resolved_date: null,
          student: {} as any
        },
        {
          id: '2',
          student_id: 'student-1',
          hold_type: 'academic',
          description: 'Low GPA',
          severity: 'medium',
          placed_date: '2024-01-01',
          resolved_date: null,
          student: {} as any
        }
      ]
      
      expect(store.criticalHolds).toHaveLength(1)
      expect(store.criticalHolds[0].severity).toBe('high')
    })
  })

  describe('Actions', () => {
    it('should fetch dashboard data successfully', async () => {
      const store = useDashboardStore()
      
      const mockData = {
        currentSemester: {
          id: 'sem-1',
          name: 'Spring 2024',
          code: 'SP24'
        },
        gpaCalculations: [],
        academicHolds: [],
        upcomingDeadlines: [],
        recentAnnouncements: []
      }
      
      mockApi.get.mockResolvedValueOnce({ data: mockData })
      
      await store.fetchDashboardData()
      
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.currentSemester).toEqual(mockData.currentSemester)
      expect(mockApi.get).toHaveBeenCalledWith('/student/dashboard')
    })

    it('should handle fetch dashboard data error', async () => {
      const store = useDashboardStore()
      
      const error = new Error('Network error')
      mockApi.get.mockRejectedValueOnce(error)
      
      await store.fetchDashboardData()
      
      expect(store.isLoading).toBe(false)
      expect(store.error).toBe('Failed to load dashboard data')
    })

    it('should fetch GPA calculations successfully', async () => {
      const store = useDashboardStore()
      
      const mockGPAData = [
        {
          id: '1',
          student_id: 'student-1',
          semester_id: 'sem-1',
          cumulative_gpa: 3.5,
          semester_gpa: 3.7,
          total_credit_points: 120,
          total_grade_points: 420,
          calculation_date: '2024-01-15',
          student: {} as any,
          semester: {} as any
        }
      ]
      
      mockApi.get.mockResolvedValueOnce({ data: mockGPAData })
      
      await store.fetchGPACalculations()
      
      expect(store.gpaCalculations).toEqual(mockGPAData)
      expect(mockApi.get).toHaveBeenCalledWith('/student/gpa-calculations')
    })

    it('should fetch academic holds successfully', async () => {
      const store = useDashboardStore()
      
      const mockHolds = [
        {
          id: '1',
          student_id: 'student-1',
          hold_type: 'financial',
          description: 'Outstanding fees',
          severity: 'high',
          placed_date: '2024-01-01',
          resolved_date: null,
          student: {} as any
        }
      ]
      
      mockApi.get.mockResolvedValueOnce({ data: mockHolds })
      
      await store.fetchAcademicHolds()
      
      expect(store.academicHolds).toEqual(mockHolds)
      expect(mockApi.get).toHaveBeenCalledWith('/student/academic-holds')
    })

    it('should acknowledge announcement successfully', async () => {
      const store = useDashboardStore()
      
      store.recentAnnouncements = [
        {
          id: '1',
          title: 'Test Announcement',
          content: 'Test content',
          priority: 'medium',
          published_date: '2024-01-01',
          acknowledged: false
        }
      ]
      
      mockApi.post.mockResolvedValueOnce({ data: { success: true } })
      
      await store.acknowledgeAnnouncement('1')
      
      expect(store.recentAnnouncements[0].acknowledged).toBe(true)
      expect(mockApi.post).toHaveBeenCalledWith('/student/announcements/1/acknowledge')
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      const store = useDashboardStore()
      
      mockApi.get.mockRejectedValueOnce(new Error('Network error'))
      
      await store.fetchDashboardData()
      
      expect(store.error).toBe('Failed to load dashboard data')
      expect(store.isLoading).toBe(false)
    })

    it('should handle API errors with custom messages', async () => {
      const store = useDashboardStore()
      
      const apiError = {
        response: {
          data: {
            message: 'Custom error message'
          }
        }
      }
      
      mockApi.get.mockRejectedValueOnce(apiError)
      
      await store.fetchDashboardData()
      
      expect(store.error).toBe('Failed to load dashboard data')
    })
  })
})
