import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'

describe('LoadingSpinner', () => {
  describe('Rendering', () => {
    it('renders with default props', () => {
      const wrapper = mount(LoadingSpinner)
      
      expect(wrapper.find('svg').exists()).toBe(true)
      expect(wrapper.find('svg').classes()).toContain('animate-spin')
      expect(wrapper.find('svg').classes()).toContain('h-6')
      expect(wrapper.find('svg').classes()).toContain('w-6')
      expect(wrapper.find('svg').classes()).toContain('text-blue-600')
    })

    it('renders with custom size', () => {
      const wrapper = mount(LoadingSpinner, {
        props: { size: 'lg' }
      })
      
      const svg = wrapper.find('svg')
      expect(svg.classes()).toContain('h-8')
      expect(svg.classes()).toContain('w-8')
    })

    it('renders with custom variant', () => {
      const wrapper = mount(LoadingSpinner, {
        props: { variant: 'white' }
      })
      
      const svg = wrapper.find('svg')
      expect(svg.classes()).toContain('text-white')
    })

    it('renders with text', () => {
      const wrapper = mount(LoadingSpinner, {
        props: { text: 'Loading...' }
      })
      
      expect(wrapper.text()).toContain('Loading...')
    })
  })

  describe('Overlay Mode', () => {
    it('renders as overlay when overlay prop is true', () => {
      const wrapper = mount(LoadingSpinner, {
        props: { overlay: true }
      })
      
      expect(wrapper.classes()).toContain('absolute')
      expect(wrapper.classes()).toContain('inset-0')
      expect(wrapper.classes()).toContain('bg-white/80')
    })

    it('renders as fullscreen when fullscreen prop is true', () => {
      const wrapper = mount(LoadingSpinner, {
        props: { fullscreen: true }
      })
      
      expect(wrapper.classes()).toContain('fixed')
      expect(wrapper.classes()).toContain('inset-0')
      expect(wrapper.classes()).toContain('z-50')
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      const wrapper = mount(LoadingSpinner)
      
      expect(wrapper.attributes('role')).toBe('status')
      expect(wrapper.attributes('aria-live')).toBe('polite')
      expect(wrapper.attributes('aria-label')).toBe('Loading')
    })

    it('has aria-hidden on SVG', () => {
      const wrapper = mount(LoadingSpinner)
      
      const svg = wrapper.find('svg')
      expect(svg.attributes('aria-hidden')).toBe('true')
    })
  })

  describe('Size Variants', () => {
    const sizes = [
      { size: 'sm', expectedClass: 'h-4' },
      { size: 'md', expectedClass: 'h-6' },
      { size: 'lg', expectedClass: 'h-8' },
      { size: 'xl', expectedClass: 'h-12' }
    ]

    sizes.forEach(({ size, expectedClass }) => {
      it(`renders ${size} size correctly`, () => {
        const wrapper = mount(LoadingSpinner, {
          props: { size: size as any }
        })
        
        const svg = wrapper.find('svg')
        expect(svg.classes()).toContain(expectedClass)
      })
    })
  })

  describe('Color Variants', () => {
    const variants = [
      { variant: 'primary', expectedClass: 'text-blue-600' },
      { variant: 'secondary', expectedClass: 'text-gray-600' },
      { variant: 'white', expectedClass: 'text-white' }
    ]

    variants.forEach(({ variant, expectedClass }) => {
      it(`renders ${variant} variant correctly`, () => {
        const wrapper = mount(LoadingSpinner, {
          props: { variant: variant as any }
        })
        
        const svg = wrapper.find('svg')
        expect(svg.classes()).toContain(expectedClass)
      })
    })
  })

  describe('Text Rendering', () => {
    it('renders text with correct size classes', () => {
      const wrapper = mount(LoadingSpinner, {
        props: { 
          text: 'Loading data...',
          size: 'lg'
        }
      })
      
      const textElement = wrapper.find('span, div').filter(el => 
        el.text() === 'Loading data...'
      )[0]
      
      expect(textElement.exists()).toBe(true)
      expect(textElement.classes()).toContain('text-lg')
    })

    it('does not render text container when no text provided', () => {
      const wrapper = mount(LoadingSpinner)
      
      const textElements = wrapper.findAll('span, div').filter(el => 
        el.classes().some(cls => cls.includes('font-medium'))
      )
      
      expect(textElements).toHaveLength(0)
    })
  })
})
