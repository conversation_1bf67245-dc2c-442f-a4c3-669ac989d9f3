# Portal SwinX

A Vue 3 application built with modern development tools and best practices.

## Features

- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Pinia** for state management
- **Vue Router** for routing
- **Tailwind CSS** for styling
- **VueUse** for utility composables
- **ESLint & Prettier** for code quality

## API Integration

The application uses a custom `useApi` composable built on top of VueUse's `createFetch` for API communication. The API handler includes:

### Features

- **Automatic Bearer Token Authentication**: Tokens are automatically added to request headers
- **Smart Token Refresh**: When a 401 error is detected, the composable automatically:
  1. Attempts to refresh the expired token
  2. Retries the original request with the new token
  3. Logs out the user if token refresh fails
- **On-Demand Token Refresh**: No periodic token refresh intervals - tokens are only refreshed when needed

### Usage Example

```typescript
import { useApi } from '@/composables/useApi'

const api = useApi()

// GET request
const users = await api.get('/users')

// POST request
const newUser = await api.post('/users', {
  name: '<PERSON>',
  email: '<EMAIL>',
})

// PUT request
const updatedUser = await api.put('/users/1', {
  name: 'Jane Doe',
})

// DELETE request
await api.delete('/users/1')
```

### API Response Format

All API responses follow a consistent format:

```typescript
interface ApiResponse<T = unknown> {
  success: boolean
  message?: string
  data?: T
  errors?: Record<string, string[]>
}
```

### Error Handling

The API composable automatically handles:

- **401 Unauthorized**: Attempts token refresh and retries the request
- **Network Errors**: Logs errors and propagates them to the calling code
- **Token Refresh Failures**: Automatically logs out the user

## Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Run type checking
pnpm type-check
```

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
pnpm test:unit
```

### Run End-to-End Tests with [Cypress](https://www.cypress.io/)

```sh
pnpm test:e2e:dev
```

This runs the end-to-end tests against the Vite development server.
It is much faster than the production build.

But it's still recommended to test the production build with `test:e2e` before deploying (e.g. in CI environments):

```sh
pnpm build
pnpm test:e2e
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm lint
```
