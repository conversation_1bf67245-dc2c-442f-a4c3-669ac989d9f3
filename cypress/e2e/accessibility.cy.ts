describe('Accessibility Tests', () => {
  beforeEach(() => {
    // Mock authentication
    cy.window().then((win) => {
      win.localStorage.setItem('auth_token', 'mock-token')
    })
    
    // Inject axe-core for accessibility testing
    cy.injectAxe()
  })

  describe('Dashboard Accessibility', () => {
    it('should have no accessibility violations on dashboard', () => {
      cy.intercept('GET', '/api/student/dashboard', {
        fixture: 'dashboard-data.json'
      }).as('getDashboardData')
      
      cy.visit('/dashboard')
      cy.wait('@getDashboardData')
      
      // Check for accessibility violations
      cy.checkA11y()
    })

    it('should have proper focus management', () => {
      cy.visit('/dashboard')
      
      // Check that focus is visible
      cy.get('body').tab()
      cy.focused().should('be.visible')
      cy.focused().should('have.css', 'outline-style', 'solid')
        .or('have.css', 'box-shadow')
    })

    it('should support keyboard navigation', () => {
      cy.visit('/dashboard')
      
      // Test tab navigation
      cy.get('body').tab()
      cy.focused().should('be.visible')
      
      // Test arrow key navigation in menus
      cy.get('[data-testid="main-menu"]').focus()
      cy.focused().type('{downarrow}')
      cy.focused().should('not.equal', cy.get('[data-testid="main-menu"]'))
    })

    it('should have proper ARIA landmarks', () => {
      cy.visit('/dashboard')
      
      cy.get('[role="main"]').should('exist')
      cy.get('[role="navigation"]').should('exist')
      cy.get('[role="banner"]').should('exist')
    })
  })

  describe('Course Registration Accessibility', () => {
    it('should have no accessibility violations on course registration', () => {
      cy.intercept('GET', '/api/student/course-offerings', {
        fixture: 'course-offerings.json'
      }).as('getCourseOfferings')
      
      cy.visit('/courses')
      cy.wait('@getCourseOfferings')
      
      cy.checkA11y()
    })

    it('should announce cart updates to screen readers', () => {
      cy.visit('/courses')
      
      // Add course to cart
      cy.get('[data-testid="course-card"]').first().within(() => {
        cy.get('[data-testid="add-to-cart"]').click()
      })
      
      // Check for ARIA live region update
      cy.get('[aria-live="polite"]').should('contain.text', 'added to cart')
    })

    it('should have proper form labels and descriptions', () => {
      cy.visit('/courses')
      
      // Check search form
      cy.get('[data-testid="course-search"]').should('have.attr', 'aria-label')
      cy.get('[data-testid="semester-filter"]').should('have.attr', 'aria-label')
      
      // Check that form controls have labels
      cy.get('input, select, textarea').each(($el) => {
        cy.wrap($el).should('satisfy', (el) => {
          return el.getAttribute('aria-label') || 
                 el.getAttribute('aria-labelledby') ||
                 el.closest('label') ||
                 el.previousElementSibling?.tagName === 'LABEL'
        })
      })
    })
  })

  describe('Timetable Accessibility', () => {
    it('should have no accessibility violations on timetable', () => {
      cy.intercept('GET', '/api/student/timetable', {
        fixture: 'timetable-data.json'
      }).as('getTimetableData')
      
      cy.visit('/schedule')
      cy.wait('@getTimetableData')
      
      cy.checkA11y()
    })

    it('should have proper table structure', () => {
      cy.visit('/schedule')
      
      // Check for proper table headers
      cy.get('[role="grid"]').within(() => {
        cy.get('[role="columnheader"]').should('have.length.greaterThan', 0)
        cy.get('[role="rowheader"]').should('have.length.greaterThan', 0)
      })
    })

    it('should support keyboard navigation in grid', () => {
      cy.visit('/schedule')
      
      // Focus on first cell
      cy.get('[role="gridcell"]').first().focus()
      
      // Test arrow key navigation
      cy.focused().type('{rightarrow}')
      cy.focused().should('have.attr', 'role', 'gridcell')
      
      cy.focused().type('{downarrow}')
      cy.focused().should('have.attr', 'role', 'gridcell')
    })
  })

  describe('Modal and Dialog Accessibility', () => {
    it('should trap focus in modals', () => {
      cy.visit('/dashboard')
      
      // Open a modal
      cy.get('[data-testid="open-modal"]').click()
      
      // Check that focus is trapped
      cy.get('[role="dialog"]').should('be.visible')
      cy.focused().should('be.within', '[role="dialog"]')
      
      // Tab through modal elements
      cy.focused().tab()
      cy.focused().should('be.within', '[role="dialog"]')
    })

    it('should return focus after modal closes', () => {
      cy.visit('/dashboard')
      
      // Focus on trigger button
      cy.get('[data-testid="open-modal"]').focus().click()
      
      // Close modal
      cy.get('[data-testid="close-modal"]').click()
      
      // Check that focus returns to trigger
      cy.focused().should('have.attr', 'data-testid', 'open-modal')
    })

    it('should have proper ARIA attributes for dialogs', () => {
      cy.visit('/dashboard')
      
      cy.get('[data-testid="open-modal"]').click()
      
      cy.get('[role="dialog"]').should('have.attr', 'aria-modal', 'true')
      cy.get('[role="dialog"]').should('have.attr', 'aria-labelledby')
      cy.get('[role="dialog"]').should('have.attr', 'aria-describedby')
    })
  })

  describe('Color and Contrast', () => {
    it('should have sufficient color contrast', () => {
      cy.visit('/dashboard')
      
      // Check color contrast (this would need a custom command)
      cy.checkColorContrast()
    })

    it('should not rely solely on color for information', () => {
      cy.visit('/dashboard')
      
      // Check that status indicators have text or icons
      cy.get('[data-testid="status-indicator"]').each(($el) => {
        cy.wrap($el).should('satisfy', (el) => {
          return el.textContent.trim() !== '' || 
                 el.querySelector('svg, img, [aria-label]')
        })
      })
    })
  })

  describe('Screen Reader Support', () => {
    it('should have proper heading structure', () => {
      cy.visit('/dashboard')
      
      // Check heading hierarchy
      cy.get('h1').should('have.length', 1)
      
      cy.get('h1, h2, h3, h4, h5, h6').then(($headings) => {
        const levels = Array.from($headings).map(h => parseInt(h.tagName.charAt(1)))
        
        // Check that headings don't skip levels
        for (let i = 1; i < levels.length; i++) {
          expect(levels[i] - levels[i-1]).to.be.at.most(1)
        }
      })
    })

    it('should have descriptive link text', () => {
      cy.visit('/dashboard')
      
      // Check that links have meaningful text
      cy.get('a').each(($link) => {
        cy.wrap($link).should('satisfy', (link) => {
          const text = link.textContent?.trim() || link.getAttribute('aria-label')
          return text && text !== 'click here' && text !== 'read more' && text.length > 2
        })
      })
    })

    it('should announce dynamic content changes', () => {
      cy.visit('/courses')
      
      // Trigger a dynamic update
      cy.get('[data-testid="filter-courses"]').select('Computer Science')
      
      // Check for live region announcement
      cy.get('[aria-live]').should('contain.text', 'results updated')
    })
  })

  describe('Reduced Motion Support', () => {
    it('should respect prefers-reduced-motion', () => {
      // Set reduced motion preference
      cy.window().then((win) => {
        Object.defineProperty(win, 'matchMedia', {
          writable: true,
          value: cy.stub().returns({
            matches: true,
            media: '(prefers-reduced-motion: reduce)',
            addEventListener: cy.stub(),
            removeEventListener: cy.stub()
          })
        })
      })
      
      cy.visit('/dashboard')
      
      // Check that animations are disabled or reduced
      cy.get('[data-testid="animated-element"]').should('have.css', 'animation-duration', '0s')
        .or('have.css', 'transition-duration', '0s')
    })
  })

  describe('Touch and Mobile Accessibility', () => {
    it('should have adequate touch targets on mobile', () => {
      cy.viewport('iphone-x')
      cy.visit('/dashboard')
      
      // Check that interactive elements are at least 44px
      cy.get('button, a, [role="button"]').each(($el) => {
        cy.wrap($el).then(($element) => {
          const rect = $element[0].getBoundingClientRect()
          expect(Math.min(rect.width, rect.height)).to.be.at.least(44)
        })
      })
    })

    it('should support swipe gestures where appropriate', () => {
      cy.viewport('iphone-x')
      cy.visit('/schedule')
      
      // Test swipe navigation (this would need custom commands)
      cy.get('[data-testid="timetable-grid"]').swipeLeft()
      cy.get('[data-testid="next-week"]').should('be.visible')
    })
  })
})

// Custom commands for accessibility testing
Cypress.Commands.add('checkColorContrast', () => {
  // Implementation would check WCAG color contrast ratios
  cy.get('*').each(($el) => {
    const element = $el[0]
    const styles = window.getComputedStyle(element)
    const color = styles.color
    const backgroundColor = styles.backgroundColor
    
    // Calculate contrast ratio and check against WCAG standards
    // This is a simplified version - real implementation would be more complex
    if (color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
      // Contrast ratio calculation would go here
      expect(true).to.be.true // Placeholder
    }
  })
})

Cypress.Commands.add('swipeLeft', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject)
    .trigger('touchstart', { touches: [{ clientX: 300, clientY: 100 }] })
    .trigger('touchmove', { touches: [{ clientX: 100, clientY: 100 }] })
    .trigger('touchend')
})
