describe('Dashboard', () => {
  beforeEach(() => {
    // Mock authentication
    cy.window().then((win) => {
      win.localStorage.setItem('auth_token', 'mock-token')
    })
    
    // Intercept API calls
    cy.intercept('GET', '/api/student/dashboard', {
      fixture: 'dashboard-data.json'
    }).as('getDashboardData')
    
    cy.intercept('GET', '/api/student/gpa-calculations', {
      fixture: 'gpa-calculations.json'
    }).as('getGPACalculations')
    
    cy.intercept('GET', '/api/student/academic-holds', {
      fixture: 'academic-holds.json'
    }).as('getAcademicHolds')
    
    cy.visit('/dashboard')
  })

  describe('Page Load', () => {
    it('should load dashboard successfully', () => {
      cy.wait('@getDashboardData')
      cy.get('[data-testid="dashboard-container"]').should('be.visible')
      cy.get('h1').should('contain.text', 'Dashboard')
    })

    it('should display loading state initially', () => {
      cy.get('[data-testid="loading-spinner"]').should('be.visible')
      cy.wait('@getDashboardData')
      cy.get('[data-testid="loading-spinner"]').should('not.exist')
    })

    it('should handle API errors gracefully', () => {
      cy.intercept('GET', '/api/student/dashboard', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('getDashboardError')
      
      cy.visit('/dashboard')
      cy.wait('@getDashboardError')
      
      cy.get('[data-testid="error-message"]').should('be.visible')
      cy.get('[data-testid="retry-button"]').should('be.visible')
    })
  })

  describe('Dashboard Components', () => {
    it('should display current semester information', () => {
      cy.wait('@getDashboardData')
      
      cy.get('[data-testid="current-semester"]').should('be.visible')
      cy.get('[data-testid="current-semester"]').should('contain.text', 'Spring 2024')
    })

    it('should display GPA information', () => {
      cy.wait('@getGPACalculations')
      
      cy.get('[data-testid="gpa-display"]').should('be.visible')
      cy.get('[data-testid="current-gpa"]').should('contain.text', '3.5')
    })

    it('should display credit progress', () => {
      cy.get('[data-testid="credit-progress"]').should('be.visible')
      cy.get('[data-testid="credits-completed"]').should('be.visible')
      cy.get('[data-testid="credits-remaining"]').should('be.visible')
    })

    it('should display academic holds when present', () => {
      cy.wait('@getAcademicHolds')
      
      cy.get('[data-testid="academic-holds"]').should('be.visible')
      cy.get('[data-testid="hold-card"]').should('have.length.greaterThan', 0)
    })

    it('should display upcoming deadlines', () => {
      cy.get('[data-testid="upcoming-deadlines"]').should('be.visible')
      cy.get('[data-testid="deadline-item"]').should('have.length.greaterThan', 0)
    })
  })

  describe('Navigation', () => {
    it('should navigate to timetable from quick action', () => {
      cy.wait('@getDashboardData')
      
      cy.get('[data-testid="quick-action-timetable"]').click()
      cy.url().should('include', '/schedule')
    })

    it('should navigate to grades from quick action', () => {
      cy.get('[data-testid="quick-action-grades"]').click()
      cy.url().should('include', '/grades')
    })

    it('should navigate to course registration from quick action', () => {
      cy.get('[data-testid="quick-action-courses"]').click()
      cy.url().should('include', '/courses')
    })
  })

  describe('Interactive Elements', () => {
    it('should acknowledge announcements', () => {
      cy.intercept('POST', '/api/student/announcements/*/acknowledge', {
        statusCode: 200,
        body: { success: true }
      }).as('acknowledgeAnnouncement')
      
      cy.wait('@getDashboardData')
      
      cy.get('[data-testid="announcement-item"]').first().within(() => {
        cy.get('[data-testid="acknowledge-button"]').click()
      })
      
      cy.wait('@acknowledgeAnnouncement')
      cy.get('[data-testid="announcement-item"]').first().should('have.class', 'acknowledged')
    })

    it('should expand/collapse hold details', () => {
      cy.wait('@getAcademicHolds')
      
      cy.get('[data-testid="hold-card"]').first().within(() => {
        cy.get('[data-testid="expand-button"]').click()
        cy.get('[data-testid="hold-details"]').should('be.visible')
        
        cy.get('[data-testid="expand-button"]').click()
        cy.get('[data-testid="hold-details"]').should('not.be.visible')
      })
    })
  })

  describe('Responsive Design', () => {
    it('should display correctly on mobile devices', () => {
      cy.viewport('iphone-x')
      cy.wait('@getDashboardData')
      
      cy.get('[data-testid="dashboard-container"]').should('be.visible')
      cy.get('[data-testid="mobile-nav"]').should('be.visible')
      cy.get('[data-testid="desktop-sidebar"]').should('not.be.visible')
    })

    it('should display correctly on tablet devices', () => {
      cy.viewport('ipad-2')
      cy.wait('@getDashboardData')
      
      cy.get('[data-testid="dashboard-container"]').should('be.visible')
      cy.get('[data-testid="sidebar"]').should('be.visible')
    })

    it('should display correctly on desktop', () => {
      cy.viewport(1280, 720)
      cy.wait('@getDashboardData')
      
      cy.get('[data-testid="dashboard-container"]').should('be.visible')
      cy.get('[data-testid="desktop-sidebar"]').should('be.visible')
      cy.get('[data-testid="mobile-nav"]').should('not.be.visible')
    })
  })

  describe('Accessibility', () => {
    it('should have proper heading hierarchy', () => {
      cy.wait('@getDashboardData')
      
      cy.get('h1').should('have.length', 1)
      cy.get('h2').should('have.length.greaterThan', 0)
      
      // Check heading order
      cy.get('h1, h2, h3, h4, h5, h6').then(($headings) => {
        const headingLevels = Array.from($headings).map(h => parseInt(h.tagName.charAt(1)))
        
        for (let i = 1; i < headingLevels.length; i++) {
          expect(headingLevels[i] - headingLevels[i-1]).to.be.at.most(1)
        }
      })
    })

    it('should have proper ARIA labels', () => {
      cy.wait('@getDashboardData')
      
      cy.get('[data-testid="gpa-display"]').should('have.attr', 'aria-label')
      cy.get('[data-testid="credit-progress"]').should('have.attr', 'aria-label')
      cy.get('[data-testid="quick-actions"]').should('have.attr', 'aria-label')
    })

    it('should be keyboard navigable', () => {
      cy.wait('@getDashboardData')
      
      // Tab through interactive elements
      cy.get('body').tab()
      cy.focused().should('be.visible')
      
      // Test quick actions keyboard navigation
      cy.get('[data-testid="quick-action-timetable"]').focus()
      cy.focused().type('{enter}')
      cy.url().should('include', '/schedule')
    })

    it('should have sufficient color contrast', () => {
      cy.wait('@getDashboardData')
      
      // This would typically use a custom command to check contrast ratios
      cy.get('[data-testid="dashboard-container"]').should('be.visible')
      
      // Check that text is readable
      cy.get('h1').should('have.css', 'color').and('not.equal', 'rgba(0, 0, 0, 0)')
      cy.get('p').should('have.css', 'color').and('not.equal', 'rgba(0, 0, 0, 0)')
    })
  })

  describe('Performance', () => {
    it('should load within acceptable time limits', () => {
      const start = Date.now()
      
      cy.visit('/dashboard')
      cy.wait('@getDashboardData')
      
      cy.then(() => {
        const loadTime = Date.now() - start
        expect(loadTime).to.be.lessThan(3000) // 3 seconds
      })
    })

    it('should not have memory leaks', () => {
      // Visit dashboard multiple times to check for memory leaks
      for (let i = 0; i < 5; i++) {
        cy.visit('/dashboard')
        cy.wait('@getDashboardData')
        cy.go('back')
      }
      
      // Check that performance is still good
      cy.visit('/dashboard')
      cy.wait('@getDashboardData')
      cy.get('[data-testid="dashboard-container"]').should('be.visible')
    })
  })
})
