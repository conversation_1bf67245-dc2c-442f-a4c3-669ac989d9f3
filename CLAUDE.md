# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Primary Commands

- `pnpm dev` - Start development server
- `pnpm build` - Build for production (runs type-check and build-only in parallel)
- `pnpm type-check` - Run Vue TypeScript compiler
- `pnpm test:unit` - Run unit tests with Vitest
- `pnpm test:e2e` - Run end-to-end tests with Cypress (production build)
- `pnpm test:e2e:dev` - Run e2e tests against dev server (faster)

### Code Quality Commands

- `pnpm lint` - Run all linters (oxlint + eslint)
- `pnpm lint:oxlint` - Run oxlint with auto-fix
- `pnpm lint:eslint` - Run ESLint with auto-fix
- `pnpm format` - Format code with Prettier

### Single Test Execution

- `pnpm test:unit src/path/to/test.test.ts` - Run specific unit test
- `pnpm test:unit --watch` - Run unit tests in watch mode

## Project Architecture

### Tech Stack

- **Frontend**: Vue 3 with Composition API and TypeScript
- **State Management**: Pinia stores with composables pattern
- **Routing**: Vue Router 4 with route guards and lazy loading
- **Styling**: Tailwind CSS 4 with Shadcn/UI components via Reka UI
- **API Layer**: Custom `useApi` composable with automatic token refresh
- **Build Tool**: Vite with Vue DevTools integration
- **Testing**: Vitest (unit) + Cypress (e2e)
- **Package Manager**: pnpm

### Application Structure

#### Core Architecture Pattern

- **Role-based routing**: Dual portal architecture with student and lecturer routes
- **Layout-first routing**: Role-specific layouts (`StudentLayout`/`LecturerLayout`) wrap authenticated routes
- **Composables-based logic**: Business logic extracted into reusable composables
- **Store-per-domain**: Separate Pinia stores per role and domain (auth, course, dashboard, notification, lecturer-specific stores)
- **Type-safe API**: TypeScript interfaces for all API responses and requests

#### Directory Structure

**Role-based Architecture with Clear Separation:**

```
src/
├── shared/              # Shared resources across roles
│   ├── components/      # Reusable UI components
│   │   ├── ui/          # Base UI components (Reka UI extensions)
│   │   └── icons/       # Shared icon components
│   ├── composables/     # Shared business logic
│   │   ├── useBaseApi.ts      # Core API functionality
│   │   └── useNotifications.ts # Notification management
│   ├── stores/          # Shared Pinia stores
│   │   ├── auth.ts      # Authentication and user management
│   │   └── notification.ts    # User notifications
│   ├── types/           # Shared TypeScript definitions
│   │   ├── api/         # API request/response types
│   │   │   ├── api.ts         # Core API interfaces
│   │   │   ├── course.ts      # Course API types
│   │   │   └── notification.ts # Notification API types
│   │   └── models/      # Domain model types
│   │       ├── student.ts     # Student domain models
│   │       ├── lecturer.ts    # Lecturer domain models
│   │       ├── attendance.ts  # Attendance models
│   │       └── notification.ts # Notification models
│   └── views/           # Shared views (login, 404, etc.)
├── student/             # Student portal resources
│   ├── components/      # Student-specific components
│   │   ├── dashboard/   # Student dashboard components
│   │   └── layout/      # Student layout components
│   ├── composables/     # Student-specific logic
│   │   └── useStudentApi.ts   # Student API hooks
│   ├── stores/          # Student Pinia stores
│   │   ├── course.ts    # Student course data
│   │   └── dashboard.ts # Student dashboard data
│   ├── types/           # Student-specific types
│   │   ├── api/         # Student API types
│   │   └── models/      # Student domain models
│   └── views/           # Student portal views
├── lecturer/            # Lecturer portal resources
│   ├── components/      # Lecturer-specific components
│   │   └── layout/      # Lecturer layout components
│   ├── composables/     # Lecturer-specific logic
│   │   └── useLecturerApi.ts  # Lecturer API hooks
│   ├── stores/          # Lecturer Pinia stores
│   │   ├── dashboard.ts # Lecturer dashboard data
│   │   ├── courses.ts   # Lecturer course management
│   │   ├── attendance.ts # Lecturer attendance tracking
│   │   └── students.ts  # Student management
│   ├── types/           # Lecturer-specific types
│   │   ├── api/         # Lecturer API types
│   │   └── models/      # Lecturer domain models
│   └── views/           # Lecturer portal views
└── router/              # Vue Router configuration
    ├── index.ts         # Main router configuration
    ├── student.routes.ts # Student route definitions
    └── lecturer.routes.ts # Lecturer route definitions
```

### Authentication & API Integration

#### Authentication Flow

- Google OAuth integration via `vue3-google-signin`
- JWT token storage in localStorage with automatic refresh
- Route guards in `router/index.ts` protect authenticated routes
- Token refresh logic in `useApi` composable handles 401 errors automatically

#### API Layer (Role-based API Architecture)

**Base API (`useBaseApi` composable):**
- Built on VueUse's `createFetch` with enhanced error handling
- Automatic bearer token injection and refresh on 401 errors
- Exponential backoff retry logic with jitter
- Global loading state management
- Foundation for role-specific API hooks

**Role-specific API Hooks:**
- `useStudentApi()` - Student portal API endpoints and methods
- `useLecturerApi()` - Lecturer portal API endpoints and methods
- Type-safe endpoint definitions with role-appropriate data structures
- Automatic role-based request routing and response handling

### State Management Patterns

#### Pinia Store Structure

**Shared Stores (`/shared/stores/`):**
- **auth.ts**: User authentication, role management, and profile data for both students and lecturers
- **notification.ts**: User notifications and alerts across all portals

**Student Stores (`/student/stores/`):**
- **course.ts**: Course enrollment, registration, and academic data
- **dashboard.ts**: Student dashboard metrics, summaries, and overview data

**Lecturer Stores (`/lecturer/stores/`):**
- **dashboard.ts**: Lecturer dashboard data, teaching summary, and student alerts
- **courses.ts**: Course offerings, class sessions, and course materials management
- **attendance.ts**: Attendance tracking, marking, and analytics
- **students.ts**: Student management, monitoring, and academic oversight

#### Composables Pattern

- Extract business logic into composables (e.g., `useNotifications`)
- Reactive state management with Vue 3 reactivity
- Reusable across components with consistent interfaces

### Component Architecture

#### Layout System

**Role-based Layouts:**
- `StudentLayout` wraps all student portal routes with `StudentSidebar` and `StudentMobileNav`
- `LecturerLayout` wraps all lecturer portal routes with `LecturerSidebar` and `LecturerMobileNav`
- Role-specific navigation with active route detection
- Responsive design with mobile-optimized navigation
- Quick access tools and role-appropriate header actions

#### UI Components

- Reka UI as base component library
- Tailwind CSS for styling with utility-first approach
- Lucide icons for consistent iconography
- Card-based layouts for information display

### Route Structure

#### Authentication Routes

- `/login` - Login page (guest only, supports both student and lecturer authentication)
- All other routes require authentication and role-based access

#### Student Portal Routes (`/student/*`)

- `/student/dashboard` - Student dashboard with academic overview
- `/student/courses` - Course catalog and enrollment
- `/student/schedule` - Class schedule and calendar
- `/student/assessment` - Assessment and assignment tracking
- `/student/attendance` - Personal attendance records
- `/student/grades/trend` - GPA trends and analysis
- `/student/profile` - Student profile and study plan
- `/student/notifications` - System notifications
- `/student/holds` - Academic holds and registration blocks
- `/student/curriculum` - Curriculum requirements and progress

#### Lecturer Portal Routes (`/lecturer/*`)

- `/lecturer/dashboard` - Lecturer dashboard with teaching overview
- `/lecturer/teaching/courses` - Course management and offerings
- `/lecturer/teaching/timetable` - Teaching schedule and class sessions
- `/lecturer/attendance` - Attendance marking and tracking
- `/lecturer/students` - Student management and monitoring
- `/lecturer/feedback` - Student feedback and communication
- `/lecturer/notifications` - System notifications and messaging
- `/lecturer/profile` - Lecturer profile and information
- `/lecturer/settings` - Portal settings and preferences

#### Route Guards and Role Management

- **Role-based access control**: Routes enforce specific role requirements
- **Automatic role detection**: Root path redirects based on user role
- **Legacy route support**: Backward compatibility with role-based redirects
- **Enhanced navigation guards**: Authentication and authorization validation

### Development Standards

#### Code Quality

- ESLint with Vue 3 + TypeScript configuration
- Prettier with 100-character line length, single quotes, no semicolons
- EditorConfig for consistent formatting (2-space indentation)
- Oxlint for performance-focused linting

#### TypeScript Configuration

- Strict mode enabled with Node.js 22 configuration
- Path aliases: `@/*` maps to `./src/*`
- Separate configs for app, node, and test environments

#### Testing Strategy

- Unit tests with Vitest for composables and utilities
- E2E tests with Cypress for critical user flows
- Test files use `.test.ts` suffix
- Test coverage for API integration and state management

## Dual Portal Architecture

### Role-based Portal System

- **Student Portal**: Comprehensive academic management for enrolled students
- **Lecturer Portal**: Teaching and course management tools for academic staff
- **Shared Authentication**: Single sign-on with role-based routing and permissions
- **Consistent UI/UX**: Unified design system across both portals with role-appropriate features

### Role Management

- **Dynamic Role Detection**: Authentication determines user role (student/lecturer)
- **Role-specific Routes**: Separate route trees for each portal with appropriate guards
- **Context-aware Navigation**: Portal-specific sidebars and navigation patterns
- **Permission-based Access**: Fine-grained access control based on user role and permissions

### Data Architecture

- **Role-specific Types**: Separate TypeScript interfaces organized in role-based directories
- **Specialized Stores**: Domain-specific Pinia stores for each role's data management
- **API Segregation**: Role-specific API hooks with type-safe endpoint definitions
- **Cross-role Integration**: Shared data models for common entities in `/shared/types/models/`
- **Hierarchical Type Organization**: Clear separation between API types and domain models

### Architecture Benefits

**Scalability:**
- Easy to add new roles or extend existing functionality
- Clear boundaries prevent feature overlap
- Modular structure supports independent development

**Maintainability:**
- Role-specific code organization reduces complexity
- Clear import paths with `@/shared/`, `@/student/`, `@/lecturer/` prefixes
- Consistent patterns across all role-based modules

**Type Safety:**
- Comprehensive TypeScript coverage with role-specific types
- API types separated from domain models for better organization
- Shared types prevent duplication while maintaining specificity

**Developer Experience:**
- Intuitive file structure that matches mental models
- IDE auto-completion works seamlessly with organized imports
- Clear separation of concerns reduces cognitive load

## Key Design Principles

### Mobile-First Approach

- Responsive design targeting ~70% mobile usage
- PWA capabilities with offline support
- Tailwind CSS breakpoints for progressive enhancement

### User Experience

- Three-tier information hierarchy: Overview → List → Detail/Action
- Loading states: Skeletons ≤300ms, spinners for >2s operations
- Color coding: Blue/Indigo = Info, Yellow = Warnings, Red = Alerts
- Always provide user feedback for actions

### Performance Optimization

- Lazy-loaded routes with dynamic imports
- Efficient API caching and retry logic
- Optimized bundle splitting with Vite

### Accessibility

- WCAG 2.1 AA compliance target
- Semantic HTML structure
- Keyboard navigation support
- Screen reader compatibility

## Common Development Patterns

### API Integration

**Role-specific API Structure:**
```typescript
// Base API for shared functionality
const baseApi = useBaseApi()
const user = await baseApi.get('/api/auth/me')
const notifications = await baseApi.get('/api/notifications')

// Student API methods
const studentApi = useStudentApi()
const dashboardData = await studentApi.dashboard.getData()
const courses = await studentApi.courses.getRegistered()
const grades = await studentApi.grades.getTrend()
const holds = await studentApi.holds.getActive()

// Lecturer API methods  
const lecturerApi = useLecturerApi()
const lecturerDashboard = await lecturerApi.dashboard.getData()
const courseOfferings = await lecturerApi.courses.getOfferings()
const attendanceData = await lecturerApi.attendance.getSessions()
const studentList = await lecturerApi.students.getEnrolled()
```

### State Management

**Role-based State Management:**
```typescript
// Shared stores
const authStore = useAuthStore() // from @/shared/stores/auth
const notificationStore = useNotificationStore() // from @/shared/stores/notification
const { user, isAuthenticated, isStudent, isLecturer, role } = storeToRefs(authStore)

// Student-specific stores (imported from @/student/stores/)
const studentDashboard = useDashboardStore() // from @/student/stores/dashboard
const courseStore = useCourseStore() // from @/student/stores/course

// Lecturer-specific stores (imported from @/lecturer/stores/)
const lecturerDashboard = useDashboardStore() // from @/lecturer/stores/dashboard
const lecturerCourses = useCoursesStore() // from @/lecturer/stores/courses
const lecturerAttendance = useAttendanceStore() // from @/lecturer/stores/attendance
const lecturerStudents = useStudentsStore() // from @/lecturer/stores/students
```

### Component Development

- Use Composition API with `<script setup>`
- Extract complex logic into composables
- Follow existing component patterns in the codebase
- Use TypeScript interfaces for props and emits

### Environment Configuration

- API base URL: `VITE_API_BASE_URL`
- Development server runs on default Vite port
- Production builds optimized for modern browsers
