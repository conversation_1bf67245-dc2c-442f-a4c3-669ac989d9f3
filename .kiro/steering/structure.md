# Project Structure

## Root Directory Organization

```
├── src/                    # Main application source
├── public/                 # Static assets
├── prd/                    # Product requirements documents
├── cypress/                # E2E test files
├── .cursor/rules/          # Cursor AI coding rules
├── .kiro/                  # Kiro AI configuration
└── node_modules/           # Dependencies
```

## Source Code Architecture (`src/`)

### Role-Based Module Structure

The application follows a role-based architecture with shared components:

```
src/
├── shared/                 # Cross-role shared code
│   ├── components/         # Reusable UI components
│   ├── composables/        # Shared composables (useBaseApi, useNotifications)
│   ├── stores/             # Shared Pinia stores (auth, notification)
│   ├── types/              # Shared TypeScript types
│   └── views/              # Shared views (Login, NotFound)
├── student/                # Student-specific code
│   ├── components/         # Student UI components
│   ├── composables/        # Student composables (useStudentApi)
│   ├── stores/             # Student Pinia stores
│   ├── types/              # Student TypeScript types
│   └── views/              # Student views/pages
├── lecturer/               # Lecturer-specific code
│   ├── components/         # Lecturer UI components
│   ├── composables/        # Lecturer composables (useLecturerApi)
│   ├── stores/             # Lecturer Pi<PERSON> stores
│   ├── types/              # Lecturer TypeScript types
│   └── views/              # Lecturer views/pages
├── router/                 # Vue Router configuration
├── lib/                    # Utility functions
├── assets/                 # Images, fonts, etc.
├── App.vue                 # Root component
└── main.ts                 # Application entry point
```

## Key Architectural Patterns

### Component Organization

- **UI Components**: Located in `src/shared/components/ui/` following shadcn/vue structure
- **Feature Components**: Role-specific components in respective module folders
- **Layout Components**: Separate layouts for student/lecturer roles

### Store Structure

- **Shared Stores**: Authentication, notifications in `src/shared/stores/`
- **Role Stores**: Domain-specific stores in respective role folders
- **Store Index**: Each role has an `index.ts` for store exports

### Type Definitions

- **API Types**: Separate folders for API-related types
- **Model Types**: Domain model definitions
- **Shared Types**: Cross-cutting type definitions

### Composables Pattern

- **Base API**: `useBaseApi` for common HTTP operations
- **Role APIs**: `useStudentApi`, `useLecturerApi` for role-specific endpoints
- **Feature Composables**: Domain-specific logic encapsulation

## File Naming Conventions

- **Components**: PascalCase (e.g., `StudentDashboard.vue`)
- **Composables**: camelCase with `use` prefix (e.g., `useStudentApi.ts`)
- **Stores**: camelCase (e.g., `dashboard.ts`)
- **Types**: camelCase (e.g., `student.ts`)
- **Views**: PascalCase with `View` suffix (e.g., `DashboardView.vue`)

## Import Aliases

- `@/` maps to `src/` directory for clean imports
- Use absolute imports with alias for better maintainability

## Configuration Files

- **TypeScript**: Multiple tsconfig files for different contexts
- **Vite**: Single config with Vue, JSX, and Tailwind plugins
- **ESLint**: Modern flat config with Vue and TypeScript rules
- **Components**: shadcn/vue configuration in `components.json`
