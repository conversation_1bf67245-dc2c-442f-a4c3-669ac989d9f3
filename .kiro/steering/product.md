# Product Overview

Portal SwinX is a unified student and lecturer portal built as a single Vue 3 application with role-based access control.

## Core Purpose

- **Student Portal**: Academic management including course registration, grades, attendance, timetables, and notifications
- **Lecturer Portal**: Teaching management including course administration, grading, attendance tracking, and student communication

## Key Features

- Role-based routing and layouts (student vs lecturer)
- Real-time academic data management
- Mobile-first responsive design
- Progressive Web App (PWA) capabilities
- Integrated authentication with automatic token refresh
- Multi-channel notifications (in-app + email)

## User Goals

- **Students**: "Know exactly where I am and what I should do next" with self-service capabilities
- **Lecturers**: Efficient course management, grading, and student communication tools

## Design Principles

- 3-level information hierarchy: Overview (Dashboard) → List View → Detail/Action
- Mobile-first approach (~70% mobile access)
- WCAG 2.1 AA accessibility compliance
- Consistent feedback states and loading indicators
- Color-coded status indicators (Blue=Info, Yellow=Warning, Red=Alert)
