# Technology Stack

## Core Framework

- **Vue 3** with Composition API and TypeScript
- **Vite** as build tool and development server
- **Vue Router 4** for client-side routing
- **Pinia** for state management

## UI & Styling

- **Tailwind CSS 4** for utility-first styling
- **Reka UI** for headless component primitives
- **Lucide Vue Next** for icons
- **shadcn/vue** component system (via components.json)
- **class-variance-authority** for component variants

## Development Tools

- **TypeScript** with strict type checking
- **ESLint** + **Prettier** for code formatting
- **Oxlint** for fast linting
- **Vue DevTools** for debugging

## Testing

- **Vitest** for unit testing
- **Cypress** for end-to-end testing
- **@vue/test-utils** for Vue component testing

## Package Management

- **pnpm** as package manager (version 9.15.3+)

## Common Commands

### Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview
```

### Testing

```bash
# Run unit tests
pnpm test:unit

# Run E2E tests (development)
pnpm test:e2e:dev

# Run E2E tests (production)
pnpm test:e2e
```

### Code Quality

```bash
# Run all linters
pnpm lint

# Format code
pnpm format

# Type checking
pnpm type-check
```

## API Integration

- Custom `useApi` composable built on VueUse's `createFetch`
- Automatic Bearer token authentication
- Smart token refresh on 401 errors
- Consistent API response format with success/error handling
