# Design Document

## Overview

The Student Portal is a comprehensive Vue 3 application built with TypeScript, utilizing a modern tech stack including Pinia for state management, Tailwind CSS for styling, and Reka UI components. The application follows a mobile-first, progressive web app (PWA) approach with offline capabilities and accessibility compliance.

The architecture emphasizes modularity, type safety, and user experience optimization through responsive design, optimistic UI patterns, and comprehensive error handling.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Vue 3 SPA] --> B[Vue Router]
        A --> C[Pinia Stores]
        A --> D[Composables]
        A --> E[UI Components]
    end

    subgraph "State Management"
        C --> F[Auth Store]
        C --> G[Dashboard Store]
        C --> H[Course Store]
        C --> I[Notification Store]
    end

    subgraph "API Layer"
        D --> J[useApi Composable]
        J --> K[HTTP Client]
        K --> L[Backend API]
    end

    subgraph "UI Layer"
        E --> M[Layout Components]
        E --> N[Feature Components]
        E --> O[Shared Components]
        E --> P[Reka UI Components]
    end

    subgraph "PWA Features"
        Q[Service Worker]
        R[Offline Storage]
        S[Push Notifications]
    end

    A --> Q
    C --> R
    I --> S
```

### Technology Stack

- **Frontend Framework**: Vue 3 with Composition API
- **Type System**: TypeScript for type safety
- **State Management**: Pinia for reactive state management
- **Routing**: Vue Router 4 with route guards
- **Styling**: Tailwind CSS 4 with custom design system
- **UI Components**: Reka UI + custom components
- **HTTP Client**: VueUse createFetch with automatic token refresh
- **Testing**: Vitest for unit tests, Cypress for E2E
- **Build Tool**: Vite for fast development and optimized builds
- **PWA**: Vite PWA plugin for offline capabilities

## Components and Interfaces

### Core Data Models

```typescript
// Student Profile Models (aligned with database schema)
interface Student {
  id: string
  student_id: string
  full_name: string
  email: string
  program_id: string
  specialization_id?: string
  campus_id: string
  curriculum_version_id: string
  program: Program
  specialization?: Specialization
  campus: Campus
  curriculum_version: CurriculumVersion
}

// Academic Program Models
interface Program {
  id: string
  name: string
  code: string
  description: string
}

interface Specialization {
  id: string
  program_id: string
  name: string
  code: string
  program: Program
}

interface CurriculumVersion {
  id: string
  program_id: string
  specialization_id?: string
  semester_id: string
  program: Program
  specialization?: Specialization
  semester: Semester
}

// Course & Unit Models
interface Unit {
  id: string
  code: string
  name: string
  credit_points: number
}

interface CurriculumUnit {
  id: string
  curriculum_version_id: string
  unit_id: string
  semester_id: string
  curriculum_version: CurriculumVersion
  unit: Unit
  semester: Semester
}

interface CourseOffering {
  id: string
  curriculum_unit_id: string
  semester_id: string
  lecture_id: string
  curriculum_unit: CurriculumUnit
  semester: Semester
  lecturer: Lecturer
  class_sessions: ClassSession[]
}

interface ClassSession {
  id: string
  course_offering_id: string
  instructor_id: string
  room_id: string
  room_booking_id: string
  course_offering: CourseOffering
  instructor: Lecturer
  room: Room
}

// Registration & Academic Records
interface CourseRegistration {
  id: string
  student_id: string
  course_offering_id: string
  semester_id: string
  student: Student
  course_offering: CourseOffering
  semester: Semester
}

interface AcademicRecord {
  id: string
  student_id: string
  course_offering_id: string
  semester_id: string
  unit_id: string
  student: Student
  course_offering: CourseOffering
  semester: Semester
  unit: Unit
}

interface Enrollment {
  id: string
  student_id: string
  semester_id: string
  curriculum_version_id: string
  student: Student
  semester: Semester
  curriculum_version: CurriculumVersion
}

// Assessment Models
interface Syllabus {
  id: string
  curriculum_unit_id: string
  curriculum_unit: CurriculumUnit
  assessment_components: AssessmentComponent[]
}

interface AssessmentComponent {
  id: string
  syllabus_id: string
  syllabus: Syllabus
  assessment_component_details: AssessmentComponentDetail[]
}

interface AssessmentComponentDetail {
  id: string
  component_id: string
  assessment_component: AssessmentComponent
  assessment_scores: AssessmentScore[]
}

interface AssessmentScore {
  id: string
  assessment_component_detail_id: string
  student_id: string
  course_offering_id: string
  assessment_component_detail: AssessmentComponentDetail
  student: Student
  course_offering: CourseOffering
}

// Attendance Models
interface Attendance {
  id: string
  class_session_id: string
  student_id: string
  class_session: ClassSession
  student: Student
}

// Academic Support Models
interface AcademicHold {
  id: string
  student_id: string
  placed_by_user_id: string
  student: Student
  placed_by: User
}

interface ProgramChangeRequest {
  id: string
  student_id: string
  from_program_id: string
  to_program_id: string
  student: Student
  from_program: Program
  to_program: Program
}

// Facilities Models
interface Campus {
  id: string
  name: string
  code: string
  address: string
  buildings: Building[]
}

interface Building {
  id: string
  campus_id: string
  name: string
  code: string
  campus: Campus
}

interface Room {
  id: string
  campus_id: string
  name: string
  code: string
  campus: Campus
}

// Staff Models
interface Lecturer {
  id: string
  employee_id: string
  email: string
  campus_id: string
  campus: Campus
}

// System Models
interface Semester {
  id: string
  name: string
  code: string
}

interface User {
  id: string
  name: string
  email: string
}

interface Role {
  id: string
  name: string
  code: string
}

interface Permission {
  id: string
  name: string
  code: string
}

// Academic Standing & GPA
interface AcademicStanding {
  id: string
  student_id: string
  semester_id: string
  student: Student
  semester: Semester
}

interface GPACalculation {
  id: string
  student_id: string
  semester_id: string
  program_id: string
  student: Student
  semester: Semester
  program: Program
}
```

### Store Architecture

```typescript
// Dashboard Store (updated for database schema)
export const useDashboardStore = defineStore('dashboard', () => {
  const currentSemester = ref<Semester | null>(null)
  const creditProgress = ref<{
    earned_credits: number
    required_credits: number
    remaining_requirements: CurriculumUnit[]
    completion_percentage: number
  } | null>(null)
  const currentGPA = ref<GPACalculation | null>(null)
  const academicHolds = ref<AcademicHold[]>([])
  const upcomingAssessments = ref<AssessmentComponentDetail[]>([])
  const enrollmentStatus = ref<Enrollment | null>(null)
  const academicStanding = ref<AcademicStanding | null>(null)

  const fetchDashboardData = async () => {
    const { data } = await useApi().get<DashboardResponse>('/dashboard')

    currentSemester.value = data.current_semester
    creditProgress.value = data.credit_progress
    currentGPA.value = data.current_gpa
    academicHolds.value = data.academic_holds
    upcomingAssessments.value = data.upcoming_assessments
    enrollmentStatus.value = data.enrollment_status
    academicStanding.value = data.academic_standing
  }

  const refreshData = async () => {
    await fetchDashboardData()
  }

  return {
    currentSemester,
    creditProgress,
    currentGPA,
    academicHolds,
    upcomingAssessments,
    enrollmentStatus,
    academicStanding,
    fetchDashboardData,
    refreshData,
  }
})

// Course Registration Store (updated for database schema)
export const useCourseStore = defineStore('course', () => {
  const availableOfferings = ref<CourseOffering[]>([])
  const registeredOfferings = ref<CourseRegistration[]>([])
  const selectedOfferings = ref<CourseOffering[]>([])
  const registrationStatus = ref<RegistrationStatus>('idle')
  const registrationPeriod = ref<{
    start_date: string
    end_date: string
    is_open: boolean
  } | null>(null)

  const addCourseOffering = async (offering: CourseOffering) => {
    // Optimistic UI update
    selectedOfferings.value.push(offering)

    try {
      await useApi().post('/course-registrations', {
        course_offering_id: offering.id,
        semester_id: offering.semester_id,
      })
    } catch (error) {
      // Rollback on error
      selectedOfferings.value = selectedOfferings.value.filter((o) => o.id !== offering.id)
      throw error
    }
  }

  const fetchAvailableOfferings = async (semesterId: string) => {
    const { data } = await useApi().get<CourseRegistrationResponse>(
      `/course-offerings/available/${semesterId}`,
    )

    availableOfferings.value = data.available_offerings
    registeredOfferings.value = data.registered_offerings
    registrationPeriod.value = data.registration_period
  }

  return {
    availableOfferings,
    registeredOfferings,
    selectedOfferings,
    registrationStatus,
    registrationPeriod,
    addCourseOffering,
    fetchAvailableOfferings,
  }
})

// Timetable Store
export const useTimetableStore = defineStore('timetable', () => {
  const classSessions = ref<ClassSession[]>([])
  const currentSemester = ref<Semester | null>(null)
  const weekRange = ref<{ start_date: string; end_date: string } | null>(null)

  const fetchTimetable = async (semesterId: string, weekStart?: string) => {
    const params = new URLSearchParams({ semester_id: semesterId })
    if (weekStart) params.append('week_start', weekStart)

    const { data } = await useApi().get<TimetableResponse>(`/timetable?${params}`)

    classSessions.value = data.class_sessions
    currentSemester.value = data.semester
    weekRange.value = data.week_range
  }

  return {
    classSessions,
    currentSemester,
    weekRange,
    fetchTimetable,
  }
})

// Grades Store
export const useGradesStore = defineStore('grades', () => {
  const academicRecords = ref<AcademicRecord[]>([])
  const assessmentScores = ref<AssessmentScore[]>([])
  const gpaCalculations = ref<GPACalculation[]>([])
  const semesterSummaries = ref<
    {
      semester: Semester
      total_credits: number
      semester_gpa: number
      units_completed: number
    }[]
  >([])

  const fetchGrades = async () => {
    const { data } = await useApi().get<GradesResponse>('/grades')

    academicRecords.value = data.academic_records
    assessmentScores.value = data.assessment_scores
    gpaCalculations.value = data.gpa_calculations
    semesterSummaries.value = data.semester_summaries
  }

  return {
    academicRecords,
    assessmentScores,
    gpaCalculations,
    semesterSummaries,
    fetchGrades,
  }
})

// Attendance Store
export const useAttendanceStore = defineStore('attendance', () => {
  const attendanceRecords = ref<Attendance[]>([])
  const attendanceSummary = ref<
    {
      course_offering: CourseOffering
      total_sessions: number
      attended_sessions: number
      attendance_percentage: number
      minimum_required: number
      status: 'good' | 'warning' | 'critical'
    }[]
  >([])

  const fetchAttendance = async (semesterId?: string) => {
    const params = semesterId ? `?semester_id=${semesterId}` : ''
    const { data } = await useApi().get<AttendanceResponse>(`/attendance${params}`)

    attendanceRecords.value = data.attendance_records
    attendanceSummary.value = data.attendance_summary
  }

  return {
    attendanceRecords,
    attendanceSummary,
    fetchAttendance,
  }
})
```

### Component Structure

```
src/components/
├── layout/
│   ├── AppLayout.vue           # Main layout wrapper
│   ├── Sidebar.vue             # Desktop sidebar navigation
│   ├── MobileNav.vue           # Mobile bottom navigation
│   └── Header.vue              # Top header with user menu
├── dashboard/
│   ├── DashboardOverview.vue   # Main dashboard container
│   ├── SemesterCard.vue        # Current semester display
│   ├── CreditProgress.vue      # Progress ring component
│   ├── GPADisplay.vue          # GPA with color coding
│   └── HoldsAlert.vue          # Academic holds display
├── courses/
│   ├── CourseList.vue          # Available courses list
│   ├── CourseCard.vue          # Individual course card
│   ├── CourseFilters.vue       # Filter controls
│   ├── RegistrationCart.vue    # Selected courses panel
│   └── ConflictChecker.vue     # Time conflict detection
├── timetable/
│   ├── WeeklyView.vue          # Main timetable view
│   ├── TimeSlot.vue            # Individual time slot
│   ├── SessionDetails.vue      # Session detail modal
│   └── TimetableFilters.vue    # Class type filters
├── grades/
│   ├── TranscriptView.vue      # Grades transcript
│   ├── GPATrend.vue            # GPA trend chart
│   └── GradeCard.vue           # Individual grade display
├── shared/
│   ├── LoadingSpinner.vue      # Loading states
│   ├── EmptyState.vue          # Empty state illustrations
│   ├── ErrorBoundary.vue       # Error handling
│   ├── Toast.vue               # Notification toasts
│   └── ConfirmModal.vue        # Confirmation dialogs
└── ui/                         # Reka UI extensions
    ├── Button.vue
    ├── Card.vue
    ├── Modal.vue
    └── Form/
```

### Composables Architecture

```typescript
// useApi.ts - Enhanced API composable
export const useApi = () => {
  const { token, logout, refreshToken } = useAuthStore()

  const apiCall = async <T>(endpoint: string, options: RequestInit = {}) => {
    // Token refresh logic, error handling, retry mechanism
  }

  return { get, post, put, delete: del }
}

// useDashboard.ts - Dashboard data management
export const useDashboard = () => {
  const store = useDashboardStore()
  const { get } = useApi()

  const loadDashboard = async () => {
    const [semester, progress, gpa, holds] = await Promise.all([
      get('/academic/current-semester'),
      get('/academic/credit-progress'),
      get('/academic/gpa'),
      get('/academic/holds'),
    ])

    // Update store with fetched data
  }

  return { loadDashboard, ...toRefs(store) }
}

// useOffline.ts - PWA offline capabilities
export const useOffline = () => {
  const isOnline = useOnline()
  const storage = useLocalStorage('offline-data', {})

  const cacheData = (key: string, data: any) => {
    storage.value[key] = { data, timestamp: Date.now() }
  }

  const getCachedData = (key: string, maxAge = 7 * 24 * 60 * 60 * 1000) => {
    const cached = storage.value[key]
    if (cached && Date.now() - cached.timestamp < maxAge) {
      return cached.data
    }
    return null
  }

  return { isOnline, cacheData, getCachedData }
}

// useNotifications.ts - Push notification management
export const useNotifications = () => {
  const permission = usePermission('notifications')
  const store = useNotificationStore()

  const requestPermission = async () => {
    if (permission.value === 'prompt') {
      await Notification.requestPermission()
    }
  }

  const showNotification = (title: string, options?: NotificationOptions) => {
    if (permission.value === 'granted') {
      new Notification(title, options)
    }
  }

  return { requestPermission, showNotification, ...toRefs(store) }
}
```

## Data Models

### Database Schema Design

The student portal integrates with a comprehensive academic management database structure that includes the following key entities:

#### Academic Program Structure

- **programs**: Academic programs with specializations, curriculum versions, and graduation requirements
- **specializations**: Program specializations linked to specific curriculum versions
- **curriculum_versions**: Version-controlled curriculum tied to specific semesters and programs
- **curriculum_units**: Individual units within curriculum versions, linked to specific semesters
- **units**: Course units with credit points and prerequisite relationships

#### Student Management

- **students**: Core student information including program enrollment, campus assignment, and curriculum version
- **enrollments**: Student enrollment records per semester with curriculum version tracking
- **academic_standings**: Semester-based academic standing records
- **gpa_calculations**: GPA calculations per semester and program

#### Course Delivery & Registration

- **course_offerings**: Specific course instances per semester with assigned lecturers
- **course_registrations**: Student course registrations linked to specific offerings
- **class_sessions**: Individual class sessions with room bookings and instructor assignments
- **attendances**: Student attendance records per class session

#### Assessment & Academic Records

- **academic_records**: Comprehensive academic records linking students, courses, and instructors
- **syllabus**: Course syllabi with assessment component definitions
- **assessment_components**: Assessment structure with detailed component breakdown
- **assessment_component_details**: Granular assessment details
- **assessment_component_detail_scores**: Individual student scores per assessment component

#### Facilities & Resources

- **campuses**: Campus locations with associated buildings and rooms
- **buildings**: Campus buildings
- **rooms**: Specific rooms with booking capabilities
- **room_bookings**: Room reservation system

#### Staff & Administration

- **lectures**: Lecturer information with campus assignments
- **users**: System users with role-based access
- **roles**: User roles with permission assignments
- **permissions**: Granular system permissions
- **campus_user_roles**: Campus-specific role assignments

#### Academic Support

- **academic_holds**: Student holds with resolution tracking
- **program_change_requests**: Student program transfer requests
- **semesters**: Academic semester definitions

#### Key Relationships

```mermaid
erDiagram
    students ||--o{ academic_records : has
    students ||--o{ course_registrations : makes
    students ||--o{ enrollments : has
    students ||--o{ academic_holds : may_have
    students }o--|| programs : enrolled_in
    students }o--o| specializations : may_have
    students }o--|| curriculum_versions : follows

    course_offerings ||--o{ course_registrations : receives
    course_offerings ||--o{ class_sessions : contains
    course_offerings }o--|| curriculum_units : implements
    course_offerings }o--|| lectures : taught_by

    class_sessions ||--o{ attendances : tracks
    class_sessions }o--|| rooms : held_in

    curriculum_versions ||--o{ curriculum_units : contains
    curriculum_units }o--|| units : references

    syllabus ||--o{ assessment_components : defines
    assessment_components ||--o{ assessment_component_details : contains
    assessment_component_details ||--o{ assessment_component_detail_scores : scored_by
```

### API Response Formats

```typescript
// Standardized API Response
interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: Record<string, string[]>
  meta?: {
    pagination?: PaginationMeta
    filters?: FilterMeta
  }
}

// Dashboard API Response (aligned with database schema)
interface DashboardResponse {
  current_semester: Semester & {
    start_date: string
    end_date: string
    academic_calendar_url: string
  }
  credit_progress: {
    earned_credits: number
    required_credits: number
    remaining_requirements: CurriculumUnit[]
    completion_percentage: number
  }
  current_gpa: GPACalculation & {
    rank?: number
    trend: 'up' | 'down' | 'stable'
    semester_gpa: number
    cumulative_gpa: number
  }
  academic_holds: AcademicHold[]
  upcoming_assessments: AssessmentComponentDetail[]
  enrollment_status: Enrollment
  academic_standing: AcademicStanding
}

// Course Registration API Response
interface CourseRegistrationResponse {
  available_offerings: CourseOffering[]
  registered_offerings: CourseRegistration[]
  registration_period: {
    start_date: string
    end_date: string
    is_open: boolean
  }
  credit_limits: {
    minimum: number
    maximum: number
    overload_threshold: number
  }
}

// Timetable API Response
interface TimetableResponse {
  class_sessions: ClassSession[]
  semester: Semester
  week_range: {
    start_date: string
    end_date: string
  }
}

// Grades API Response
interface GradesResponse {
  academic_records: AcademicRecord[]
  assessment_scores: AssessmentScore[]
  gpa_calculations: GPACalculation[]
  semester_summaries: {
    semester: Semester
    total_credits: number
    semester_gpa: number
    units_completed: number
  }[]
}

// Attendance API Response
interface AttendanceResponse {
  attendance_records: Attendance[]
  attendance_summary: {
    course_offering: CourseOffering
    total_sessions: number
    attended_sessions: number
    attendance_percentage: number
    minimum_required: number
    status: 'good' | 'warning' | 'critical'
  }[]
}
```

## Error Handling

### Error Boundary Strategy

```typescript
// Global Error Handler
export const useErrorHandler = () => {
  const toast = useToast()
  const router = useRouter()

  const handleError = (error: Error, context?: string) => {
    console.error(`Error in ${context}:`, error)

    // Categorize errors
    if (error.message.includes('401') || error.message.includes('Authentication')) {
      // Redirect to login
      router.push('/login')
      toast.error('Session expired. Please log in again.')
    } else if (error.message.includes('403')) {
      toast.error('You do not have permission to perform this action.')
    } else if (error.message.includes('404')) {
      toast.error('The requested resource was not found.')
    } else if (error.message.includes('500')) {
      toast.error('Server error. Please try again later.')
    } else {
      toast.error(error.message || 'An unexpected error occurred.')
    }
  }

  return { handleError }
}

// Component Error Boundary
export const ErrorBoundary = defineComponent({
  setup(_, { slots }) {
    const error = ref<Error | null>(null)
    const { handleError } = useErrorHandler()

    const resetError = () => {
      error.value = null
    }

    onErrorCaptured((err) => {
      error.value = err
      handleError(err, 'Component Error Boundary')
      return false
    })

    return () => {
      if (error.value) {
        return h(ErrorFallback, { error: error.value, onReset: resetError })
      }
      return slots.default?.()
    }
  },
})
```

### Network Error Handling

```typescript
// Retry Logic with Exponential Backoff
export const useRetry = () => {
  const retry = async <T>(fn: () => Promise<T>, maxAttempts = 3, baseDelay = 1000): Promise<T> => {
    let lastError: Error

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error

        if (attempt === maxAttempts) {
          throw lastError
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt - 1)
        await new Promise((resolve) => setTimeout(resolve, delay))
      }
    }

    throw lastError!
  }

  return { retry }
}
```

## Testing Strategy

### Unit Testing Approach

```typescript
// Component Testing with Vue Test Utils
describe('DashboardOverview', () => {
  it('displays current semester information', async () => {
    const mockStore = {
      currentSemester: {
        name: 'Fall 2024',
        start_date: '2024-09-01',
        end_date: '2024-12-15',
      },
    }

    const wrapper = mount(DashboardOverview, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: { dashboard: mockStore },
          }),
        ],
      },
    })

    expect(wrapper.text()).toContain('Fall 2024')
  })

  it('handles loading states correctly', async () => {
    const wrapper = mount(DashboardOverview)

    expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)

    await flushPromises()

    expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(false)
  })
})

// Store Testing
describe('useDashboardStore', () => {
  it('fetches dashboard data correctly', async () => {
    const store = useDashboardStore()

    // Mock API response
    vi.mocked(useApi().get).mockResolvedValue({
      success: true,
      data: mockDashboardData,
    })

    await store.fetchDashboardData()

    expect(store.currentSemester).toEqual(mockDashboardData.current_semester)
  })
})
```

### E2E Testing Strategy

```typescript
// Cypress E2E Tests
describe('Student Portal E2E', () => {
  beforeEach(() => {
    cy.login('<EMAIL>', 'password')
  })

  it('completes course registration flow', () => {
    cy.visit('/courses')
    cy.get('[data-testid="course-card"]').first().click()
    cy.get('[data-testid="add-course-btn"]').click()
    cy.get('[data-testid="registration-cart"]').should('contain', 'CS101')
    cy.get('[data-testid="submit-registration"]').click()
    cy.get('[data-testid="success-toast"]').should('be.visible')
  })

  it('displays dashboard with correct data', () => {
    cy.visit('/dashboard')
    cy.get('[data-testid="semester-card"]').should('be.visible')
    cy.get('[data-testid="gpa-display"]').should('contain.text', '3.')
    cy.get('[data-testid="credit-progress"]').should('be.visible')
  })
})
```

### Performance Testing

```typescript
// Performance Monitoring
export const usePerformanceMonitor = () => {
  const measurePageLoad = (pageName: string) => {
    const startTime = performance.now()

    onMounted(() => {
      const endTime = performance.now()
      const loadTime = endTime - startTime

      // Log to analytics
      console.log(`${pageName} loaded in ${loadTime}ms`)

      // Send to monitoring service
      if (loadTime > 2000) {
        console.warn(`Slow page load detected: ${pageName} - ${loadTime}ms`)
      }
    })
  }

  return { measurePageLoad }
}
```

## Accessibility Implementation

### WCAG 2.1 AA Compliance

```typescript
// Accessibility Composable
export const useAccessibility = () => {
  const announceToScreenReader = (message: string) => {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'polite')
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message

    document.body.appendChild(announcement)

    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }

  const trapFocus = (element: HTMLElement) => {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    )

    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault()
          lastElement.focus()
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault()
          firstElement.focus()
        }
      }
    }

    element.addEventListener('keydown', handleTabKey)
    firstElement.focus()

    return () => element.removeEventListener('keydown', handleTabKey)
  }

  return { announceToScreenReader, trapFocus }
}
```

### Keyboard Navigation

```vue
<!-- Keyboard Navigation Implementation -->
<template>
  <nav role="navigation" aria-label="Main navigation" @keydown="handleKeyNavigation">
    <ul role="menubar">
      <li v-for="(item, index) in menuItems" :key="item.id" role="none">
        <router-link
          :to="item.path"
          role="menuitem"
          :aria-current="$route.path === item.path ? 'page' : undefined"
          :tabindex="index === 0 ? 0 : -1"
          @focus="currentIndex = index"
        >
          {{ item.label }}
        </router-link>
      </li>
    </ul>
  </nav>
</template>

<script setup lang="ts">
const currentIndex = ref(0)
const menuItems = ref([
  { id: 'dashboard', label: 'Dashboard', path: '/dashboard' },
  { id: 'courses', label: 'Courses', path: '/courses' },
  { id: 'grades', label: 'Grades', path: '/grades' },
])

const handleKeyNavigation = (event: KeyboardEvent) => {
  const { key } = event

  if (key >= '1' && key <= '9') {
    const index = parseInt(key) - 1
    if (index < menuItems.value.length) {
      currentIndex.value = index
      // Focus and navigate
    }
  }

  if (key === 'ArrowDown' || key === 'ArrowRight') {
    event.preventDefault()
    currentIndex.value = (currentIndex.value + 1) % menuItems.value.length
  }

  if (key === 'ArrowUp' || key === 'ArrowLeft') {
    event.preventDefault()
    currentIndex.value =
      currentIndex.value === 0 ? menuItems.value.length - 1 : currentIndex.value - 1
  }
}
</script>
```

## Progressive Web App Features

### Service Worker Implementation

```typescript
// sw.ts - Service Worker
const CACHE_NAME = 'student-portal-v1'
const STATIC_CACHE = ['/', '/dashboard', '/courses', '/grades', '/manifest.json']

self.addEventListener('install', (event) => {
  event.waitUntil(caches.open(CACHE_NAME).then((cache) => cache.addAll(STATIC_CACHE)))
})

self.addEventListener('fetch', (event) => {
  if (event.request.url.includes('/api/')) {
    // Network first for API calls
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          if (response.ok) {
            const responseClone = response.clone()
            caches.open(CACHE_NAME).then((cache) => cache.put(event.request, responseClone))
          }
          return response
        })
        .catch(() => caches.match(event.request)),
    )
  } else {
    // Cache first for static assets
    event.respondWith(
      caches.match(event.request).then((response) => response || fetch(event.request)),
    )
  }
})
```

### Push Notifications

```typescript
// Push Notification Service
export const usePushNotifications = () => {
  const { requestPermission } = useNotifications()

  const subscribeToPush = async () => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      const registration = await navigator.serviceWorker.ready

      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: import.meta.env.VITE_VAPID_PUBLIC_KEY,
      })

      // Send subscription to server
      await useApi().post('/notifications/subscribe', {
        subscription: subscription.toJSON(),
      })
    }
  }

  return { subscribeToPush }
}
```

This design document provides a comprehensive foundation for implementing the student portal with modern web technologies, focusing on performance, accessibility, and user experience while maintaining code quality and maintainability.
