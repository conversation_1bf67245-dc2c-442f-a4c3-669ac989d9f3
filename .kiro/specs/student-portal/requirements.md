# Requirements Document

## Introduction

The Student Portal is a comprehensive web application designed to provide students with a centralized platform for managing their academic journey. The portal serves as a "control tower" where students can access all essential academic information, perform self-service tasks, and stay informed about their progress. The system prioritizes clarity, mobile-first design, and accessibility while reducing dependency on academic staff through intuitive self-service capabilities.

## Requirements

### Requirement 1

**User Story:** As a student, I want to view a comprehensive dashboard that shows my current academic status, so that I can quickly understand where I am in my studies and what actions I need to take next.

#### Acceptance Criteria

1. WHEN a student accesses the dashboard THEN the system SHALL display the current semester name and date range
2. WHEN a student views the dashboard THEN the system SHALL show a credit progress indicator displaying earned credits versus graduation requirements
3. WHEN a student views the dashboard THEN the system SHALL display their current GPA with color-coded status (green ≥3.2, yellow 2-3, red <2)
4. WHEN a student has academic holds THEN the system SHALL display them prominently with a subtle red pulse animation (non-looping)
5. WHEN a student clicks on the semester card THEN the system SHALL open a modal with the academic calendar
6. WHEN a student hovers over credit progress THEN the system SHALL show a tooltip indicating missing requirements

### Requirement 2

**User Story:** As a student, I want to manage my personal information and view my study plan, so that I can keep my details current and track my academic progression.

#### Acceptance Criteria

1. WHEN a student accesses their profile THEN the system SHALL display personal information in view mode by default
2. WHEN a student clicks "Edit" on personal info THEN the system SHALL enable inline editing with form validation
3. WHEN a student modifies personal information THEN the system SHALL auto-save after 1 second of inactivity and show confirmation
4. WHEN a student views their study plan THEN the system SHALL display an 8-semester grid with subject avatars
5. WHEN a subject is passed THEN the system SHALL show it as filled, otherwise as outline
6. WHEN a student taps a subject THEN the system SHALL open a bottom-sheet modal with course details

### Requirement 3

**User Story:** As a student, I want to view my weekly timetable with filtering options, so that I can see my schedule and join online sessions when needed.

#### Acceptance Criteria

1. WHEN a student accesses the timetable THEN the system SHALL display a weekly view with horizontal scroll navigation
2. WHEN viewing the timetable THEN the system SHALL provide a "Today" button for quick navigation
3. WHEN a student applies filters THEN the system SHALL show/hide class types (Lecture/Lab/Exam) without layout shifts
4. WHEN a student taps a time slot THEN the system SHALL open a slide-in panel with session details
5. WHEN an online session starts within 10 minutes THEN the system SHALL show a "Join Zoom" button
6. WHEN classes are color-coded by unit THEN the system SHALL maintain consistent colors for recognition

### Requirement 4

**User Story:** As a student, I want to register for courses by exploring available options and managing my selections, so that I can plan my academic schedule effectively.

#### Acceptance Criteria

1. WHEN a student accesses course registration THEN the system SHALL display open courses with filtering options
2. WHEN a course is full THEN the system SHALL show "Full 0/50" badge and disable the Add button
3. WHEN there's a time conflict THEN the system SHALL gray out the Add button and show conflict warning
4. WHEN a student adds a course THEN the system SHALL move it to "My List" panel with optimistic UI
5. WHEN API errors occur THEN the system SHALL rollback the optimistic update and show error toast
6. WHEN a student submits registration THEN the system SHALL show confirmation modal with total credits and fees
7. WHEN registration exceeds normal load THEN the system SHALL show overload warning with approval request option

### Requirement 5

**User Story:** As a student, I want to view my grades and GPA trends, so that I can track my academic performance over time.

#### Acceptance Criteria

1. WHEN a student accesses grades THEN the system SHALL display transcript grouped by semester with sticky headers
2. WHEN no grades are available THEN the system SHALL show empty state with illustration and helpful message
3. WHEN a student views GPA trend THEN the system SHALL display a line chart across semesters
4. WHEN hovering over chart points THEN the system SHALL show GPA and rank information
5. WHEN displaying incomplete semesters THEN the system SHALL use dotted lines to indicate projected data

### Requirement 6

**User Story:** As a student, I want to track my assignments and assessments, so that I can manage my workload and meet deadlines.

#### Acceptance Criteria

1. WHEN a student accesses assessments THEN the system SHALL display assignment cards with progress rings per course
2. WHEN an assignment has less than 24 hours remaining THEN the system SHALL show countdown timer in red
3. WHEN uploading files THEN the system SHALL support drag-drop functionality with progress indicators
4. WHEN assignments are overdue THEN the system SHALL clearly mark them with appropriate visual indicators

### Requirement 7

**User Story:** As a student, I want to monitor my attendance across courses, so that I can ensure I meet minimum requirements and avoid exam restrictions.

#### Acceptance Criteria

1. WHEN a student views attendance THEN the system SHALL display bar charts showing attendance percentage per class
2. WHEN attendance falls below 75% THEN the system SHALL mark it in red with "Risk of exam ban" tooltip
3. WHEN attendance drops below 80% THEN the system SHALL send push notifications to alert the student

### Requirement 8

**User Story:** As a student, I want to view and resolve any academic holds, so that I can address issues that might prevent my academic progress.

#### Acceptance Criteria

1. WHEN a student has holds THEN the system SHALL display them in a list with severity icons
2. WHEN displaying hold information THEN the system SHALL provide clear, actionable instructions
3. WHEN holds are critical THEN the system SHALL include specific deadlines and contact information

### Requirement 9

**User Story:** As a student, I want to explore my curriculum roadmap, so that I can understand course prerequisites and plan my academic path.

#### Acceptance Criteria

1. WHEN a student accesses curriculum THEN the system SHALL display a 4-column grid representing Years 1-4
2. WHEN filtering courses THEN the system SHALL toggle between Core and Elective course visibility
3. WHEN hovering over courses (desktop) THEN the system SHALL show prerequisite information
4. WHEN courses are completed THEN the system SHALL display them with crossed-out styling
5. WHEN on mobile THEN the system SHALL support long-press for course details

### Requirement 10

**User Story:** As a student, I want to receive and manage notifications, so that I can stay informed about important academic updates and deadlines.

#### Acceptance Criteria

1. WHEN a student accesses notifications THEN the system SHALL display them in a chat-like feed with source avatars
2. WHEN filtering notifications THEN the system SHALL provide tabs for All/Academic/Finance categories
3. WHEN refreshing notifications THEN the system SHALL support pull-to-refresh functionality
4. WHEN critical events occur THEN the system SHALL send both email and in-app notifications

### Requirement 11

**User Story:** As a student, I want the portal to work seamlessly across devices with offline capabilities, so that I can access essential information even without internet connectivity.

#### Acceptance Criteria

1. WHEN accessing on mobile devices THEN the system SHALL provide a mobile-first responsive design
2. WHEN on desktop (>1280px) THEN the system SHALL maintain a persistent sidebar navigation
3. WHEN on mobile THEN the system SHALL use hamburger menu with bottom navigation for key functions
4. WHEN installed as PWA THEN the system SHALL cache timetable data offline for 7 days
5. WHEN using deep links THEN the system SHALL support URLs like `/assessment/12345` for direct access
