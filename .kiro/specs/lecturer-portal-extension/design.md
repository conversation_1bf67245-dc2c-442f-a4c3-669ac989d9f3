# Design Document

## Overview

The Lecturer Portal Extension enhances the existing Vue 3 student portal application by introducing role-based functionality within a unified codebase. The design maintains architectural consistency while adding lecturer-specific features through role-based layouts, routing, and state management. The extension leverages existing components and infrastructure while introducing new lecturer-focused interfaces for teaching management, grading, attendance tracking, and student communication.

The architecture emphasizes code reusability, type safety, and maintainability by sharing core components between student and lecturer interfaces while providing role-specific adaptations. The system uses the existing tech stack (Vue 3, TypeScript, Pinia, Tailwind CSS, Reka UI) and extends it with lecturer-specific stores, views, and API integrations.

## Architecture

### High-Level Architecture Extension

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Vue 3 SPA] --> B[Vue Router with Role Guards]
        A --> C[Pinia Stores]
        A --> D[Composables]
        A --> E[UI Components]
    end

    subgraph "Role-Based State Management"
        C --> F[Auth Store - Shared]
        C --> G[Student Stores]
        C --> H[Lecturer Stores]
        H --> H1[useLecturerDashboard]
        H --> H2[useLecturerCourses]
        H --> H3[useLecturerGrading]
        H --> H4[useLecturerAttendance]
    end

    subgraph "Role-Based API Layer"
        D --> J[useApi Composable]
        J --> K[HTTP Client with Role Detection]
        K --> L1[Student API - /api/student/*]
        K --> L2[Lecturer API - /api/lecturer/*]
    end

    subgraph "Role-Based UI Layer"
        E --> M1[Student Layout]
        E --> M2[Lecturer Layout]
        E --> N[Shared Components]
        E --> O[Role-Specific Components]
        N --> P[TimetableView]
        N --> Q[CourseCard]
        N --> R[NotificationCenter]
    end

    subgraph "Role-Based Routing"
        B --> S1[Student Routes - /student/*]
        B --> S2[Lecturer Routes - /lecturer/*]
        B --> T[Route Guards with Role Validation]
    end
```

### Role-Based Architecture Strategy

The extension implements a role-based architecture that maintains a single codebase while providing distinct user experiences:

1. **Shared Authentication**: Single auth store manages user sessions and role detection
2. **Role-Based Routing**: Separate route trees for students (`/student/*`) and lecturers (`/lecturer/*`)
3. **Layout Separation**: Distinct layouts (StudentLayout, LecturerLayout) with role-specific navigation
4. **Component Reusability**: Shared components adapt behavior based on role props
5. **API Segregation**: Role-specific API endpoints with automatic routing based on user role

## Components and Interfaces

### Enhanced Authentication System

```typescript
// Extended User interface with role support
export interface User {
  id: string
  email: string
  name: string
  role: 'student' | 'lecturer'

  // Student-specific fields
  student_id?: string
  enrollment_status?: string
  campus?: Campus
  program?: Program

  // Lecturer-specific fields
  employee_id?: string
  department?: Department
  title?: string
  office_location?: string

  // Shared fields
  full_name?: string
  phone?: string
  avatar_url?: string
  can_register?: boolean
  has_active_holds?: boolean
}

// Enhanced Auth Store with role management
export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)

  const role = computed(() => user.value?.role || null)
  const isStudent = computed(() => role.value === 'student')
  const isLecturer = computed(() => role.value === 'lecturer')

  const getApiPrefix = computed(() => {
    return role.value ? `/api/${role.value}` : '/api'
  })

  return {
    user,
    token,
    role,
    isStudent,
    isLecturer,
    getApiPrefix,
    // ... existing methods
  }
})
```

### Role-Based Router Configuration

```typescript
// Enhanced router with role-based routing
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true },
    },

    // Student Portal Routes
    {
      path: '/student',
      component: () => import('../layouts/StudentLayout.vue'),
      meta: { requiresAuth: true, role: 'student' },
      children: [
        {
          path: 'dashboard',
          name: 'student-dashboard',
          component: () => import('../views/student/DashboardView.vue'),
        },
        {
          path: 'courses',
          name: 'student-courses',
          component: () => import('../views/student/CoursesView.vue'),
        },
        {
          path: 'grades',
          name: 'student-grades',
          component: () => import('../views/student/GradesView.vue'),
        },
        // ... other student routes
      ],
    },

    // Lecturer Portal Routes
    {
      path: '/lecturer',
      component: () => import('../layouts/LecturerLayout.vue'),
      meta: { requiresAuth: true, role: 'lecturer' },
      children: [
        {
          path: 'dashboard',
          name: 'lecturer-dashboard',
          component: () => import('../views/lecturer/DashboardView.vue'),
        },
        {
          path: 'teaching',
          name: 'lecturer-teaching',
          component: () => import('../views/lecturer/TeachingView.vue'),
          children: [
            {
              path: 'courses',
              name: 'lecturer-courses',
              component: () => import('../views/lecturer/CoursesView.vue'),
            },
            {
              path: 'timetable',
              name: 'lecturer-timetable',
              component: () => import('../views/lecturer/TimetableView.vue'),
            },
          ],
        },
        {
          path: 'attendance',
          name: 'lecturer-attendance',
          component: () => import('../views/lecturer/AttendanceView.vue'),
        },
        {
          path: 'students',
          name: 'lecturer-students',
          component: () => import('../views/lecturer/StudentsView.vue'),
        },
        {
          path: 'feedback',
          name: 'lecturer-feedback',
          component: () => import('../views/lecturer/FeedbackView.vue'),
        },
        // ... other lecturer routes
      ],
    },

    // Root redirect based on role
    {
      path: '/',
      redirect: (to) => {
        const authStore = useAuthStore()
        if (authStore.isStudent) return '/student/dashboard'
        if (authStore.isLecturer) return '/lecturer/dashboard'
        return '/login'
      },
    },
  ],
})

// Enhanced navigation guard with role validation
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // Check authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // Check role authorization
  if (to.meta.role && authStore.role !== to.meta.role) {
    // Redirect to appropriate dashboard based on user role
    const redirectPath = authStore.isStudent ? '/student/dashboard' : '/lecturer/dashboard'
    next(redirectPath)
    return
  }

  next()
})
```

### Lecturer-Specific Data Models

```typescript
// Lecturer Dashboard Models (aligned with existing schema)
interface LecturerDashboard {
  teaching_summary: {
    total_courses: number
    total_students: number
    active_sessions_today: ClassSession[]
    upcoming_sessions: ClassSession[]
  }
  attendance_summary: {
    sessions_requiring_attendance: number
    sessions_completed_today: number
    average_class_attendance: number
  }
  student_alerts: {
    low_attendance_students: StudentAlert[]
    students_needing_attention: StudentAlert[]
  }
  recent_activities: Activity[]
}

interface StudentAlert {
  student_id: number
  student_name: string
  course_code: string
  alert_type: 'low_attendance' | 'poor_participation' | 'multiple_absences'
  severity: 'low' | 'medium' | 'high'
  details: string
  action_required: boolean
  attendance_percentage?: number
}

// Course Management Models (aligned with existing schema)
interface CourseOffering {
  id: number
  curriculum_unit: {
    code: string
    name: string
    credits: number
  }
  semester: {
    id: number
    name: string
    start_date: string
    end_date: string
  }
  section_code: string
  max_capacity: number
  current_enrollment: number
  delivery_mode: 'in_person' | 'online' | 'hybrid' | 'blended'
  schedule_days: string[] // JSON array
  schedule_time_start: string
  schedule_time_end: string
  location: string
  class_sessions: ClassSession[]
  materials: CourseMaterial[]
}

interface ClassSession {
  id: number
  course_offering_id: number
  session_title: string
  session_description: string
  session_date: string
  start_time: string
  end_time: string
  session_type:
    | 'lecture'
    | 'tutorial'
    | 'practical'
    | 'laboratory'
    | 'seminar'
    | 'workshop'
    | 'exam'
  delivery_mode: 'in_person' | 'online' | 'hybrid' | 'blended'
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'postponed'
  online_meeting_url?: string
  location?: string
  attendance_required: boolean
  attendance_marked: boolean
  expected_attendees: number
  actual_attendees: number
  attendance_percentage: number
}

// Student List and Course Management Models
interface EnrolledStudent {
  student_id: number
  student_name: string
  student_email: string
  enrollment_status: string
  registration_date: string
  attendance_percentage: number
  participation_score?: number
  academic_standing?: string
}

interface CourseStatistics {
  total_sessions: number
  completed_sessions: number
  average_attendance: number
  attendance_trend: 'improving' | 'declining' | 'stable'
  student_performance_summary: {
    excellent_attendance: number // >90%
    good_attendance: number // 75-90%
    poor_attendance: number // <75%
  }
}

// Attendance Models (using existing attendances table)
interface AttendanceSession {
  id: number
  class_session_id: number
  course_code: string
  session_date: string
  session_type: string
  attendance_records: AttendanceRecord[]
  marked_by: number // lecturer id
  marked_at?: string
}

interface AttendanceRecord {
  id: number
  class_session_id: number
  student_id: number
  student_name: string
  status: 'present' | 'absent' | 'late' | 'excused' | 'partial'
  check_in_time?: string
  check_out_time?: string
  minutes_late: number
  participation_level?: 'excellent' | 'good' | 'average' | 'poor' | 'none'
  participation_score?: number
  notes?: string
  recorded_by_lecture_id: number
}
```

### Lecturer Store Architecture

```typescript
// Lecturer Dashboard Store
export const useLecturerDashboardStore = defineStore('lecturerDashboard', () => {
  const dashboard = ref<LecturerDashboard | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const { get } = useApi()

  const fetchDashboard = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await get('/lecturer/dashboard')
      dashboard.value = response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load dashboard'
    } finally {
      loading.value = false
    }
  }

  const refreshDashboard = async () => {
    await fetchDashboard()
  }

  return {
    dashboard,
    loading,
    error,
    fetchDashboard,
    refreshDashboard,
  }
})

// Lecturer Courses Store
export const useLecturerCoursesStore = defineStore('lecturerCourses', () => {
  const courses = ref<CourseOffering[]>([])
  const selectedCourse = ref<CourseOffering | null>(null)
  const loading = ref(false)

  const { get, post, put, delete: del } = useApi()

  const fetchCourses = async () => {
    loading.value = true
    try {
      const response = await get('/lecturer/courses')
      courses.value = response.data
    } finally {
      loading.value = false
    }
  }

  const uploadMaterial = async (courseId: string, material: FormData) => {
    const response = await post(`/lecturer/courses/${courseId}/materials`, material)

    // Update local state
    const course = courses.value.find((c) => c.id === courseId)
    if (course) {
      course.materials.push(response.data)
    }

    return response.data
  }

  const updateSyllabus = async (courseId: string, syllabusData: any) => {
    const response = await put(`/lecturer/courses/${courseId}/syllabus`, syllabusData)

    // Update local state
    const course = courses.value.find((c) => c.id === courseId)
    if (course) {
      course.syllabus_url = response.data.syllabus_url
    }

    return response.data
  }

  return {
    courses,
    selectedCourse,
    loading,
    fetchCourses,
    uploadMaterial,
    updateSyllabus,
  }
})

// Lecturer Student Management Store
export const useLecturerStudentStore = defineStore('lecturerStudent', () => {
  const enrolledStudents = ref<EnrolledStudent[]>([])
  const courseStatistics = ref<CourseStatistics | null>(null)
  const selectedCourse = ref<number | null>(null)

  const { get, put } = useApi()

  const fetchEnrolledStudents = async (courseId: number) => {
    const response = await get(`/lecturer/courses/${courseId}/students`)
    enrolledStudents.value = response.data
    selectedCourse.value = courseId
  }

  const updateStudentNotes = async (studentId: number, courseId: number, notes: string) => {
    const response = await put(`/lecturer/students/${studentId}/notes`, {
      course_id: courseId,
      notes,
    })
    return response.data
  }

  const fetchCourseStatistics = async (courseId: number) => {
    const response = await get(`/lecturer/courses/${courseId}/statistics`)
    courseStatistics.value = response.data
    return response.data
  }

  const getStudentAttendanceDetails = async (studentId: number, courseId: number) => {
    const response = await get(`/lecturer/students/${studentId}/attendance?course_id=${courseId}`)
    return response.data
  }

  return {
    enrolledStudents,
    courseStatistics,
    selectedCourse,
    fetchEnrolledStudents,
    updateStudentNotes,
    fetchCourseStatistics,
    getStudentAttendanceDetails,
  }
})
```

### Shared Component Adaptations

```typescript
// Enhanced TimetableView with role-based functionality
<template>
  <div class="timetable-container">
    <TimetableHeader
      :role="userRole"
      :can-create-sessions="isLecturer"
      @create-session="handleCreateSession"
    />

    <TimetableGrid
      :sessions="sessions"
      :role="userRole"
      @session-click="handleSessionClick"
    />

    <!-- Role-specific session details -->
    <StudentSessionModal
      v-if="isStudent && selectedSession"
      :session="selectedSession"
      @close="selectedSession = null"
    />

    <LecturerSessionModal
      v-if="isLecturer && selectedSession"
      :session="selectedSession"
      @mark-attendance="handleMarkAttendance"
      @upload-materials="handleUploadMaterials"
      @close="selectedSession = null"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  role: 'student' | 'lecturer'
  sessions: ClassSession[]
}

const props = defineProps<Props>()
const { role } = toRefs(props)

const isStudent = computed(() => role.value === 'student')
const isLecturer = computed(() => role.value === 'lecturer')

// Role-specific event handlers
const handleSessionClick = (session: ClassSession) => {
  selectedSession.value = session

  if (isLecturer.value) {
    // Lecturer-specific logic: check if attendance can be marked
    checkAttendanceStatus(session)
  }
}

const handleCreateSession = (sessionData: Partial<ClassSession>) => {
  if (isLecturer.value) {
    emit('create-session', sessionData)
  }
}
</script>

// Enhanced CourseCard with role-based display
<template>
  <Card class="course-card" :class="roleClasses">
    <CardHeader>
      <div class="flex justify-between items-start">
        <div>
          <CardTitle>{{ course.course_code }}</CardTitle>
          <CardDescription>{{ course.course_name }}</CardDescription>
        </div>

        <!-- Role-specific actions -->
        <div class="flex gap-2">
          <Button
            v-if="isStudent"
            variant="outline"
            size="sm"
            @click="viewCourseDetails"
          >
            View Details
          </Button>

          <DropdownMenu v-if="isLecturer">
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical class="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem @click="manageCourse">
                Manage Course
              </DropdownMenuItem>
              <DropdownMenuItem @click="viewGradebook">
                View Gradebook
              </DropdownMenuItem>
              <DropdownMenuItem @click="markAttendance">
                Mark Attendance
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </CardHeader>

    <CardContent>
      <!-- Student view: enrollment status, grades -->
      <div v-if="isStudent" class="space-y-2">
        <div class="flex justify-between text-sm">
          <span>Current Grade:</span>
          <Badge :variant="gradeVariant">{{ currentGrade || 'N/A' }}</Badge>
        </div>
        <div class="flex justify-between text-sm">
          <span>Attendance:</span>
          <span :class="attendanceColor">{{ attendancePercentage }}%</span>
        </div>
      </div>

      <!-- Lecturer view: student count, grading status -->
      <div v-if="isLecturer" class="space-y-2">
        <div class="flex justify-between text-sm">
          <span>Enrolled Students:</span>
          <span>{{ course.enrolled_students }}/{{ course.max_capacity }}</span>
        </div>
        <div class="flex justify-between text-sm">
          <span>Pending Grades:</span>
          <Badge variant="secondary">{{ pendingGrades }}</Badge>
        </div>
        <Progress :value="gradingProgress" class="h-2" />
      </div>
    </CardContent>
  </Card>
</template>
```

### Layout Components

```typescript
// LecturerLayout.vue - Lecturer-specific layout
<template>
  <SidebarProvider>
    <LecturerSidebar />
    <SidebarInset>
      <header class="lecturer-header">
        <SidebarTrigger />
        <Separator orientation="vertical" />

        <!-- Lecturer-specific header content -->
        <div class="flex items-center gap-4">
          <h1 class="text-lg font-semibold">{{ pageTitle }}</h1>
        </div>

        <div class="flex-1" />

        <!-- Lecturer tools -->
        <div class="flex items-center gap-3">
          <QuickGradeButton />
          <AttendanceQuickAccess />
          <NotificationPopover />
          <UserAvatar />
        </div>
      </header>

      <main class="flex-1 p-4">
        <RouterView />
      </main>
    </SidebarInset>

    <LecturerMobileNav />
  </SidebarProvider>
</template>

// LecturerSidebar.vue - Lecturer navigation
<template>
  <Sidebar>
    <SidebarHeader>
      <div class="flex items-center gap-2">
        <GraduationCap class="h-6 w-6" />
        <span class="font-semibold">Lecturer Portal</span>
      </div>
    </SidebarHeader>

    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>Overview</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton as-child>
                <RouterLink to="/lecturer/dashboard">
                  <LayoutDashboard class="h-4 w-4" />
                  Dashboard
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>Teaching</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton as-child>
                <RouterLink to="/lecturer/teaching/courses">
                  <BookOpen class="h-4 w-4" />
                  My Courses
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton as-child>
                <RouterLink to="/lecturer/teaching/timetable">
                  <Calendar class="h-4 w-4" />
                  Timetable
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>Student Management</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton as-child>
                <RouterLink to="/lecturer/attendance">
                  <Users class="h-4 w-4" />
                  Attendance
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton as-child>
                <RouterLink to="/lecturer/students">
                  <UserCheck class="h-4 w-4" />
                  Student Lists
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton as-child>
                <RouterLink to="/lecturer/feedback">
                  <MessageSquare class="h-4 w-4" />
                  Feedback
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <!-- Additional lecturer menu items -->
    </SidebarContent>
  </Sidebar>
</template>
```

## Data Models

### Existing Database Schema Alignment

Based on the existing database schema, the lecturer portal will work with these existing tables:

```sql
-- Existing tables that will be used:

-- lectures table (existing - contains lecturer information)
-- Fields: id, employee_id, first_name, last_name, email, phone, department,
--         academic_rank, office_address, biography, etc.

-- course_offerings table (existing - course instances taught by lecturers)
-- Fields: id, semester_id, curriculum_unit_id, lecture_id, section_code,
--         max_capacity, current_enrollment, delivery_mode, schedule_days, etc.

-- class_sessions table (existing - individual class sessions)
-- Fields: id, course_offering_id, instructor_id, session_date, start_time,
--         end_time, session_type, delivery_mode, online_meeting_url, etc.

-- attendances table (existing - attendance records)
-- Fields: id, class_session_id, student_id, recorded_by_lecture_id, status,
--         check_in_time, participation_level, notes, etc.

-- The lecturer portal will work with existing tables only:
-- No additional database tables are required for the initial implementation.
--
-- Core functionality will be built around:
-- - lectures table (lecturer information and authentication)
-- - course_offerings table (courses taught by lecturers)
-- - class_sessions table (individual class sessions)
-- - attendances table (attendance tracking and management)
-- - students table (student information for class lists)
-- - course_registrations table (enrolled students per course)
--
-- Future enhancements could add assignment/material management tables,
-- but the initial implementation focuses on core teaching functionality.
```

### API Response Formats

```typescript
// Lecturer Dashboard API Response
interface LecturerDashboardResponse {
  teaching_summary: {
    total_courses: number
    total_students: number
    active_sessions_today: ClassSession[]
  }
  grading_summary: {
    pending_assignments: number
    total_assignments: number
    recent_submissions: AssignmentSubmission[]
  }
  student_alerts: {
    low_gpa_students: StudentAlert[]
    high_absence_students: StudentAlert[]
    overdue_assignments: AssignmentAlert[]
  }
  recent_activities: Activity[]
  upcoming_deadlines: {
    assignment_due_dates: Assignment[]
    exam_schedules: ClassSession[]
  }
}

// Course Management API Response
interface CourseOfferingResponse {
  id: string
  course: {
    code: string
    name: string
    credits: number
    description: string
  }
  semester: string
  year: number
  enrolled_students: StudentSummary[]
  max_capacity: number
  schedule: ClassSession[]
  materials: CourseMaterial[]
  assignments: Assignment[]
  syllabus_url?: string
}

// Gradebook API Response
interface GradebookResponse {
  course_offering: CourseOfferingResponse
  assessment_components: AssessmentComponent[]
  student_grades: GradebookEntry[]
  grade_statistics: {
    average_grade: number
    median_grade: number
    grade_distribution: { [grade: string]: number }
  }
}
```

## Error Handling

### Role-Based Error Handling

```typescript
// Enhanced error handler with role-specific logic
export const useErrorHandler = () => {
  const toast = useToast()
  const router = useRouter()
  const authStore = useAuthStore()

  const handleError = (error: Error, context?: string) => {
    console.error(`Error in ${context}:`, error)

    // Role-specific error handling
    if (error.message.includes('403')) {
      if (authStore.isLecturer) {
        toast.error('You do not have permission to access this student data.')
      } else {
        toast.error('You do not have permission to perform this action.')
      }
    } else if (error.message.includes('grading')) {
      toast.error('Failed to save grade. Please try again.')
    } else if (error.message.includes('attendance')) {
      toast.error('Failed to mark attendance. Please check your connection.')
    }

    // Standard error handling
    // ... existing error handling logic
  }

  return { handleError }
}
```

## Testing Strategy

### Role-Based Testing Approach

```typescript
// Component testing with role context
describe('TimetableView', () => {
  it('displays lecturer-specific actions for lecturer role', async () => {
    const wrapper = mount(TimetableView, {
      props: { role: 'lecturer', sessions: mockSessions },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(wrapper.find('[data-testid="create-session-btn"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="mark-attendance-btn"]').exists()).toBe(true)
  })

  it('hides lecturer actions for student role', async () => {
    const wrapper = mount(TimetableView, {
      props: { role: 'student', sessions: mockSessions },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(wrapper.find('[data-testid="create-session-btn"]').exists()).toBe(false)
    expect(wrapper.find('[data-testid="mark-attendance-btn"]').exists()).toBe(false)
  })
})

// Store testing with role-specific data
describe('useLecturerStudentStore', () => {
  it('fetches enrolled students correctly', async () => {
    const store = useLecturerStudentStore()

    vi.mocked(useApi().get).mockResolvedValue({
      success: true,
      data: [
        {
          student_id: 1,
          student_name: 'John Doe',
          student_email: '<EMAIL>',
          attendance_percentage: 85,
        },
      ],
    })

    await store.fetchEnrolledStudents(1)

    expect(useApi().get).toHaveBeenCalledWith('/lecturer/courses/1/students')
    expect(store.enrolledStudents).toHaveLength(1)
  })
})
```

### E2E Testing for Role-Based Flows

```typescript
// Cypress E2E tests for lecturer portal
describe('Lecturer Portal E2E', () => {
  beforeEach(() => {
    cy.loginAsLecturer('<EMAIL>', 'password')
  })

  it('views student list and details', () => {
    cy.visit('/lecturer/students')
    cy.get('[data-testid="course-select"]').select('CS101')
    cy.get('[data-testid="student-list"]').should('be.visible')
    cy.get('[data-testid="student-item"]').first().click()
    cy.get('[data-testid="student-details-modal"]').should('be.visible')
    cy.get('[data-testid="attendance-percentage"]').should('contain', '%')
  })

  it('marks attendance for class session', () => {
    cy.visit('/lecturer/teaching/timetable')
    cy.get('[data-testid="session-card"]').first().click()
    cy.get('[data-testid="mark-attendance-btn"]').click()
    cy.get('[data-testid="student-attendance"]').first().find('[data-testid="present-btn"]').click()
    cy.get('[data-testid="save-attendance-btn"]').click()
    cy.get('[data-testid="attendance-saved-toast"]').should('be.visible')
  })
})
```

This design document provides a comprehensive foundation for implementing the lecturer portal extension while maintaining code reusability, architectural consistency, and user experience quality across both student and lecturer interfaces.
