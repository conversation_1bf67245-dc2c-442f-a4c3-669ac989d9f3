# Backend API Requirements for Student Portal

## Overview

This document outlines the complete backend API requirements, database models, and technical specifications needed to support the Student Portal frontend implementation. The backend should provide RESTful APIs with proper authentication, validation, and error handling.

## Current Implementation Status

✅ **Completed APIs:**

- `POST /auth/login/google` - Google OAuth authentication
- `GET /auth/me` - Get current user profile
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Token refresh

## Required Database Models

### 1. Core Academic Structure

```sql
-- Campuses
CREATE TABLE campuses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(10) UNIQUE NOT NULL,
  address TEXT,
  timezone VARCHAR(50) DEFAULT 'UTC',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Programs (Degrees)
CREATE TABLE programs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(20) UNIQUE NOT NULL,
  degree_type VARCHAR(50) NOT NULL, -- Bachelor, Master, PhD
  total_credits INTEGER NOT NULL,
  duration_semesters INTEGER NOT NULL,
  campus_id UUID REFERENCES campuses(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Specializations
CREATE TABLE specializations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(20),
  program_id UUID REFERENCES programs(id),
  required_credits INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Academic Semesters
CREATE TABLE academic_semesters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL, -- "Fall 2024", "Spring 2025"
  code VARCHAR(20) NOT NULL, -- "2024-3", "2025-1"
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  registration_start DATE,
  registration_end DATE,
  is_current BOOLEAN DEFAULT FALSE,
  academic_year INTEGER NOT NULL,
  semester_number INTEGER NOT NULL, -- 1=Spring, 2=Summer, 3=Fall
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Course Management

```sql
-- Instructors
CREATE TABLE instructors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id VARCHAR(50) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  department VARCHAR(100),
  title VARCHAR(100), -- Professor, Associate Professor, etc.
  avatar_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Courses (Course Catalog)
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(20) UNIQUE NOT NULL, -- "CS101", "MATH201"
  name VARCHAR(255) NOT NULL,
  description TEXT,
  credits INTEGER NOT NULL,
  course_type VARCHAR(50) NOT NULL, -- Core, Elective, Prerequisite
  prerequisites JSONB DEFAULT '[]', -- Array of course codes
  program_id UUID REFERENCES programs(id),
  semester_offered INTEGER[], -- [1,3] for Spring and Fall
  created_at TIMESTAMP DEFAULT NOW()
);

-- Course Offerings (Specific semester instances)
CREATE TABLE course_offerings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID REFERENCES courses(id),
  semester_id UUID REFERENCES academic_semesters(id),
  instructor_id UUID REFERENCES instructors(id),
  section VARCHAR(10) NOT NULL, -- "A", "B", "01"
  enrollment_capacity INTEGER NOT NULL,
  enrolled_count INTEGER DEFAULT 0,
  waitlist_capacity INTEGER DEFAULT 0,
  waitlist_count INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'open', -- open, closed, cancelled
  meeting_pattern JSONB, -- Schedule information
  classroom VARCHAR(100),
  online_meeting_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(course_id, semester_id, section)
);

-- Course Schedules
CREATE TABLE course_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_offering_id UUID REFERENCES course_offerings(id),
  day_of_week INTEGER NOT NULL, -- 1=Monday, 7=Sunday
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  session_type VARCHAR(50) NOT NULL, -- Lecture, Lab, Tutorial, Exam
  location VARCHAR(255),
  online_meeting_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Student Management

```sql
-- Students (Enhanced from existing)
CREATE TABLE students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  student_id VARCHAR(50) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  phone VARCHAR(20),
  date_of_birth DATE,
  gender VARCHAR(20),
  nationality VARCHAR(100),
  address TEXT,
  emergency_contact JSONB, -- {name, phone, relationship}
  avatar_url VARCHAR(500),

  -- Academic Information
  enrollment_status VARCHAR(50) NOT NULL DEFAULT 'active', -- active, inactive, graduated, suspended
  admission_date DATE NOT NULL,
  expected_graduation_date DATE,
  current_semester INTEGER DEFAULT 1,
  campus_id UUID REFERENCES campuses(id),
  program_id UUID REFERENCES programs(id),
  specialization_id UUID REFERENCES specializations(id),

  -- Registration Permissions
  can_register BOOLEAN DEFAULT TRUE,
  has_active_holds BOOLEAN DEFAULT FALSE,
  max_credits_per_semester INTEGER DEFAULT 18,

  -- Academic Standing
  current_gpa DECIMAL(3,2) DEFAULT 0.00,
  total_credits_earned INTEGER DEFAULT 0,
  total_credits_attempted INTEGER DEFAULT 0,
  academic_standing VARCHAR(50) DEFAULT 'good', -- good, probation, suspension

  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Course Registrations
CREATE TABLE course_registrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  registration_date TIMESTAMP DEFAULT NOW(),
  status VARCHAR(50) DEFAULT 'registered', -- registered, dropped, completed, failed
  grade VARCHAR(5), -- A+, A, A-, B+, B, B-, C+, C, C-, D, F
  grade_points DECIMAL(3,2), -- 4.00, 3.67, 3.33, etc.
  credits_earned INTEGER DEFAULT 0,
  final_grade_date TIMESTAMP,

  -- Attendance tracking
  total_sessions INTEGER DEFAULT 0,
  attended_sessions INTEGER DEFAULT 0,
  attendance_percentage DECIMAL(5,2) DEFAULT 100.00,

  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(student_id, course_offering_id)
);

-- Waitlist
CREATE TABLE course_waitlist (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  position INTEGER NOT NULL,
  added_date TIMESTAMP DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'waiting', -- waiting, enrolled, expired
  UNIQUE(student_id, course_offering_id)
);
```

### 4. Academic Records

```sql
-- Assignments
CREATE TABLE assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_offering_id UUID REFERENCES course_offerings(id),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  assignment_type VARCHAR(50) NOT NULL, -- homework, quiz, exam, project
  total_points INTEGER NOT NULL,
  due_date TIMESTAMP NOT NULL,
  submission_type VARCHAR(50) NOT NULL, -- online, paper, presentation
  allow_late_submission BOOLEAN DEFAULT FALSE,
  late_penalty_per_day DECIMAL(5,2) DEFAULT 0.00,
  instructions TEXT,
  attachments JSONB DEFAULT '[]',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Assignment Submissions
CREATE TABLE assignment_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assignment_id UUID REFERENCES assignments(id),
  student_id UUID REFERENCES students(id),
  submission_date TIMESTAMP DEFAULT NOW(),
  content TEXT,
  attachments JSONB DEFAULT '[]',
  status VARCHAR(50) DEFAULT 'submitted', -- draft, submitted, graded, returned
  points_earned DECIMAL(5,2),
  feedback TEXT,
  graded_date TIMESTAMP,
  graded_by UUID REFERENCES instructors(id),
  is_late BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(assignment_id, student_id)
);

-- Attendance Records
CREATE TABLE attendance_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  session_date DATE NOT NULL,
  session_type VARCHAR(50) NOT NULL, -- Lecture, Lab, Tutorial
  status VARCHAR(20) NOT NULL, -- present, absent, late, excused
  recorded_by UUID REFERENCES instructors(id),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(student_id, course_offering_id, session_date, session_type)
);
```

### 5. Academic Holds and Notifications

```sql
-- Academic Holds
CREATE TABLE academic_holds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  hold_type VARCHAR(50) NOT NULL, -- financial, academic, disciplinary, administrative
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  severity VARCHAR(20) NOT NULL, -- low, medium, high, critical
  resolution_steps JSONB NOT NULL, -- Array of steps to resolve
  deadline DATE,
  contact_info JSONB, -- {department, phone, email, office}
  prevents_registration BOOLEAN DEFAULT TRUE,
  prevents_transcript BOOLEAN DEFAULT FALSE,
  resolved BOOLEAN DEFAULT FALSE,
  resolved_date TIMESTAMP,
  resolved_by VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Notifications
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  type VARCHAR(50) NOT NULL, -- academic, financial, system, deadline, grade
  category VARCHAR(50) NOT NULL, -- info, warning, success, error
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  action_url VARCHAR(500),
  action_text VARCHAR(100),
  priority VARCHAR(20) DEFAULT 'normal', -- low, normal, high, urgent
  read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMP,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Push Notification Subscriptions
CREATE TABLE push_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  endpoint TEXT NOT NULL,
  p256dh_key TEXT NOT NULL,
  auth_key TEXT NOT NULL,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## Required API Endpoints

### 1. Dashboard APIs

```http
GET /api/dashboard
# Response: Current semester, GPA, credit progress, holds, upcoming deadlines

GET /api/dashboard/semester/current
# Response: Current semester details with academic calendar

GET /api/dashboard/credit-progress
# Response: Earned vs required credits, remaining requirements

GET /api/dashboard/gpa
# Response: Current GPA, trend, rank (if available)

GET /api/dashboard/holds
# Response: Active academic holds with resolution steps
```

### 2. Course Management APIs

```http
GET /api/courses/available
# Query params: semester_id, program_id, search, page, limit
# Response: Available courses for registration with enrollment status

GET /api/courses/registered
# Query params: semester_id, status
# Response: Student's registered courses

POST /api/courses/register
# Body: { course_offering_ids: [uuid] }
# Response: Registration confirmation with conflicts/errors

DELETE /api/courses/register/{course_offering_id}
# Response: Drop confirmation

GET /api/courses/{course_id}/details
# Response: Course details, prerequisites, description

GET /api/courses/conflicts
# Body: { course_offering_ids: [uuid] }
# Response: Time conflicts and prerequisite violations

POST /api/courses/waitlist/{course_offering_id}
# Response: Waitlist position and estimated enrollment chance
```

### 3. Timetable APIs

```http
GET /api/timetable
# Query params: semester_id, week_start_date
# Response: Weekly schedule with course sessions

GET /api/timetable/session/{session_id}
# Response: Session details with join links (if online)

GET /api/timetable/filters
# Response: Available filter options (course types, instructors)
```

### 4. Grades and Academic Progress APIs

```http
GET /api/grades/transcript
# Query params: semester_id (optional)
# Response: Complete transcript grouped by semester

GET /api/grades/gpa-trend
# Response: GPA history across semesters with trend analysis

GET /api/grades/course/{course_offering_id}
# Response: Detailed grades for specific course

GET /api/assignments
# Query params: course_offering_id, status, due_date_range
# Response: Assignments with submission status

GET /api/assignments/{assignment_id}
# Response: Assignment details with submission info

POST /api/assignments/{assignment_id}/submit
# Body: FormData with files and content
# Response: Submission confirmation

GET /api/assignments/{assignment_id}/submission
# Response: Student's submission details and feedback
```

### 5. Attendance APIs

```http
GET /api/attendance
# Query params: semester_id, course_offering_id
# Response: Attendance records with percentages

GET /api/attendance/summary
# Response: Attendance summary across all courses

GET /api/attendance/alerts
# Response: Courses with attendance below threshold
```

### 6. Profile Management APIs

```http
PUT /api/profile/personal
# Body: { phone, address, emergency_contact, etc. }
# Response: Updated profile confirmation

GET /api/profile/study-plan
# Response: 8-semester study plan with course progression

PUT /api/profile/avatar
# Body: FormData with image file
# Response: New avatar URL

GET /api/profile/academic-history
# Response: Complete academic journey and milestones
```

### 7. Curriculum and Program APIs

```http
GET /api/curriculum
# Query params: program_id, specialization_id
# Response: Complete curriculum roadmap

GET /api/curriculum/prerequisites/{course_id}
# Response: Prerequisite tree and completion status

GET /api/programs/{program_id}/requirements
# Response: Graduation requirements and progress
```

### 8. Notification APIs

```http
GET /api/notifications
# Query params: type, category, read, page, limit
# Response: Paginated notifications

PUT /api/notifications/{notification_id}/read
# Response: Mark as read confirmation

POST /api/notifications/push/subscribe
# Body: { subscription: PushSubscription }
# Response: Subscription confirmation

DELETE /api/notifications/push/unsubscribe
# Response: Unsubscription confirmation

GET /api/notifications/preferences
# Response: Notification preferences

PUT /api/notifications/preferences
# Body: { email_enabled, push_enabled, categories: [] }
# Response: Updated preferences
```

### 9. Academic Calendar APIs

```http
GET /api/calendar/academic
# Query params: year, semester_id
# Response: Academic calendar with important dates

GET /api/calendar/deadlines
# Query params: date_range, type
# Response: Upcoming deadlines and important dates

GET /api/calendar/events
# Response: Academic events, holidays, exam schedules
```

## API Response Standards

### Success Response Format

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  },
  "meta": {
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 100,
      "last_page": 5
    },
    "filters": {
      "applied": ["status:active"],
      "available": ["status", "semester", "program"]
    }
  }
}
```

### Error Response Format

```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "email": ["Email is required", "Email must be valid"],
    "course_id": ["Course not found"]
  },
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Authentication & Authorization

### JWT Token Structure

```json
{
  "sub": "student_uuid",
  "email": "<EMAIL>",
  "student_id": "STU123456",
  "role": "student",
  "campus_id": "campus_uuid",
  "program_id": "program_uuid",
  "permissions": ["view_grades", "register_courses", "view_transcript"],
  "iat": 1642234567,
  "exp": 1642320967
}
```

### Required Middleware

- Authentication verification
- Rate limiting (per endpoint)
- Request validation
- CORS handling
- Error logging and monitoring

## Database Indexes and Performance

### Critical Indexes

```sql
-- Student lookups
CREATE INDEX idx_students_email ON students(email);
CREATE INDEX idx_students_student_id ON students(student_id);
CREATE INDEX idx_students_program_campus ON students(program_id, campus_id);

-- Course registration queries
CREATE INDEX idx_course_registrations_student_semester ON course_registrations(student_id, course_offering_id);
CREATE INDEX idx_course_offerings_semester_status ON course_offerings(semester_id, status);

-- Academic records
CREATE INDEX idx_grades_student_semester ON course_registrations(student_id, final_grade_date);
CREATE INDEX idx_assignments_course_due_date ON assignments(course_offering_id, due_date);
CREATE INDEX idx_attendance_student_course ON attendance_records(student_id, course_offering_id);

-- Notifications and holds
CREATE INDEX idx_notifications_student_read ON notifications(student_id, read, created_at);
CREATE INDEX idx_holds_student_resolved ON academic_holds(student_id, resolved);
```

## Data Validation Rules

### Student Registration Validation

- Maximum credits per semester (default: 18)
- Prerequisite completion verification
- Time conflict detection
- Hold status check
- Registration period validation

### Grade Calculation Rules

- GPA calculation: (Sum of grade points × credits) / Total credits
- Academic standing thresholds:
  - Good Standing: GPA ≥ 2.0
  - Academic Probation: GPA < 2.0
  - Academic Suspension: GPA < 1.5 for 2 consecutive semesters

### Attendance Requirements

- Minimum attendance: 75% for exam eligibility
- Warning threshold: 80%
- Automatic notifications when below thresholds

## Security Requirements

### Data Protection

- Encrypt sensitive personal information
- Hash and salt passwords (if local auth implemented)
- Secure file upload validation
- SQL injection prevention
- XSS protection

### API Security

- Rate limiting: 100 requests/minute per user
- Input validation and sanitization
- HTTPS enforcement
- CORS policy configuration
- Request logging for audit trails

## Testing Requirements

### Unit Tests Required

- All API endpoints with success/error scenarios
- Database model validations
- Business logic functions (GPA calculation, conflict detection)
- Authentication and authorization flows

### Integration Tests Required

- Complete user workflows (registration, grade viewing)
- Database transaction integrity
- File upload and processing
- Push notification delivery

### Performance Requirements

- API response time: < 500ms for 95% of requests
- Database query optimization
- Caching strategy for frequently accessed data
- File upload handling up to 10MB per file

## Deployment Considerations

### Environment Variables

```env
# Database
DATABASE_URL=postgresql://user:pass@host:port/dbname
REDIS_URL=redis://host:port/db

# Authentication
JWT_SECRET=your-secret-key
JWT_EXPIRY=24h
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# File Storage
STORAGE_DRIVER=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_BUCKET=your-bucket-name

# Push Notifications
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# Email (for notifications)
MAIL_DRIVER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
```

### Infrastructure Requirements

- PostgreSQL 14+ with UUID extension
- Redis for caching and session storage
- File storage (AWS S3 or compatible)
- Background job processing (for notifications)
- SSL certificate for HTTPS
- CDN for static assets (optional)

This comprehensive backend specification should provide everything needed to implement the APIs that support your student portal frontend. Would you like me to elaborate on any specific section or add additional requirements?
