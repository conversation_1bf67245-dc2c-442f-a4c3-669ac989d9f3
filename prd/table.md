```sql
create table lectures
(
    id                             bigint unsigned auto_increment
        primary key,
    employee_id                    varchar(20)                                                                                                                                                  not null,
    title                          varchar(10)                                                                                                                                                  null,
    first_name                     varchar(100)                                                                                                                                                 not null,
    last_name                      varchar(100)                                                                                                                                                 not null,
    email                          varchar(255)                                                                                                                                                 not null,
    phone                          varchar(20)                                                                                                                                                  null,
    mobile_phone                   varchar(20)                                                                                                                                                  null,
    campus_id                      bigint unsigned                                                                                                                                              not null,
    department                     varchar(100)                                                                                                                                                 null,
    faculty                        varchar(100)                                                                                                                                                 null,
    specialization                 varchar(255)                                                                                                                                                 null,
    expertise_areas                json                                                                                                                                                         null,
    academic_rank                  enum ('lecturer', 'senior_lecturer', 'associate_professor', 'professor', 'emeritus_professor', 'visiting_lecturer', 'adjunct_professor') default 'lecturer'  not null,
    highest_degree                 varchar(50)                                                                                                                                                  null,
    degree_field                   varchar(255)                                                                                                                                                 null,
    alma_mater                     varchar(255)                                                                                                                                                 null,
    graduation_year                year                                                                                                                                                         null,
    hire_date                      date                                                                                                                                                         not null,
    contract_start_date            date                                                                                                                                                         null,
    contract_end_date              date                                                                                                                                                         null,
    employment_type                enum ('full_time', 'part_time', 'contract', 'visiting', 'emeritus')                                                                      default 'full_time' not null,
    employment_status              enum ('active', 'on_leave', 'sabbatical', 'retired', 'terminated', 'suspended')                                                          default 'active'    not null,
    preferred_teaching_days        json                                                                                                                                                         null,
    preferred_start_time           time                                                                                                                                                         null,
    preferred_end_time             time                                                                                                                                                         null,
    max_teaching_hours_per_week    int                                                                                                                                      default 40          not null,
    teaching_modalities            json                                                                                                                                                         null,
    office_address                 text                                                                                                                                                         null,
    office_phone                   varchar(20)                                                                                                                                                  null,
    emergency_contact_name         text                                                                                                                                                         null,
    emergency_contact_phone        varchar(20)                                                                                                                                                  null,
    emergency_contact_relationship varchar(50)                                                                                                                                                  null,
    biography                      text                                                                                                                                                         null,
    certifications                 json                                                                                                                                                         null,
    languages                      json                                                                                                                                                         null,
    hourly_rate                    decimal(10, 2)                                                                                                                                               null,
    salary                         decimal(12, 2)                                                                                                                                               null,
    is_active                      tinyint(1)                                                                                                                               default 1           not null,
    can_teach_online               tinyint(1)                                                                                                                               default 1           not null,
    is_available_for_assignment    tinyint(1)                                                                                                                               default 1           not null,
    notes                          text                                                                                                                                                         null,
    created_at                     timestamp                                                                                                                                                    null,
    updated_at                     timestamp                                                                                                                                                    null,
    deleted_at                     timestamp                                                                                                                                                    null,
    constraint lectures_email_unique
        unique (email),
    constraint lectures_employee_id_unique
        unique (employee_id),
    constraint lectures_campus_id_foreign
        foreign key (campus_id) references campuses (id)
)

create table course_offerings
(
    id                      bigint unsigned auto_increment
        primary key,
    semester_id             bigint unsigned                                                           not null,
    curriculum_unit_id      bigint unsigned                                                           not null,
    lecture_id              bigint unsigned                                                           null,
    section_code            varchar(10)                                                               null,
    max_capacity            int                                                   default 1000        not null,
    current_enrollment      int                                                   default 0           not null,
    waitlist_capacity       int                                                   default 10          not null,
    current_waitlist        int                                                   default 0           not null,
    delivery_mode           enum ('in_person', 'online', 'hybrid', 'blended')     default 'in_person' not null,
    schedule_days           json                                                                      null,
    schedule_time_start     time                                                                      null,
    schedule_time_end       time                                                                      null,
    location                varchar(255)                                                              null,
    is_active               tinyint(1)                                            default 1           not null,
    enrollment_status       enum ('open', 'closed', 'waitlist_only', 'cancelled') default 'open'      not null,
    registration_start_date date                                                                      null,
    registration_end_date   date                                                                      null,
    special_requirements    text                                                                      null,
    notes                   text                                                                      null,
    created_at              timestamp                                                                 null,
    updated_at              timestamp                                                                 null,
    deleted_at              timestamp                                                                 null,
    constraint unique_semester_unit_section
        unique (semester_id, curriculum_unit_id, section_code),
    constraint course_offerings_curriculum_unit_id_foreign
        foreign key (curriculum_unit_id) references curriculum_units (id)
            on delete cascade,
    constraint course_offerings_lecture_id_foreign
        foreign key (lecture_id) references lectures (id)
            on delete set null,
    constraint course_offerings_semester_id_foreign
        foreign key (semester_id) references semesters (id)
            on delete cascade
)

create table class_sessions
(
    id                           bigint unsigned auto_increment
        primary key,
    course_offering_id           bigint unsigned                                                                                                                                                            not null,
    room_id                      bigint unsigned                                                                                                                                                            null,
    room_booking_id              bigint unsigned                                                                                                                                                            null,
    instructor_id                bigint unsigned                                                                                                                                                            null,
    session_title                varchar(200)                                                                                                                                                               null,
    session_description          text                                                                                                                                                                       null,
    session_date                 date                                                                                                                                                                       not null,
    start_time                   time                                                                                                                                                                       not null,
    end_time                     time                                                                                                                                                                       not null,
    duration_minutes             int                                                                                                                                                                        null,
    session_type                 enum ('lecture', 'tutorial', 'practical', 'laboratory', 'seminar', 'workshop', 'exam', 'assessment', 'field_trip', 'guest_lecture', 'review', 'other') default 'lecture'   not null,
    delivery_mode                enum ('in_person', 'online', 'hybrid', 'blended')                                                                                                      default 'in_person' not null,
    status                       enum ('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed', 'moved')                                                                      default 'scheduled' not null,
    online_meeting_url           varchar(500)                                                                                                                                                               null,
    meeting_id                   varchar(100)                                                                                                                                                               null,
    meeting_password             varchar(100)                                                                                                                                                               null,
    learning_objectives          json                                                                                                                                                                       null,
    required_materials           json                                                                                                                                                                       null,
    topics_covered               json                                                                                                                                                                       null,
    attendance_required          tinyint(1)                                                                                                                                             default 1           not null,
    attendance_tracking_enabled  tinyint(1)                                                                                                                                             default 1           not null,
    expected_attendees           int                                                                                                                                                                        null,
    actual_attendees             int                                                                                                                                                                        null,
    attendance_percentage        decimal(5, 2)                                                                                                                                                              null,
    is_assessment                tinyint(1)                                                                                                                                             default 0           not null,
    assessment_weight            decimal(5, 2)                                                                                                                                                              null,
    assessment_duration_minutes  int                                                                                                                                                                        null,
    assessment_materials_allowed json                                                                                                                                                                       null,
    is_recurring                 tinyint(1)                                                                                                                                             default 0           not null,
    parent_session_id            bigint unsigned                                                                                                                                                            null,
    sequence_number              int                                                                                                                                                                        null,
    instructor_notes             text                                                                                                                                                                       null,
    admin_notes                  text                                                                                                                                                                       null,
    student_instructions         text                                                                                                                                                                       null,
    cancellation_reason          text                                                                                                                                                                       null,
    scheduled_at                 timestamp                                                                                                                                                                  null,
    started_at                   timestamp                                                                                                                                                                  null,
    ended_at                     timestamp                                                                                                                                                                  null,
    cancelled_at                 timestamp                                                                                                                                                                  null,
    created_at                   timestamp                                                                                                                                                                  null,
    updated_at                   timestamp                                                                                                                                                                  null,
    deleted_at                   timestamp                                                                                                                                                                  null,
    constraint unique_co_sequence
        unique (course_offering_id, sequence_number),
    constraint class_sessions_course_offering_id_foreign
        foreign key (course_offering_id) references course_offerings (id)
            on delete cascade,
    constraint class_sessions_instructor_id_foreign
        foreign key (instructor_id) references lectures (id)
            on delete set null,
    constraint class_sessions_parent_session_id_foreign
        foreign key (parent_session_id) references class_sessions (id)
            on delete set null,
    constraint class_sessions_room_booking_id_foreign
        foreign key (room_booking_id) references room_bookings (id)
            on delete set null,
    constraint class_sessions_room_id_foreign
        foreign key (room_id) references rooms (id)
            on delete set null,
    constraint check_sessions_actual_attendees_valid
        check ((`actual_attendees` is null) or (`actual_attendees` >= 0)),
    constraint check_sessions_assessment_weight
        check ((`assessment_weight` is null) or ((`assessment_weight` >= 0) and (`assessment_weight` <= 100))),
    constraint check_sessions_attendance_percentage
        check ((`attendance_percentage` is null) or
               ((`attendance_percentage` >= 0) and (`attendance_percentage` <= 100))),
    constraint check_sessions_expected_attendees_positive
        check ((`expected_attendees` is null) or (`expected_attendees` > 0)),
    constraint check_sessions_times
        check (`start_time` < `end_time`)
)

create table attendances
(
    id                     bigint unsigned auto_increment
        primary key,
    class_session_id       bigint unsigned                                                                                                       not null,
    student_id             bigint unsigned                                                                                                       not null,
    recorded_by_lecture_id bigint unsigned                                                                                                       null,
    status                 enum ('present', 'absent', 'late', 'excused', 'partial', 'medical_leave', 'official_leave')          default 'absent' not null,
    check_in_time          timestamp                                                                                                             null,
    check_out_time         timestamp                                                                                                             null,
    minutes_late           int                                                                                                  default 0        not null,
    minutes_present        int                                                                                                                   null,
    recording_method       enum ('manual', 'qr_code', 'rfid', 'biometric', 'mobile_app', 'online_participation', 'auto_system') default 'manual' not null,
    notes                  text                                                                                                                  null,
    excuse_reason          text                                                                                                                  null,
    excuse_document_path   varchar(500)                                                                                                          null,
    participation_level    enum ('excellent', 'good', 'average', 'poor', 'none')                                                                 null,
    participation_score    decimal(3, 1)                                                                                                         null,
    participation_notes    text                                                                                                                  null,
    is_verified            tinyint(1)                                                                                           default 0        not null,
    affects_grade          tinyint(1)                                                                                           default 1        not null,
    is_makeup_allowed      tinyint(1)                                                                                           default 0        not null,
    verified_at            timestamp                                                                                                             null,
    verified_by_lecture_id bigint unsigned                                                                                                       null,
    batch_id               varchar(50)                                                                                                           null,
    device_info            json                                                                                                                  null,
    ip_address             varchar(45)                                                                                                           null,
    latitude               decimal(10, 8)                                                                                                        null,
    longitude              decimal(11, 8)                                                                                                        null,
    created_at             timestamp                                                                                                             null,
    updated_at             timestamp                                                                                                             null,
    deleted_at             timestamp                                                                                                             null,
    constraint unique_session_student_attendance
        unique (class_session_id, student_id),
    constraint attendances_class_session_id_foreign
        foreign key (class_session_id) references class_sessions (id)
            on delete cascade,
    constraint attendances_recorded_by_lecture_id_foreign
        foreign key (recorded_by_lecture_id) references lectures (id)
            on delete set null,
    constraint attendances_student_id_foreign
        foreign key (student_id) references students (id)
            on delete cascade,
    constraint attendances_verified_by_lecture_id_foreign
        foreign key (verified_by_lecture_id) references lectures (id)
            on delete set null,
    constraint check_attendances_minutes_late_positive
        check (`minutes_late` >= 0),
    constraint check_attendances_minutes_present_positive
        check ((`minutes_present` is null) or (`minutes_present` >= 0)),
    constraint check_attendances_participation_score
        check ((`participation_score` is null) or ((`participation_score` >= 0) and (`participation_score` <= 10)))
)
```
