# Backend API Requirements for Student Portal

## Overview

This document outlines the complete backend API requirements, database models, and technical specifications needed to support the Student Portal frontend implementation. The backend should provide RESTful APIs with proper authentication, validation, and error handling.

## Current Implementation Status

✅ **Completed APIs:**

- `POST /auth/login/google` - Google OAuth authentication
- `GET /auth/me` - Get current user profile
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Token refresh

## Required Database Models

### 1. Core Academic Structure

```sql
-- Academic Program Management
CREATE TABLE programs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE specializations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  program_id UUID REFERENCES programs(id),
  name VA<PERSON>HAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE semesters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE curriculum_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  program_id UUID REFERENCES programs(id),
  specialization_id UUID REFERENCES specializations(id),
  semester_id UUID REFERENCES semesters(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE units (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(20) NOT NULL,
  name VARCHAR(255) NOT NULL,
  credit_points INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE curriculum_units (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  curriculum_version_id UUID REFERENCES curriculum_versions(id),
  unit_id UUID REFERENCES units(id),
  semester_id UUID REFERENCES semesters(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Facilities and Staff

```sql
-- Facilities
CREATE TABLE campuses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  address TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE buildings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campus_id UUID REFERENCES campuses(id),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campus_id UUID REFERENCES campuses(id),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Staff
CREATE TABLE lectures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id VARCHAR(50) NOT NULL,
  email VARCHAR(255) NOT NULL,
  campus_id UUID REFERENCES campuses(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Course Delivery and Registration

```sql
-- Course Offerings
CREATE TABLE course_offerings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  curriculum_unit_id UUID REFERENCES curriculum_units(id),
  semester_id UUID REFERENCES semesters(id),
  lecture_id UUID REFERENCES lectures(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE class_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_offering_id UUID REFERENCES course_offerings(id),
  instructor_id UUID REFERENCES lectures(id),
  room_id UUID REFERENCES rooms(id),
  room_booking_id UUID, -- References room_bookings if implemented
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Student Management
CREATE TABLE students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id VARCHAR(50) NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  program_id UUID REFERENCES programs(id),
  specialization_id UUID REFERENCES specializations(id),
  campus_id UUID REFERENCES campuses(id),
  curriculum_version_id UUID REFERENCES curriculum_versions(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE course_registrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  semester_id UUID REFERENCES semesters(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  semester_id UUID REFERENCES semesters(id),
  curriculum_version_id UUID REFERENCES curriculum_versions(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 4. Academic Records and Assessment

```sql
-- Academic Records
CREATE TABLE academic_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  semester_id UUID REFERENCES semesters(id),
  unit_id UUID REFERENCES units(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE academic_standings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  semester_id UUID REFERENCES semesters(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE gpa_calculations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  semester_id UUID REFERENCES semesters(id),
  program_id UUID REFERENCES programs(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Assessment System
CREATE TABLE syllabus (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  curriculum_unit_id UUID REFERENCES curriculum_units(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE assessment_components (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  syllabus_id UUID REFERENCES syllabus(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE assessment_component_details (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  component_id UUID REFERENCES assessment_components(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE assessment_component_detail_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  assessment_component_detail_id UUID REFERENCES assessment_component_details(id),
  student_id UUID REFERENCES students(id),
  course_offering_id UUID REFERENCES course_offerings(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Attendance
CREATE TABLE attendances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_session_id UUID REFERENCES class_sessions(id),
  student_id UUID REFERENCES students(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Academic Support and System Management

```sql
-- Academic Holds
CREATE TABLE academic_holds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  placed_by_user_id UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE program_change_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  from_program_id UUID REFERENCES programs(id),
  to_program_id UUID REFERENCES programs(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- System Management
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  code VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE campus_user_roles (
  user_id UUID REFERENCES users(id),
  campus_id UUID REFERENCES campuses(id),
  role_id UUID REFERENCES roles(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  PRIMARY KEY (user_id, campus_id, role_id)
);
```

## Required API Endpoints

### 1. Dashboard APIs

```http
GET /api/dashboard
# Response: Current semester, GPA calculations, credit progress, academic holds, upcoming assessments
# Includes: Semester, GPACalculation, CurriculumUnit progress, AcademicHold[], AssessmentComponentDetail[]

GET /api/dashboard/semester/current
# Response: Current semester details with enrollment status
# Includes: Semester with Enrollment data

GET /api/dashboard/credit-progress
# Response: Earned vs required credits based on CurriculumVersion and AcademicRecord
# Includes: CurriculumUnit[] remaining requirements, completion percentage

GET /api/dashboard/gpa
# Response: GPACalculation with semester and cumulative GPA, trend analysis
# Includes: GPACalculation with Program and Semester relationships

GET /api/dashboard/holds
# Response: Active AcademicHold entities with User (placed_by) information
# Includes: AcademicHold[] with resolution details
```

### 2. Course Registration APIs

```http
GET /api/course-offerings/available/{semester_id}
# Query params: curriculum_version_id, search, page, limit
# Response: Available CourseOffering entities with CurriculumUnit, Lecturer, and ClassSession data
# Includes: CourseOffering[] with enrollment capacity and conflict information

GET /api/course-registrations
# Query params: semester_id, status
# Response: Student's CourseRegistration entities with CourseOffering details
# Includes: CourseRegistration[] with CourseOffering, Semester relationships

POST /api/course-registrations
# Body: { course_offering_id: uuid, semester_id: uuid }
# Response: CourseRegistration confirmation with conflict detection
# Validates: ClassSession time conflicts, prerequisite requirements

DELETE /api/course-registrations/{course_offering_id}
# Response: CourseRegistration removal confirmation

GET /api/course-offerings/{course_offering_id}/details
# Response: CourseOffering with CurriculumUnit, Unit, ClassSession, and Lecturer details
# Includes: Complete course offering information with schedule

GET /api/course-offerings/conflicts
# Body: { course_offering_ids: [uuid] }
# Response: ClassSession time conflicts and CurriculumUnit prerequisite violations
# Includes: Detailed conflict analysis with affected sessions

GET /api/curriculum-units/{curriculum_unit_id}/prerequisites
# Response: Unit prerequisite tree with completion status from AcademicRecord
# Includes: Unit relationships and student completion status
```

### 3. Timetable APIs

```http
GET /api/timetable
# Query params: semester_id, week_start_date
# Response: ClassSession entities for student's registered CourseOffering
# Includes: ClassSession[] with CourseOffering, Room, and Lecturer information

GET /api/class-sessions/{class_session_id}
# Response: ClassSession details with Room, CourseOffering, and online meeting information
# Includes: Complete session details with location and instructor data

GET /api/timetable/filters
# Response: Available filter options based on student's CourseOffering entities
# Includes: CourseOffering types, Lecturer names, Room locations
```

### 4. Grades and Academic Progress APIs

```http
GET /api/grades
# Query params: semester_id (optional)
# Response: AcademicRecord entities grouped by Semester with Unit and CourseOffering details
# Includes: AcademicRecord[], AssessmentScore[], GPACalculation[], semester summaries

GET /api/grades/gpa-trend
# Response: GPACalculation history across Semester entities with trend analysis
# Includes: GPACalculation[] with Semester and Program relationships

GET /api/grades/course-offering/{course_offering_id}
# Response: AcademicRecord and AssessmentScore entities for specific CourseOffering
# Includes: Detailed assessment breakdown with Syllabus and AssessmentComponent data

GET /api/assessments
# Query params: course_offering_id, semester_id, status
# Response: AssessmentComponentDetail entities with submission status
# Includes: AssessmentComponentDetail[] with AssessmentScore relationships

GET /api/assessments/{assessment_component_detail_id}
# Response: AssessmentComponentDetail with AssessmentComponent and Syllabus information
# Includes: Complete assessment details with scoring rubric

GET /api/assessment-scores
# Query params: course_offering_id, assessment_component_detail_id
# Response: AssessmentScore entities for student with CourseOffering context
# Includes: AssessmentScore[] with grading details and feedback
```

### 5. Attendance APIs

```http
GET /api/attendance
# Query params: semester_id, course_offering_id
# Response: Attendance records with percentages

GET /api/attendance/summary
# Response: Attendance summary across all courses

GET /api/attendance/alerts
# Response: Courses with attendance below threshold
```

### 6. Profile Management APIs

```http
PUT /api/profile/personal
# Body: { phone, address, emergency_contact, etc. }
# Response: Updated profile confirmation

GET /api/profile/study-plan
# Response: 8-semester study plan with course progression

PUT /api/profile/avatar
# Body: FormData with image file
# Response: New avatar URL

GET /api/profile/academic-history
# Response: Complete academic journey and milestones
```

### 7. Curriculum and Program APIs

```http
GET /api/curriculum
# Query params: program_id, specialization_id
# Response: Complete curriculum roadmap

GET /api/curriculum/prerequisites/{course_id}
# Response: Prerequisite tree and completion status

GET /api/programs/{program_id}/requirements
# Response: Graduation requirements and progress
```

### 8. Notification APIs

```http
GET /api/notifications
# Query params: type, category, read, page, limit
# Response: Paginated notifications

PUT /api/notifications/{notification_id}/read
# Response: Mark as read confirmation

POST /api/notifications/push/subscribe
# Body: { subscription: PushSubscription }
# Response: Subscription confirmation

DELETE /api/notifications/push/unsubscribe
# Response: Unsubscription confirmation

GET /api/notifications/preferences
# Response: Notification preferences

PUT /api/notifications/preferences
# Body: { email_enabled, push_enabled, categories: [] }
# Response: Updated preferences
```

### 9. Academic Calendar APIs

```http
GET /api/calendar/academic
# Query params: year, semester_id
# Response: Academic calendar with important dates

GET /api/calendar/deadlines
# Query params: date_range, type
# Response: Upcoming deadlines and important dates

GET /api/calendar/events
# Response: Academic events, holidays, exam schedules
```

## API Response Standards

### Success Response Format

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  },
  "meta": {
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 100,
      "last_page": 5
    },
    "filters": {
      "applied": ["status:active"],
      "available": ["status", "semester", "program"]
    }
  }
}
```

### Error Response Format

```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "email": ["Email is required", "Email must be valid"],
    "course_id": ["Course not found"]
  },
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Authentication & Authorization

### JWT Token Structure

```json
{
  "sub": "student_uuid",
  "email": "<EMAIL>",
  "student_id": "STU123456",
  "role": "student",
  "campus_id": "campus_uuid",
  "program_id": "program_uuid",
  "permissions": ["view_grades", "register_courses", "view_transcript"],
  "iat": 1642234567,
  "exp": 1642320967
}
```

### Required Middleware

- Authentication verification
- Rate limiting (per endpoint)
- Request validation
- CORS handling
- Error logging and monitoring

## Database Indexes and Performance

### Critical Indexes

```sql
-- Student lookups
CREATE INDEX idx_students_email ON students(email);
CREATE INDEX idx_students_student_id ON students(student_id);
CREATE INDEX idx_students_program_campus ON students(program_id, campus_id);

-- Course registration queries
CREATE INDEX idx_course_registrations_student_semester ON course_registrations(student_id, course_offering_id);
CREATE INDEX idx_course_offerings_semester_status ON course_offerings(semester_id, status);

-- Academic records
CREATE INDEX idx_grades_student_semester ON course_registrations(student_id, final_grade_date);
CREATE INDEX idx_assignments_course_due_date ON assignments(course_offering_id, due_date);
CREATE INDEX idx_attendance_student_course ON attendance_records(student_id, course_offering_id);

-- Notifications and holds
CREATE INDEX idx_notifications_student_read ON notifications(student_id, read, created_at);
CREATE INDEX idx_holds_student_resolved ON academic_holds(student_id, resolved);
```

## Data Validation Rules

### Student Registration Validation

- Maximum credits per semester (default: 18)
- Prerequisite completion verification
- Time conflict detection
- Hold status check
- Registration period validation

### Grade Calculation Rules

- GPA calculation: (Sum of grade points × credits) / Total credits
- Academic standing thresholds:
  - Good Standing: GPA ≥ 2.0
  - Academic Probation: GPA < 2.0
  - Academic Suspension: GPA < 1.5 for 2 consecutive semesters

### Attendance Requirements

- Minimum attendance: 75% for exam eligibility
- Warning threshold: 80%
- Automatic notifications when below thresholds

## Security Requirements

### Data Protection

- Encrypt sensitive personal information
- Hash and salt passwords (if local auth implemented)
- Secure file upload validation
- SQL injection prevention
- XSS protection

### API Security

- Rate limiting: 100 requests/minute per user
- Input validation and sanitization
- HTTPS enforcement
- CORS policy configuration
- Request logging for audit trails

## Testing Requirements

### Unit Tests Required

- All API endpoints with success/error scenarios
- Database model validations
- Business logic functions (GPA calculation, conflict detection)
- Authentication and authorization flows

### Integration Tests Required

- Complete user workflows (registration, grade viewing)
- Database transaction integrity
- File upload and processing
- Push notification delivery

### Performance Requirements

- API response time: < 500ms for 95% of requests
- Database query optimization
- Caching strategy for frequently accessed data
- File upload handling up to 10MB per file

## Deployment Considerations

### Environment Variables

```env
# Database
DATABASE_URL=postgresql://user:pass@host:port/dbname
REDIS_URL=redis://host:port/db

# Authentication
JWT_SECRET=your-secret-key
JWT_EXPIRY=24h
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# File Storage
STORAGE_DRIVER=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_BUCKET=your-bucket-name

# Push Notifications
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# Email (for notifications)
MAIL_DRIVER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
```

### Infrastructure Requirements

- PostgreSQL 14+ with UUID extension
- Redis for caching and session storage
- File storage (AWS S3 or compatible)
- Background job processing (for notifications)
- SSL certificate for HTTPS
- CDN for static assets (optional)

This comprehensive backend specification should provide everything needed to implement the APIs that support your student portal frontend. Would you like me to elaborate on any specific section or add additional requirements?
