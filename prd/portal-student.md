# Student Portal PRD

## 1. General Design Principles

| Topic                        | UX / UI Guideline                                                                                                                                                    |
| ---------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **User Goals**               | • “Know exactly where I am and what I should do next” • Self-service – reduce dependency on academic staff.                                                          |
| **Information Hierarchy**    | 3 levels: _Overview (Dashboard)_ → _List View_ → _Detail / Action_.                                                                                                  |
| **Feedback & States**        | • Always notify students of the result of an action (toast, badge). • Use loading skeletons ≤ 300ms; if >2s, add spinners with microcopy (e.g., “Fetching grades…”). |
| **Color & State Indicators** | Blue/Indigo = Info; Yellow = Soft warnings; Red = Academic alert. Use a minimal 6–8 color palette with WCAG 2.1 AA contrast.                                         |
| **Mobile-first**             | Mobile = ~70% access; vertical layout, swipe tabs; desktop keeps persistent sidebar.                                                                                 |
| **Accessibility (a11y)**     | Logical tab order, ARIA-labels for icon buttons, number hotkeys (1–9) for menu.                                                                                      |

---

## 2. Information Architecture & Navigation

```pgsql

Student Portal
├─ Dashboard
├─ Profile
│  ├─ Personal Info
│  └─ Study Plan
├─ Timetable
├─ Course Registration
│  ├─ Open Courses
│  └─ My Registrations
├─ Grades
│  ├─ Transcript
│  └─ GPA Trend
├─ Assessment
├─ Attendance
├─ Holds
├─ Curriculum
└─ Notifications
```

_Sidebar_ remains fixed on desktop (>1280px). On mobile, it's hidden behind a hamburger menu, with a bottom nav bar for 4 key icons: Home, Timetable, Registration, Notifications.

---

## 3. Functional Flows & UX Details

### 3.1 Dashboard – “Control tower”

| Section              | Behavior                                                   | UX Keypoints                                                                                       |
| -------------------- | ---------------------------------------------------------- | -------------------------------------------------------------------------------------------------- |
| **Current Semester** | Show name & date range                                     | Horizontal card; click = modal with academic calendar.                                             |
| **Credit Progress**  | Progress bar / donut: Earned vs Graduation Required        | Show “Missing X credits”; hover/tap = tooltip with what’s left (e.g., “2 core courses remaining”). |
| **Current GPA**      | Numeric GPA + color badge (≥3.2 green, 2–3 yellow, <2 red) | Click = go to GPA Trend page.                                                                      |
| **Academic Holds**   | List current holds                                         | If >1, show badge count + summary; subtle red pulse once to draw attention (no flashing loop).     |

### 3.2 Profile

| Sub-page          | Flow                                                                                    | Validation / UX                                                            |
| ----------------- | --------------------------------------------------------------------------------------- | -------------------------------------------------------------------------- |
| **Personal Info** | ① View → ② Tap "Edit" → ③ Inline form                                                   | Auto-save after 1s pause; show green check “Saved”; regex for email/phone. |
| **Study Plan**    | Grid of 8 semesters; each with subject avatars (filled = passed, outline = unattempted) | Tap = bottom-sheet modal (credit, prerequisites, etc.).                    |

### 3.3 Timetable

| Scenario                 | UX Detail                                           | Design Considerations                                                         |
| ------------------------ | --------------------------------------------------- | ----------------------------------------------------------------------------- |
| **Weekly View**          | Horizontal scroll by week; top bar = “Today” button | Sticky headers for day/weekday; color-code per Unit for recognition.          |
| **Filter class types**   | Lecture / Lab / Exam                                | Segmented control; preserve height to avoid layout shifts.                    |
| **View session details** | Tap slot → slide-in panel                           | Show “Join Zoom” only 10 min before session starts; hide for offline classes. |

### 3.4 Course Registration

| Flow                     | State Management                                                        | UX Tips                                                                         |
| ------------------------ | ----------------------------------------------------------------------- | ------------------------------------------------------------------------------- |
| **Explore Open Courses** | Filter: Unit Code, Lecturer, Conflict checker                           | Badge “Full 0/50”; gray out “Add” if time conflict exists.                      |
| **Add course**           | Tap “Add” → move item to right “My List” panel instantly                | Optimistic UI with check animation; rollback on API error (toast).              |
| **Confirm registration** | Tap “Submit” → confirmation modal showing total credits & fees (if any) | For overloads, show red warning + button “Request Approval” linking to support. |

### 3.5 Grades & GPA

| Feature        | UX Details                                          | Edge Cases                                             |
| -------------- | --------------------------------------------------- | ------------------------------------------------------ |
| **Transcript** | Grouped by Semester; sticky subject header rows     | Empty state: “No grades available yet” + illustration. |
| **GPA Trend**  | Line chart across semesters; hover shows GPA & rank | Upcoming/incomplete semester shown as dotted line.     |

### 3.6 Assessment

| Focus                                                       | UX Notes                                                                                                   |
| ----------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------- |
| Dashboard per course: Assignment cards with % progress ring | Countdown (e.g., 00:12:59) in red when <24h left; support drag-drop upload; show progress bar within card. |

### 3.7 Attendance

| UI                                    | Logic                                                | Alerts                                |
| ------------------------------------- | ---------------------------------------------------- | ------------------------------------- |
| Bar chart for each class attendance % | If <75%, mark in red with tooltip “Risk of exam ban” | Push notification if <80% & dropping. |

### 3.8 Holds

| Display                                     | Microcopy                                                                  |
| ------------------------------------------- | -------------------------------------------------------------------------- |
| List + severity icons (info/warning/danger) | “Visit Finance Office (Desk 3) to settle tuition hold before 15-Aug-2025.” |

### 3.9 Curriculum

| Experience                                                                                        | UX Insight                                                                               |
| ------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------- |
| Curriculum roadmap: 4-column grid (Year 1–4), course cards toggle based on filter (Core/Elective) | Hover (desktop) = show prerequisites; long-press (mobile); crossed code = passed course. |

### 3.10 Notifications

| Layout                                            | Filtering                                        |
| ------------------------------------------------- | ------------------------------------------------ |
| Chat-like feed (source avatar: System / Lecturer) | Tabs: All / Academic / Finance; pull to refresh. |

---

## 4. UI States (Empty, Error, Success)

| Type        | Best Practice                                                                                                                         |
| ----------- | ------------------------------------------------------------------------------------------------------------------------------------- |
| **Empty**   | Custom illustration + next step suggestion, e.g., “No courses registered. Explore open courses?”                                      |
| **Error**   | Toast at bottom-right; actionable text: “Server unavailable. Retry?” (with retry button).                                             |
| **Success** | Green toast + check icon; auto-dismiss after 3s. For major actions (e.g. course submission), show a receipt page with “Download PDF”. |

---

## 5. Multichannel & Continuity

| Context                       | Feature                                                                                                               |
| ----------------------------- | --------------------------------------------------------------------------------------------------------------------- |
| **Email + In-app**            | Send email for critical events (new grade, new hold) along with app notification.                                     |
| **Deep linking**              | Allow URLs like `/assessment/12345` to open specific tasks (for sharing or reminders).                                |
| **Progressive Web App (PWA)** | Allow mobile install shortcuts; cache timetable offline for 7 days.                                                   |
| **Help center**               | Floating “?” button that opens FAQ based on context (e.g., show “How to drop a course?” when on registration screen). |

---

## 6. Usability Testing & Metrics

| Stage               | KPI / Method                                                        |
| ------------------- | ------------------------------------------------------------------- |
| 2 weeks post-launch | SUS Score ≥ 75; time to complete course registration ≤ 3 minutes    |
| Continuous          | Heatmaps for Timetable & Registration; 4xx / 5xx error rates < 0.5% |
| Quarterly           | NPS surveys; analytics on course drops and failure rates            |

---

## 7. Conclusion

- The portal's **core business logic** centers on: _Course Registration → Learning Progress → Results & Alerts_.
- **UX priority**: clarity (via dashboard), frictionless actions (e.g. add course in one tap), and decision support (progress, GPA, holds).
- **Feedback and notification strategy** ensure students never miss deadlines.
