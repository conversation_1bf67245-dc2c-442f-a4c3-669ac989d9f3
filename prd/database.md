<!-- Academic Program Management -->

## Table: programs

- fields: id, name, code, description
- relationships: hasMany specializations, curriculum_versions, graduation_requirements, students, academic_records, program_change_requests

## Table: specializations

- fields: id, program_id, name, code
- relationships: belongsTo program, hasMany curriculum_versions, graduation_requirements, students

## Table: curriculum_versions

- fields: id, program_id, specialization_id, semester_id
- relationships: belongsTo program, specialization, semester; hasMany curriculum_units, enrollments, students

## Table: curriculum_units

- fields: id, curriculum_version_id, unit_id, semester_id
- relationships: belongsTo curriculum_version, unit, semester; hasMany syllabus

## Table: units

- fields: id, code, name, credit_points
- relationships: hasMany curriculum_units, academic_records, unit_prerequisite_groups, equivalent_units

<!-- Students & Admissions -->

## Table: students

- fields: id, student_id, full_name, email, program_id, specialization_id, campus_id, curriculum_version_id
- relationships: belongsTo program, specialization, campus, curriculum_version; hasMany academic_records, course_registrations, enrollments, academic_standings, academic_holds, gpa_calculations, program_change_requests

<!-- Facilities -->

## Table: campuses

- fields: id, name, code, address
- relationships: hasMany buildings, rooms, students, lectures

## Table: buildings

- fields: id, campus_id, name, code
- relationships: belongsTo campus

## Table: rooms

- fields: id, campus_id, name, code
- relationships: belongsTo campus; hasMany room_bookings, class_sessions

<!-- Lecturers -->

## Table: lectures

- fields: id, employee_id, email, campus_id
- relationships: belongsTo campus; hasMany course_offerings, class_sessions, academic_records, assessment_scores

<!-- Courses & Class Sessions -->

## Table: course_offerings

- fields: id, curriculum_unit_id, semester_id, lecture_id
- relationships: belongsTo curriculum_unit, semester, lecture; hasMany class_sessions, course_registrations, academic_records, assessment_scores

## Table: class_sessions

- fields: id, course_offering_id, instructor_id, room_id, room_booking_id
- relationships: belongsTo course_offering, room, room_booking, instructor (lecture); hasMany attendances

## Table: attendances

- fields: id, class_session_id, student_id
- relationships: belongsTo class_session, student

<!-- Course Registration & Academic Records -->

## Table: course_registrations

- fields: id, student_id, course_offering_id, semester_id
- relationships: belongsTo student, course_offering, semester

## Table: academic_records

- fields: id, student_id, course_offering_id, semester_id, unit_id
- relationships: belongsTo student, course_offering, semester, unit, instructor, program

## Table: enrollments

- fields: id, student_id, semester_id, curriculum_version_id
- relationships: belongsTo student, semester, curriculum_version

## Table: academic_standings

- fields: id, student_id, semester_id
- relationships: belongsTo student, semester

## Table: gpa_calculations

- fields: id, student_id, semester_id, program_id
- relationships: belongsTo student, semester, program

<!-- Assessments & Components -->

## Table: syllabus

- fields: id, curriculum_unit_id
- relationships: belongsTo curriculum_unit; hasMany assessment_components

## Table: assessment_components

- fields: id, syllabus_id
- relationships: belongsTo syllabus; hasMany assessment_component_details

## Table: assessment_component_details

- fields: id, component_id
- relationships: belongsTo assessment_component; hasMany assessment_component_detail_scores

## Table: assessment_component_detail_scores

- fields: id, assessment_component_detail_id, student_id, course_offering_id
- relationships: belongsTo assessment_component_detail, student, course_offering, lecture (graded_by)

<!-- Academic Holds & Program Changes -->

## Table: academic_holds

- fields: id, student_id, placed_by_user_id
- relationships: belongsTo student, placed_by (user)

## Table: program_change_requests

- fields: id, student_id, from_program_id, to_program_id
- relationships: belongsTo student, from/to program, from/to specialization

<!-- Supporting Tables -->

## Table: semesters

- fields: id, name, code
- relationships: hasMany curriculum_versions, course_offerings, enrollments, academic_standings, gpa_calculations

## Table: users

- fields: id, name, email
- relationships: hasMany academic_holds, campus_user_roles

## Table: roles

- fields: id, name, code
- relationships: hasMany campus_user_roles, role_permissions

## Table: permissions

- fields: id, name, code
- relationships: hasMany role_permissions

## Table: campus_user_roles

- fields: user_id, campus_id, role_id
- relationships: belongsTo user, campus, role
