## Design Goals

| Requirement                      | Description                                                                                                       |
| -------------------------------- | ----------------------------------------------------------------------------------------------------------------- |
| 1. One Vue 3 codebase            | A single `/portal` frontend app for both students and lecturers.                                                  |
| 2. Role-based layout and routing | After login, the app determines role (student or lecturer) → renders corresponding layout and routes.             |
| 3. API separated by guard        | Laravel API prefix: `api/student/*` and `api/lecturer/*`, each with separate guard (e.g. `auth:sanctum,student`). |

---

## Shared Frontend Folder Structure

```
src/
├─ api/
│  ├─ student/
│  └─ lecturer/
├─ layouts/
│  ├─ StudentLayout.vue
│  └─ LecturerLayout.vue
├─ views/
│  ├─ student/
│  │  └─ Dashboard, Grades, Courses...
│  └─ lecturer/
│     └─ Dashboard, Teaching, Grading...
├─ stores/
│  ├─ useAuthStore.ts      // shared
│  ├─ useStudentStore.ts   // role-specific
│  └─ useLecturerStore.ts
├─ router/
│  └─ index.ts
├─ App.vue
└─ main.ts

```

---

## Role-Based Router Setup

```ts
{
  path: '/student',
  component: StudentLayout,
  meta: { role: 'student' },
  children: [/* student routes */],
},
{
  path: '/lecturer',
  component: LecturerLayout,
  meta: { role: 'lecturer' },
  children: [/* lecturer routes */],
}

```

> Inside route guards: check store.auth.role, redirect if user tries to access the wrong layout.

---

## 🧑‍🏫 **Lecturer Portal – UX/UI Specification**

### Sidebar Structure (Lecturer Role):

```
Lecturer Portal
├─ Dashboard
├─ Teaching
│  ├─ My Courses
│  └─ Timetable
├─ Grading
│  ├─ Assignments
│  └─ Gradebook
├─ Attendance
├─ Feedback
├─ Syllabus & Materials
├─ Student List
└─ Notifications

```

---

### Feature Breakdown

| Section                  | UX / Business Logic                                                                                                  |
| ------------------------ | -------------------------------------------------------------------------------------------------------------------- |
| **Dashboard**            | Show classes teaching, number of assignments graded, students flagged (low GPA, absences), and recent alerts.        |
| **My Courses**           | List of `course_offerings` being taught. Click → manage syllabus, assessments, materials, student list.              |
| **Timetable**            | Personal teaching schedule (based on `class_sessions`). Click session to open attendance, materials, Zoom, etc.      |
| **Assignments**          | Create tasks → upload instructions, set deadline, attach rubric. View student submissions, grade, and give feedback. |
| **Gradebook**            | Grid of students × components. Edit grades, import/export Excel or PDF, calculate final scores.                      |
| **Attendance**           | Mark attendance quickly by selecting multiple students (present/absent/late). Data auto-syncs with academic records. |
| **Feedback**             | View anonymous or named feedback from students. Text or survey format.                                               |
| **Syllabus & Materials** | Manage syllabus, upload lecture slides, readings. Allow students to view/download post-session.                      |
| **Student List**         | Registered students per class: avatar, email, GPA, attendance %, academic warnings.                                  |
| **Notifications**        | Send messages to specific students or classes. Track send history.                                                   |

---

## Reusable Components Across Roles

| Shared Component               | Purpose                                                                 |
| ------------------------------ | ----------------------------------------------------------------------- |
| **TimetableView**              | Unified calendar for both student and lecturer views.                   |
| **CourseCard**                 | Used in both student’s registered courses and lecturer’s teaching list. |
| **AssessmentForm / UploadBox** | Shared UI for uploading assignments and submitting answers.             |
| **AttendanceTable**            | Same table layout; students = view-only, lecturers = editable.          |
| **GPAChart / ProgressChart**   | Used by students for GPA, lecturers for class performance insights.     |
| **NotificationCenter**         | One component for viewing/sending messages; adapted per role.           |

---

## API Structure (Laravel)

| Role     | API Prefix        | Example                                               |
| -------- | ----------------- | ----------------------------------------------------- |
| Student  | `/api/student/*`  | `/api/student/timetable`, `/api/student/grades`       |
| Lecturer | `/api/lecturer/*` | `/api/lecturer/teaching`, `/api/lecturer/assignments` |

---

## Auth Strategy

| Mechanism               | Description                                                              |
| ----------------------- | ------------------------------------------------------------------------ |
| **Shared login screen** | After login, detect role → redirect to `/student` or `/lecturer` layout. |
| **Auth Store**          | `useAuthStore` stores `role`, `user`, `token`, `expires`, etc.           |
| **Route Guard**         | Before each route, check if `role === meta.role`; otherwise redirect.    |
| **Logout**              | Clears session, resets app state, returns to login.                      |

---

## UI/UX Dynamic Menu & Access Control

```
ts
CopyEdit
const role = useAuthStore().role

const menus = computed(() =>
  role === 'student'
    ? studentMenu
    : lecturerMenu
)

```

Menus, page titles, top bar actions, and floating buttons are adapted dynamically per role.

---

## Summary

- **Single codebase** streamlines maintenance and supports dynamic switching by role.
- **Clear separation of routing/layout/store/api by role** ensures security and scalability.
- **Reusable UI components** improve consistency and development speed.
- UI/UX design focuses on _role-specific context_, avoiding redundant menus or features.
