### Refactoring Recommendations as Tasks

1. **Remove legacy `/components/` folder if fully migrated to role-based directories.**

   > Ensure all layout and dashboard components are placed under student/components/ and lecturer/components/. Remove unused shared folders like components/layout/ or components/dashboard/.

2. **Split shared types into `/types/api/` and `/types/models/` for better structure.**

   > Organize shared/types/ into two categories:

   - `api/`: for API response/request shape
   - `models/`: for local UI models, enums, and constants
     This improves clarity and helps scale alongside backend changes.

3. **Modularize router into role-based route files.**

   > Refactor router/index.ts by extracting:

   - `student.routes.ts`
   - `lecturer.routes.ts`
     Then import and combine them in the main `index.ts`. This separation makes route management and role-based logic easier to maintain.

4. **Split `useApi()` into role-specific hooks.**

   > Create:

   - `useStudentApi()` → preconfigures with `/api/student`
   - `useLecturerApi()` → preconfigures with `/api/lecturer`
     This helps prevent accidental cross-role API calls and improves autocomplete.

5. **Review and remove redundant `index.ts` files in composables if not used.**

   > If student/composables/index.ts or similar files are not actually used for re-exports, consider removing them for simplicity.

6. **Prefix shared UI components with context-aware names if reused across roles.**

   > Example: rename NotificationPopover.vue → SharedNotificationPopover.vue to improve discoverability and intention clarity in IDEs.
