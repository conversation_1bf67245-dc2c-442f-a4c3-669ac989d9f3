<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Student Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .offline-container {
            background: white;
            border-radius: 16px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            margin: 2rem;
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .offline-message {
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .offline-features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }

        .features-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .features-list {
            list-style: none;
            text-align: left;
        }

        .features-list li {
            padding: 0.5rem 0;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .features-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
        }

        .connection-status {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-offline {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .status-online {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        @media (max-width: 640px) {
            .offline-container {
                padding: 2rem;
                margin: 1rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-message {
                font-size: 1rem;
            }
        }

        /* Animation */
        .offline-container {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">
            📡
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry - some features are still available offline!
        </p>

        <div class="connection-status status-offline" id="connectionStatus">
            🔴 No internet connection
        </div>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="tryReconnect()">
                Try Again
            </button>
            <a href="/" class="btn btn-secondary">
                Go to Dashboard
            </a>
        </div>

        <div class="offline-features">
            <h3 class="features-title">Available Offline:</h3>
            <ul class="features-list">
                <li>View cached timetable</li>
                <li>Access recent grades</li>
                <li>Browse course information</li>
                <li>View profile information</li>
                <li>Read cached announcements</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.textContent = '🟢 Back online!';
                statusElement.className = 'connection-status status-online';
                
                // Auto-redirect after a short delay
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                statusElement.textContent = '🔴 No internet connection';
                statusElement.className = 'connection-status status-offline';
            }
        }

        // Try to reconnect
        function tryReconnect() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                window.location.href = '/';
            } else {
                // Show feedback that we're still offline
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Still offline...';
                button.disabled = true;
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.disabled = false;
                }, 2000);
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(() => {
            // Try to fetch a small resource to verify actual connectivity
            fetch('/manifest.json', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                if (!navigator.onLine) {
                    // Force online status update
                    navigator.onLine = true;
                    updateConnectionStatus();
                }
            })
            .catch(() => {
                // Connection is actually down
                if (navigator.onLine) {
                    navigator.onLine = false;
                    updateConnectionStatus();
                }
            });
        }, 5000);

        // Service Worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready');
                
                // Listen for updates
                registration.addEventListener('updatefound', () => {
                    console.log('New service worker version available');
                });
            });
        }
    </script>
</body>
</html>
